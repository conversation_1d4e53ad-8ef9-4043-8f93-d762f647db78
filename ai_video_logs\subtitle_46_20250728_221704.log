2025-07-28 22:17:04,165 - INFO - ========== 字幕 #46 处理开始 ==========
2025-07-28 22:17:04,165 - INFO - 字幕内容: 一碗爆臭粉下去，让她从此臭气熏天，再也无颜见人。
2025-07-28 22:17:04,165 - INFO - 字幕序号: [2122, 2123]
2025-07-28 22:17:04,165 - INFO - 音频文件详情:
2025-07-28 22:17:04,165 - INFO -   - 路径: output\46.wav
2025-07-28 22:17:04,165 - INFO -   - 时长: 3.56秒
2025-07-28 22:17:04,165 - INFO -   - 验证音频时长: 3.56秒
2025-07-28 22:17:04,165 - INFO - 字幕时间戳信息:
2025-07-28 22:17:04,165 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-28 22:17:04,166 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-28 22:17:04,166 - INFO -   - 根据生成的音频时长(3.56秒)已调整字幕时间戳
2025-07-28 22:17:04,166 - INFO - ========== 开始为字幕 #46 生成 6 套场景方案 ==========
2025-07-28 22:17:04,166 - INFO - 开始查找字幕序号 [2122, 2123] 对应的场景，共有 2710 个场景可选
2025-07-28 22:17:04,166 - INFO - 找到related_overlap场景: scene_id=1987, 字幕#2122
2025-07-28 22:17:04,166 - INFO - 找到related_overlap场景: scene_id=1988, 字幕#2122
2025-07-28 22:17:04,167 - INFO - 字幕 #2122 找到 2 个overlap场景, 0 个between场景
2025-07-28 22:17:04,167 - INFO - 字幕 #2123 找到 0 个overlap场景, 0 个between场景
2025-07-28 22:17:04,167 - WARNING - 字幕 #2123 没有找到任何匹配场景!
2025-07-28 22:17:04,167 - INFO - 共收集 2 个未使用的overlap场景和 0 个未使用的between场景
2025-07-28 22:17:04,167 - INFO - 开始生成方案 #1
2025-07-28 22:17:04,167 - INFO - 方案 #1: 为字幕#2122选择初始化overlap场景id=1988
2025-07-28 22:17:04,167 - INFO - 方案 #1: 初始选择后，当前总时长=2.92秒
2025-07-28 22:17:04,167 - INFO - 方案 #1: 额外添加overlap场景id=1987, 当前总时长=5.08秒
2025-07-28 22:17:04,167 - INFO - 方案 #1: 额外between选择后，当前总时长=5.08秒
2025-07-28 22:17:04,167 - INFO - 方案 #1: 场景总时长(5.08秒)大于音频时长(3.56秒)，需要裁剪
2025-07-28 22:17:04,167 - INFO - 调整前总时长: 5.08秒, 目标时长: 3.56秒
2025-07-28 22:17:04,167 - INFO - 需要裁剪 1.52秒
2025-07-28 22:17:04,167 - INFO - 裁剪最长场景ID=1988：从2.92秒裁剪至1.40秒
2025-07-28 22:17:04,167 - INFO - 调整后总时长: 3.56秒，与目标时长差异: 0.00秒
2025-07-28 22:17:04,167 - INFO - 方案 #1 调整/填充后最终总时长: 3.56秒
2025-07-28 22:17:04,167 - INFO - 方案 #1 添加到方案列表
2025-07-28 22:17:04,167 - INFO - 开始生成方案 #2
2025-07-28 22:17:04,167 - WARNING - 方案 #2: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:17:04,167 - INFO - 开始生成方案 #3
2025-07-28 22:17:04,167 - WARNING - 方案 #3: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:17:04,167 - INFO - 开始生成方案 #4
2025-07-28 22:17:04,167 - WARNING - 方案 #4: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:17:04,167 - INFO - 开始生成方案 #5
2025-07-28 22:17:04,167 - WARNING - 方案 #5: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:17:04,167 - INFO - 开始生成方案 #6
2025-07-28 22:17:04,167 - WARNING - 方案 #6: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:17:04,167 - INFO - ========== 字幕 #46 的 1 套有效场景方案生成完成 ==========
2025-07-28 22:17:04,167 - INFO - 
----- 处理字幕 #46 的方案 #1 -----
2025-07-28 22:17:04,167 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\46_1.mp4
2025-07-28 22:17:04,168 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp_hdyw2z1
2025-07-28 22:17:04,168 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1988.mp4 (确认存在: True)
2025-07-28 22:17:04,168 - INFO - 添加场景ID=1988，时长=2.92秒，累计时长=2.92秒
2025-07-28 22:17:04,168 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1987.mp4 (确认存在: True)
2025-07-28 22:17:04,168 - INFO - 添加场景ID=1987，时长=2.16秒，累计时长=5.08秒
2025-07-28 22:17:04,168 - INFO - 准备合并 2 个场景文件，总时长约 5.08秒
2025-07-28 22:17:04,168 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1988.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1987.mp4'

2025-07-28 22:17:04,168 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp_hdyw2z1\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp_hdyw2z1\temp_combined.mp4
2025-07-28 22:17:04,310 - INFO - 合并后的视频时长: 5.13秒，目标音频时长: 3.56秒
2025-07-28 22:17:04,310 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp_hdyw2z1\temp_combined.mp4 -ss 0 -to 3.562 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\46_1.mp4
2025-07-28 22:17:04,597 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 22:17:04,597 - INFO - 目标音频时长: 3.56秒
2025-07-28 22:17:04,597 - INFO - 实际视频时长: 3.62秒
2025-07-28 22:17:04,597 - INFO - 时长差异: 0.06秒 (1.71%)
2025-07-28 22:17:04,597 - INFO - ==========================================
2025-07-28 22:17:04,597 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 22:17:04,597 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\46_1.mp4
2025-07-28 22:17:04,597 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp_hdyw2z1
2025-07-28 22:17:04,649 - INFO - 方案 #1 处理完成:
2025-07-28 22:17:04,649 - INFO -   - 音频时长: 3.56秒
2025-07-28 22:17:04,649 - INFO -   - 视频时长: 3.62秒
2025-07-28 22:17:04,649 - INFO -   - 时长差异: 0.06秒 (1.71%)
2025-07-28 22:17:04,649 - INFO - 
字幕 #46 处理完成，成功生成 1/1 套方案
2025-07-28 22:17:04,649 - INFO - 生成的视频文件:
2025-07-28 22:17:04,649 - INFO -   1. F:/github/aicut_auto/newcut_ai\46_1.mp4
2025-07-28 22:17:04,649 - INFO - ========== 字幕 #46 处理结束 ==========

