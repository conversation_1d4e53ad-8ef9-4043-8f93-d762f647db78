2025-07-28 22:16:40,335 - INFO - ========== 字幕 #14 处理开始 ==========
2025-07-28 22:16:40,335 - INFO - 字幕内容: 不久后，侯府决定举办宴会，正式将寻回的真千金介绍给京城权贵。
2025-07-28 22:16:40,335 - INFO - 字幕序号: [351]
2025-07-28 22:16:40,336 - INFO - 音频文件详情:
2025-07-28 22:16:40,336 - INFO -   - 路径: output\14.wav
2025-07-28 22:16:40,336 - INFO -   - 时长: 5.59秒
2025-07-28 22:16:40,336 - INFO -   - 验证音频时长: 5.59秒
2025-07-28 22:16:40,336 - INFO - 字幕时间戳信息:
2025-07-28 22:16:40,336 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-28 22:16:40,336 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-28 22:16:40,336 - INFO -   - 根据生成的音频时长(5.59秒)已调整字幕时间戳
2025-07-28 22:16:40,336 - INFO - ========== 开始为字幕 #14 生成 6 套场景方案 ==========
2025-07-28 22:16:40,336 - INFO - 开始查找字幕序号 [351] 对应的场景，共有 2710 个场景可选
2025-07-28 22:16:40,336 - INFO - 找到related_overlap场景: scene_id=385, 字幕#351
2025-07-28 22:16:40,336 - INFO - 找到related_overlap场景: scene_id=386, 字幕#351
2025-07-28 22:16:40,337 - INFO - 字幕 #351 找到 2 个overlap场景, 0 个between场景
2025-07-28 22:16:40,337 - INFO - 共收集 2 个未使用的overlap场景和 0 个未使用的between场景
2025-07-28 22:16:40,337 - INFO - 开始生成方案 #1
2025-07-28 22:16:40,337 - INFO - 方案 #1: 为字幕#351选择初始化overlap场景id=385
2025-07-28 22:16:40,337 - INFO - 方案 #1: 初始选择后，当前总时长=1.88秒
2025-07-28 22:16:40,337 - INFO - 方案 #1: 额外添加overlap场景id=386, 当前总时长=3.92秒
2025-07-28 22:16:40,337 - INFO - 方案 #1: 额外between选择后，当前总时长=3.92秒
2025-07-28 22:16:40,337 - INFO - 方案 #1: 场景总时长(3.92秒)小于音频时长(5.59秒)，需要延伸填充
2025-07-28 22:16:40,337 - INFO - 方案 #1: 最后一个场景ID: 386
2025-07-28 22:16:40,337 - INFO - 方案 #1: 找到最后一个场景在原始列表中的索引: 385
2025-07-28 22:16:40,337 - INFO - 方案 #1: 需要填充时长: 1.68秒
2025-07-28 22:16:40,337 - INFO - 方案 #1: 追加场景 scene_id=387 (完整时长 1.64秒)
2025-07-28 22:16:40,337 - INFO - 方案 #1: 追加场景 scene_id=388 (裁剪至 0.04秒)
2025-07-28 22:16:40,337 - INFO - 方案 #1: 成功填充至目标时长
2025-07-28 22:16:40,337 - INFO - 方案 #1 调整/填充后最终总时长: 5.59秒
2025-07-28 22:16:40,337 - INFO - 方案 #1 添加到方案列表
2025-07-28 22:16:40,337 - INFO - 开始生成方案 #2
2025-07-28 22:16:40,337 - WARNING - 方案 #2: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:40,337 - INFO - 开始生成方案 #3
2025-07-28 22:16:40,337 - WARNING - 方案 #3: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:40,337 - INFO - 开始生成方案 #4
2025-07-28 22:16:40,337 - WARNING - 方案 #4: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:40,337 - INFO - 开始生成方案 #5
2025-07-28 22:16:40,337 - WARNING - 方案 #5: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:40,337 - INFO - 开始生成方案 #6
2025-07-28 22:16:40,337 - WARNING - 方案 #6: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:40,338 - INFO - ========== 字幕 #14 的 1 套有效场景方案生成完成 ==========
2025-07-28 22:16:40,338 - INFO - 
----- 处理字幕 #14 的方案 #1 -----
2025-07-28 22:16:40,338 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\14_1.mp4
2025-07-28 22:16:40,338 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmppzzzc7dy
2025-07-28 22:16:40,338 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\385.mp4 (确认存在: True)
2025-07-28 22:16:40,338 - INFO - 添加场景ID=385，时长=1.88秒，累计时长=1.88秒
2025-07-28 22:16:40,338 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\386.mp4 (确认存在: True)
2025-07-28 22:16:40,339 - INFO - 添加场景ID=386，时长=2.04秒，累计时长=3.92秒
2025-07-28 22:16:40,339 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\387.mp4 (确认存在: True)
2025-07-28 22:16:40,339 - INFO - 添加场景ID=387，时长=1.64秒，累计时长=5.56秒
2025-07-28 22:16:40,339 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\388.mp4 (确认存在: True)
2025-07-28 22:16:40,339 - INFO - 添加场景ID=388，时长=2.88秒，累计时长=8.44秒
2025-07-28 22:16:40,339 - INFO - 场景总时长(8.44秒)已达到音频时长(5.59秒)的1.5倍，停止添加场景
2025-07-28 22:16:40,339 - INFO - 准备合并 4 个场景文件，总时长约 8.44秒
2025-07-28 22:16:40,339 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/385.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/386.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/387.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/388.mp4'

2025-07-28 22:16:40,339 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmppzzzc7dy\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmppzzzc7dy\temp_combined.mp4
2025-07-28 22:16:40,533 - INFO - 合并后的视频时长: 8.53秒，目标音频时长: 5.59秒
2025-07-28 22:16:40,533 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmppzzzc7dy\temp_combined.mp4 -ss 0 -to 5.595 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\14_1.mp4
2025-07-28 22:16:40,876 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 22:16:40,876 - INFO - 目标音频时长: 5.59秒
2025-07-28 22:16:40,876 - INFO - 实际视频时长: 5.62秒
2025-07-28 22:16:40,876 - INFO - 时长差异: 0.03秒 (0.50%)
2025-07-28 22:16:40,876 - INFO - ==========================================
2025-07-28 22:16:40,876 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 22:16:40,876 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\14_1.mp4
2025-07-28 22:16:40,877 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmppzzzc7dy
2025-07-28 22:16:40,926 - INFO - 方案 #1 处理完成:
2025-07-28 22:16:40,926 - INFO -   - 音频时长: 5.59秒
2025-07-28 22:16:40,926 - INFO -   - 视频时长: 5.62秒
2025-07-28 22:16:40,926 - INFO -   - 时长差异: 0.03秒 (0.50%)
2025-07-28 22:16:40,926 - INFO - 
字幕 #14 处理完成，成功生成 1/1 套方案
2025-07-28 22:16:40,926 - INFO - 生成的视频文件:
2025-07-28 22:16:40,926 - INFO -   1. F:/github/aicut_auto/newcut_ai\14_1.mp4
2025-07-28 22:16:40,926 - INFO - ========== 字幕 #14 处理结束 ==========

