2025-07-28 22:16:41,509 - INFO - ========== 字幕 #16 处理开始 ==========
2025-07-28 22:16:41,509 - INFO - 字幕内容: 她一边假惺惺地讨好着二哥，一边在心里盘算着如何利用他接近镇国公世子。
2025-07-28 22:16:41,509 - INFO - 字幕序号: [490, 493]
2025-07-28 22:16:41,509 - INFO - 音频文件详情:
2025-07-28 22:16:41,509 - INFO -   - 路径: output\16.wav
2025-07-28 22:16:41,509 - INFO -   - 时长: 4.54秒
2025-07-28 22:16:41,510 - INFO -   - 验证音频时长: 4.54秒
2025-07-28 22:16:41,510 - INFO - 字幕时间戳信息:
2025-07-28 22:16:41,510 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-28 22:16:41,510 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-28 22:16:41,510 - INFO -   - 根据生成的音频时长(4.54秒)已调整字幕时间戳
2025-07-28 22:16:41,510 - INFO - ========== 开始为字幕 #16 生成 6 套场景方案 ==========
2025-07-28 22:16:41,510 - INFO - 开始查找字幕序号 [490, 493] 对应的场景，共有 2710 个场景可选
2025-07-28 22:16:41,510 - INFO - 找到related_overlap场景: scene_id=509, 字幕#490
2025-07-28 22:16:41,510 - INFO - 找到related_overlap场景: scene_id=510, 字幕#490
2025-07-28 22:16:41,510 - INFO - 找到related_overlap场景: scene_id=512, 字幕#493
2025-07-28 22:16:41,511 - INFO - 字幕 #490 找到 2 个overlap场景, 0 个between场景
2025-07-28 22:16:41,511 - INFO - 字幕 #493 找到 1 个overlap场景, 0 个between场景
2025-07-28 22:16:41,511 - INFO - 共收集 3 个未使用的overlap场景和 0 个未使用的between场景
2025-07-28 22:16:41,511 - INFO - 开始生成方案 #1
2025-07-28 22:16:41,511 - INFO - 方案 #1: 为字幕#490选择初始化overlap场景id=509
2025-07-28 22:16:41,511 - INFO - 方案 #1: 为字幕#493选择初始化overlap场景id=512
2025-07-28 22:16:41,511 - INFO - 方案 #1: 初始选择后，当前总时长=2.64秒
2025-07-28 22:16:41,511 - INFO - 方案 #1: 额外添加overlap场景id=510, 当前总时长=5.36秒
2025-07-28 22:16:41,511 - INFO - 方案 #1: 额外between选择后，当前总时长=5.36秒
2025-07-28 22:16:41,511 - INFO - 方案 #1: 场景总时长(5.36秒)大于音频时长(4.54秒)，需要裁剪
2025-07-28 22:16:41,511 - INFO - 调整前总时长: 5.36秒, 目标时长: 4.54秒
2025-07-28 22:16:41,511 - INFO - 需要裁剪 0.82秒
2025-07-28 22:16:41,511 - INFO - 裁剪最长场景ID=510：从2.72秒裁剪至1.90秒
2025-07-28 22:16:41,511 - INFO - 调整后总时长: 4.54秒，与目标时长差异: 0.00秒
2025-07-28 22:16:41,512 - INFO - 方案 #1 调整/填充后最终总时长: 4.54秒
2025-07-28 22:16:41,512 - INFO - 方案 #1 添加到方案列表
2025-07-28 22:16:41,512 - INFO - 开始生成方案 #2
2025-07-28 22:16:41,512 - WARNING - 方案 #2: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:41,512 - INFO - 开始生成方案 #3
2025-07-28 22:16:41,512 - WARNING - 方案 #3: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:41,512 - INFO - 开始生成方案 #4
2025-07-28 22:16:41,512 - WARNING - 方案 #4: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:41,512 - INFO - 开始生成方案 #5
2025-07-28 22:16:41,512 - WARNING - 方案 #5: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:41,512 - INFO - 开始生成方案 #6
2025-07-28 22:16:41,512 - WARNING - 方案 #6: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:41,512 - INFO - ========== 字幕 #16 的 1 套有效场景方案生成完成 ==========
2025-07-28 22:16:41,512 - INFO - 
----- 处理字幕 #16 的方案 #1 -----
2025-07-28 22:16:41,512 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\16_1.mp4
2025-07-28 22:16:41,512 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp35ciggq4
2025-07-28 22:16:41,513 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\509.mp4 (确认存在: True)
2025-07-28 22:16:41,513 - INFO - 添加场景ID=509，时长=1.36秒，累计时长=1.36秒
2025-07-28 22:16:41,513 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\512.mp4 (确认存在: True)
2025-07-28 22:16:41,513 - INFO - 添加场景ID=512，时长=1.28秒，累计时长=2.64秒
2025-07-28 22:16:41,513 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\510.mp4 (确认存在: True)
2025-07-28 22:16:41,513 - INFO - 添加场景ID=510，时长=2.72秒，累计时长=5.36秒
2025-07-28 22:16:41,513 - INFO - 准备合并 3 个场景文件，总时长约 5.36秒
2025-07-28 22:16:41,513 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/509.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/512.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/510.mp4'

2025-07-28 22:16:41,513 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp35ciggq4\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp35ciggq4\temp_combined.mp4
2025-07-28 22:16:41,682 - INFO - 合并后的视频时长: 5.43秒，目标音频时长: 4.54秒
2025-07-28 22:16:41,682 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp35ciggq4\temp_combined.mp4 -ss 0 -to 4.535 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\16_1.mp4
2025-07-28 22:16:42,036 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 22:16:42,036 - INFO - 目标音频时长: 4.54秒
2025-07-28 22:16:42,036 - INFO - 实际视频时长: 4.58秒
2025-07-28 22:16:42,036 - INFO - 时长差异: 0.05秒 (1.06%)
2025-07-28 22:16:42,036 - INFO - ==========================================
2025-07-28 22:16:42,036 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 22:16:42,036 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\16_1.mp4
2025-07-28 22:16:42,037 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp35ciggq4
2025-07-28 22:16:42,083 - INFO - 方案 #1 处理完成:
2025-07-28 22:16:42,083 - INFO -   - 音频时长: 4.54秒
2025-07-28 22:16:42,083 - INFO -   - 视频时长: 4.58秒
2025-07-28 22:16:42,083 - INFO -   - 时长差异: 0.05秒 (1.06%)
2025-07-28 22:16:42,083 - INFO - 
字幕 #16 处理完成，成功生成 1/1 套方案
2025-07-28 22:16:42,083 - INFO - 生成的视频文件:
2025-07-28 22:16:42,083 - INFO -   1. F:/github/aicut_auto/newcut_ai\16_1.mp4
2025-07-28 22:16:42,083 - INFO - ========== 字幕 #16 处理结束 ==========

