2025-07-28 22:17:09,349 - INFO - ========== 字幕 #54 处理开始 ==========
2025-07-28 22:17:09,349 - INFO - 字幕内容: 老祖灵机一动，决定让心机女见识一下，什么是真正的神仙手段。
2025-07-28 22:17:09,349 - INFO - 字幕序号: [2641, 2643]
2025-07-28 22:17:09,349 - INFO - 音频文件详情:
2025-07-28 22:17:09,349 - INFO -   - 路径: output\54.wav
2025-07-28 22:17:09,349 - INFO -   - 时长: 4.33秒
2025-07-28 22:17:09,349 - INFO -   - 验证音频时长: 4.33秒
2025-07-28 22:17:09,349 - INFO - 字幕时间戳信息:
2025-07-28 22:17:09,349 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-28 22:17:09,349 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-28 22:17:09,349 - INFO -   - 根据生成的音频时长(4.33秒)已调整字幕时间戳
2025-07-28 22:17:09,350 - INFO - ========== 开始为字幕 #54 生成 6 套场景方案 ==========
2025-07-28 22:17:09,350 - INFO - 开始查找字幕序号 [2641, 2643] 对应的场景，共有 2710 个场景可选
2025-07-28 22:17:09,350 - INFO - 找到related_overlap场景: scene_id=2516, 字幕#2641
2025-07-28 22:17:09,350 - INFO - 找到related_overlap场景: scene_id=2517, 字幕#2641
2025-07-28 22:17:09,350 - INFO - 找到related_overlap场景: scene_id=2518, 字幕#2643
2025-07-28 22:17:09,351 - INFO - 字幕 #2641 找到 2 个overlap场景, 0 个between场景
2025-07-28 22:17:09,351 - INFO - 字幕 #2643 找到 1 个overlap场景, 0 个between场景
2025-07-28 22:17:09,351 - INFO - 共收集 3 个未使用的overlap场景和 0 个未使用的between场景
2025-07-28 22:17:09,351 - INFO - 开始生成方案 #1
2025-07-28 22:17:09,351 - INFO - 方案 #1: 为字幕#2641选择初始化overlap场景id=2516
2025-07-28 22:17:09,351 - INFO - 方案 #1: 为字幕#2643选择初始化overlap场景id=2518
2025-07-28 22:17:09,351 - INFO - 方案 #1: 初始选择后，当前总时长=6.00秒
2025-07-28 22:17:09,351 - INFO - 方案 #1: 额外between选择后，当前总时长=6.00秒
2025-07-28 22:17:09,351 - INFO - 方案 #1: 场景总时长(6.00秒)大于音频时长(4.33秒)，需要裁剪
2025-07-28 22:17:09,351 - INFO - 调整前总时长: 6.00秒, 目标时长: 4.33秒
2025-07-28 22:17:09,351 - INFO - 需要裁剪 1.67秒
2025-07-28 22:17:09,351 - INFO - 裁剪最长场景ID=2516：从3.96秒裁剪至2.29秒
2025-07-28 22:17:09,351 - INFO - 调整后总时长: 4.33秒，与目标时长差异: 0.00秒
2025-07-28 22:17:09,351 - INFO - 方案 #1 调整/填充后最终总时长: 4.33秒
2025-07-28 22:17:09,351 - INFO - 方案 #1 添加到方案列表
2025-07-28 22:17:09,351 - INFO - 开始生成方案 #2
2025-07-28 22:17:09,351 - INFO - 方案 #2: 为字幕#2641选择初始化overlap场景id=2517
2025-07-28 22:17:09,351 - INFO - 方案 #2: 初始选择后，当前总时长=2.72秒
2025-07-28 22:17:09,351 - INFO - 方案 #2: 额外between选择后，当前总时长=2.72秒
2025-07-28 22:17:09,351 - INFO - 方案 #2: 场景总时长(2.72秒)小于音频时长(4.33秒)，需要延伸填充
2025-07-28 22:17:09,351 - INFO - 方案 #2: 最后一个场景ID: 2517
2025-07-28 22:17:09,351 - INFO - 方案 #2: 找到最后一个场景在原始列表中的索引: 2516
2025-07-28 22:17:09,351 - INFO - 方案 #2: 需要填充时长: 1.61秒
2025-07-28 22:17:09,351 - INFO - 方案 #2: 跳过已使用的场景: scene_id=2518
2025-07-28 22:17:09,351 - INFO - 方案 #2: 追加场景 scene_id=2519 (裁剪至 1.61秒)
2025-07-28 22:17:09,351 - INFO - 方案 #2: 成功填充至目标时长
2025-07-28 22:17:09,351 - INFO - 方案 #2 调整/填充后最终总时长: 4.33秒
2025-07-28 22:17:09,351 - INFO - 方案 #2 添加到方案列表
2025-07-28 22:17:09,351 - INFO - 开始生成方案 #3
2025-07-28 22:17:09,351 - WARNING - 方案 #3: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:17:09,351 - INFO - 开始生成方案 #4
2025-07-28 22:17:09,351 - WARNING - 方案 #4: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:17:09,351 - INFO - 开始生成方案 #5
2025-07-28 22:17:09,351 - WARNING - 方案 #5: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:17:09,351 - INFO - 开始生成方案 #6
2025-07-28 22:17:09,351 - WARNING - 方案 #6: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:17:09,351 - INFO - ========== 字幕 #54 的 2 套有效场景方案生成完成 ==========
2025-07-28 22:17:09,351 - INFO - 
----- 处理字幕 #54 的方案 #1 -----
2025-07-28 22:17:09,351 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\54_1.mp4
2025-07-28 22:17:09,352 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpqfqqhu2q
2025-07-28 22:17:09,352 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2516.mp4 (确认存在: True)
2025-07-28 22:17:09,352 - INFO - 添加场景ID=2516，时长=3.96秒，累计时长=3.96秒
2025-07-28 22:17:09,352 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2518.mp4 (确认存在: True)
2025-07-28 22:17:09,352 - INFO - 添加场景ID=2518，时长=2.04秒，累计时长=6.00秒
2025-07-28 22:17:09,352 - INFO - 准备合并 2 个场景文件，总时长约 6.00秒
2025-07-28 22:17:09,352 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2516.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2518.mp4'

2025-07-28 22:17:09,353 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpqfqqhu2q\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpqfqqhu2q\temp_combined.mp4
2025-07-28 22:17:09,499 - INFO - 合并后的视频时长: 6.05秒，目标音频时长: 4.33秒
2025-07-28 22:17:09,499 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpqfqqhu2q\temp_combined.mp4 -ss 0 -to 4.332 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\54_1.mp4
2025-07-28 22:17:09,792 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 22:17:09,792 - INFO - 目标音频时长: 4.33秒
2025-07-28 22:17:09,792 - INFO - 实际视频时长: 4.38秒
2025-07-28 22:17:09,792 - INFO - 时长差异: 0.05秒 (1.18%)
2025-07-28 22:17:09,792 - INFO - ==========================================
2025-07-28 22:17:09,792 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 22:17:09,792 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\54_1.mp4
2025-07-28 22:17:09,793 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpqfqqhu2q
2025-07-28 22:17:09,838 - INFO - 方案 #1 处理完成:
2025-07-28 22:17:09,838 - INFO -   - 音频时长: 4.33秒
2025-07-28 22:17:09,838 - INFO -   - 视频时长: 4.38秒
2025-07-28 22:17:09,838 - INFO -   - 时长差异: 0.05秒 (1.18%)
2025-07-28 22:17:09,838 - INFO - 
----- 处理字幕 #54 的方案 #2 -----
2025-07-28 22:17:09,838 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\54_2.mp4
2025-07-28 22:17:09,839 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpkuzv85bw
2025-07-28 22:17:09,839 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2517.mp4 (确认存在: True)
2025-07-28 22:17:09,839 - INFO - 添加场景ID=2517，时长=2.72秒，累计时长=2.72秒
2025-07-28 22:17:09,839 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2519.mp4 (确认存在: True)
2025-07-28 22:17:09,839 - INFO - 添加场景ID=2519，时长=5.44秒，累计时长=8.16秒
2025-07-28 22:17:09,839 - INFO - 场景总时长(8.16秒)已达到音频时长(4.33秒)的1.5倍，停止添加场景
2025-07-28 22:17:09,840 - INFO - 准备合并 2 个场景文件，总时长约 8.16秒
2025-07-28 22:17:09,840 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2517.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2519.mp4'

2025-07-28 22:17:09,840 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpkuzv85bw\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpkuzv85bw\temp_combined.mp4
2025-07-28 22:17:09,978 - INFO - 合并后的视频时长: 8.21秒，目标音频时长: 4.33秒
2025-07-28 22:17:09,979 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpkuzv85bw\temp_combined.mp4 -ss 0 -to 4.332 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\54_2.mp4
2025-07-28 22:17:10,277 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 22:17:10,277 - INFO - 目标音频时长: 4.33秒
2025-07-28 22:17:10,277 - INFO - 实际视频时长: 4.38秒
2025-07-28 22:17:10,277 - INFO - 时长差异: 0.05秒 (1.18%)
2025-07-28 22:17:10,277 - INFO - ==========================================
2025-07-28 22:17:10,277 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 22:17:10,277 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\54_2.mp4
2025-07-28 22:17:10,278 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpkuzv85bw
2025-07-28 22:17:10,323 - INFO - 方案 #2 处理完成:
2025-07-28 22:17:10,323 - INFO -   - 音频时长: 4.33秒
2025-07-28 22:17:10,323 - INFO -   - 视频时长: 4.38秒
2025-07-28 22:17:10,323 - INFO -   - 时长差异: 0.05秒 (1.18%)
2025-07-28 22:17:10,324 - INFO - 
字幕 #54 处理完成，成功生成 2/2 套方案
2025-07-28 22:17:10,324 - INFO - 生成的视频文件:
2025-07-28 22:17:10,324 - INFO -   1. F:/github/aicut_auto/newcut_ai\54_1.mp4
2025-07-28 22:17:10,324 - INFO -   2. F:/github/aicut_auto/newcut_ai\54_2.mp4
2025-07-28 22:17:10,324 - INFO - ========== 字幕 #54 处理结束 ==========

