2025-07-28 22:16:55,315 - INFO - ========== 字幕 #34 处理开始 ==========
2025-07-28 22:16:55,315 - INFO - 字幕内容: 心机女做着发财的美梦，以为整个侯府连同那九千万两都将是她的囊中之物。
2025-07-28 22:16:55,315 - INFO - 字幕序号: [1487, 1491]
2025-07-28 22:16:55,315 - INFO - 音频文件详情:
2025-07-28 22:16:55,315 - INFO -   - 路径: output\34.wav
2025-07-28 22:16:55,315 - INFO -   - 时长: 5.38秒
2025-07-28 22:16:55,316 - INFO -   - 验证音频时长: 5.38秒
2025-07-28 22:16:55,316 - INFO - 字幕时间戳信息:
2025-07-28 22:16:55,316 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-28 22:16:55,316 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-28 22:16:55,316 - INFO -   - 根据生成的音频时长(5.38秒)已调整字幕时间戳
2025-07-28 22:16:55,316 - INFO - ========== 开始为字幕 #34 生成 6 套场景方案 ==========
2025-07-28 22:16:55,316 - INFO - 开始查找字幕序号 [1487, 1491] 对应的场景，共有 2710 个场景可选
2025-07-28 22:16:55,316 - INFO - 找到related_overlap场景: scene_id=1406, 字幕#1487
2025-07-28 22:16:55,317 - INFO - 字幕 #1487 找到 1 个overlap场景, 0 个between场景
2025-07-28 22:16:55,317 - INFO - 字幕 #1491 找到 0 个overlap场景, 0 个between场景
2025-07-28 22:16:55,317 - WARNING - 字幕 #1491 没有找到任何匹配场景!
2025-07-28 22:16:55,317 - INFO - 共收集 1 个未使用的overlap场景和 0 个未使用的between场景
2025-07-28 22:16:55,317 - INFO - 开始生成方案 #1
2025-07-28 22:16:55,317 - INFO - 方案 #1: 为字幕#1487选择初始化overlap场景id=1406
2025-07-28 22:16:55,317 - INFO - 方案 #1: 初始选择后，当前总时长=9.04秒
2025-07-28 22:16:55,317 - INFO - 方案 #1: 额外between选择后，当前总时长=9.04秒
2025-07-28 22:16:55,317 - INFO - 方案 #1: 场景总时长(9.04秒)大于音频时长(5.38秒)，需要裁剪
2025-07-28 22:16:55,317 - INFO - 调整前总时长: 9.04秒, 目标时长: 5.38秒
2025-07-28 22:16:55,317 - INFO - 需要裁剪 3.66秒
2025-07-28 22:16:55,317 - INFO - 裁剪最长场景ID=1406：从9.04秒裁剪至5.38秒
2025-07-28 22:16:55,317 - INFO - 调整后总时长: 5.38秒，与目标时长差异: 0.00秒
2025-07-28 22:16:55,317 - INFO - 方案 #1 调整/填充后最终总时长: 5.38秒
2025-07-28 22:16:55,317 - INFO - 方案 #1 添加到方案列表
2025-07-28 22:16:55,317 - INFO - 开始生成方案 #2
2025-07-28 22:16:55,317 - WARNING - 方案 #2: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:55,317 - INFO - 开始生成方案 #3
2025-07-28 22:16:55,317 - WARNING - 方案 #3: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:55,317 - INFO - 开始生成方案 #4
2025-07-28 22:16:55,317 - WARNING - 方案 #4: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:55,317 - INFO - 开始生成方案 #5
2025-07-28 22:16:55,317 - WARNING - 方案 #5: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:55,317 - INFO - 开始生成方案 #6
2025-07-28 22:16:55,317 - WARNING - 方案 #6: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:55,317 - INFO - ========== 字幕 #34 的 1 套有效场景方案生成完成 ==========
2025-07-28 22:16:55,317 - INFO - 
----- 处理字幕 #34 的方案 #1 -----
2025-07-28 22:16:55,317 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\34_1.mp4
2025-07-28 22:16:55,318 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpcc3w62cn
2025-07-28 22:16:55,318 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1406.mp4 (确认存在: True)
2025-07-28 22:16:55,318 - INFO - 添加场景ID=1406，时长=9.04秒，累计时长=9.04秒
2025-07-28 22:16:55,318 - INFO - 场景总时长(9.04秒)已达到音频时长(5.38秒)的1.5倍，停止添加场景
2025-07-28 22:16:55,318 - INFO - 准备合并 1 个场景文件，总时长约 9.04秒
2025-07-28 22:16:55,319 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1406.mp4'

2025-07-28 22:16:55,319 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpcc3w62cn\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpcc3w62cn\temp_combined.mp4
2025-07-28 22:16:55,445 - INFO - 合并后的视频时长: 9.06秒，目标音频时长: 5.38秒
2025-07-28 22:16:55,445 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpcc3w62cn\temp_combined.mp4 -ss 0 -to 5.38 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\34_1.mp4
2025-07-28 22:16:55,761 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 22:16:55,761 - INFO - 目标音频时长: 5.38秒
2025-07-28 22:16:55,761 - INFO - 实际视频时长: 5.42秒
2025-07-28 22:16:55,761 - INFO - 时长差异: 0.04秒 (0.80%)
2025-07-28 22:16:55,761 - INFO - ==========================================
2025-07-28 22:16:55,761 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 22:16:55,761 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\34_1.mp4
2025-07-28 22:16:55,762 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpcc3w62cn
2025-07-28 22:16:55,806 - INFO - 方案 #1 处理完成:
2025-07-28 22:16:55,806 - INFO -   - 音频时长: 5.38秒
2025-07-28 22:16:55,806 - INFO -   - 视频时长: 5.42秒
2025-07-28 22:16:55,806 - INFO -   - 时长差异: 0.04秒 (0.80%)
2025-07-28 22:16:55,806 - INFO - 
字幕 #34 处理完成，成功生成 1/1 套方案
2025-07-28 22:16:55,806 - INFO - 生成的视频文件:
2025-07-28 22:16:55,806 - INFO -   1. F:/github/aicut_auto/newcut_ai\34_1.mp4
2025-07-28 22:16:55,806 - INFO - ========== 字幕 #34 处理结束 ==========

