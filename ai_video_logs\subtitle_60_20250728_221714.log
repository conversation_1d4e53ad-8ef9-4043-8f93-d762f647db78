2025-07-28 22:17:14,866 - INFO - ========== 字幕 #60 处理开始 ==========
2025-07-28 22:17:14,866 - INFO - 字幕内容: 可此时，无论真相如何，侯府众人早已认定，这个有勇有谋的女孩，才是他们真正的家人。
2025-07-28 22:17:14,866 - INFO - 字幕序号: [2838, 2844]
2025-07-28 22:17:14,866 - INFO - 音频文件详情:
2025-07-28 22:17:14,866 - INFO -   - 路径: output\60.wav
2025-07-28 22:17:14,866 - INFO -   - 时长: 6.04秒
2025-07-28 22:17:14,867 - INFO -   - 验证音频时长: 6.04秒
2025-07-28 22:17:14,867 - INFO - 字幕时间戳信息:
2025-07-28 22:17:14,867 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-28 22:17:14,867 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-28 22:17:14,867 - INFO -   - 根据生成的音频时长(6.04秒)已调整字幕时间戳
2025-07-28 22:17:14,867 - INFO - ========== 开始为字幕 #60 生成 6 套场景方案 ==========
2025-07-28 22:17:14,867 - INFO - 开始查找字幕序号 [2838, 2844] 对应的场景，共有 2710 个场景可选
2025-07-28 22:17:14,868 - INFO - 找到related_overlap场景: scene_id=2661, 字幕#2838
2025-07-28 22:17:14,868 - INFO - 找到related_overlap场景: scene_id=2662, 字幕#2838
2025-07-28 22:17:14,868 - INFO - 找到related_overlap场景: scene_id=2665, 字幕#2844
2025-07-28 22:17:14,868 - INFO - 字幕 #2838 找到 2 个overlap场景, 0 个between场景
2025-07-28 22:17:14,868 - INFO - 字幕 #2844 找到 1 个overlap场景, 0 个between场景
2025-07-28 22:17:14,868 - INFO - 共收集 3 个未使用的overlap场景和 0 个未使用的between场景
2025-07-28 22:17:14,869 - INFO - 开始生成方案 #1
2025-07-28 22:17:14,869 - INFO - 方案 #1: 为字幕#2838选择初始化overlap场景id=2662
2025-07-28 22:17:14,869 - INFO - 方案 #1: 为字幕#2844选择初始化overlap场景id=2665
2025-07-28 22:17:14,869 - INFO - 方案 #1: 初始选择后，当前总时长=6.20秒
2025-07-28 22:17:14,869 - INFO - 方案 #1: 额外between选择后，当前总时长=6.20秒
2025-07-28 22:17:14,869 - INFO - 方案 #1: 场景总时长(6.20秒)大于音频时长(6.04秒)，需要裁剪
2025-07-28 22:17:14,869 - INFO - 调整前总时长: 6.20秒, 目标时长: 6.04秒
2025-07-28 22:17:14,869 - INFO - 需要裁剪 0.17秒
2025-07-28 22:17:14,869 - INFO - 裁剪最长场景ID=2662：从3.60秒裁剪至3.44秒
2025-07-28 22:17:14,869 - INFO - 调整后总时长: 6.04秒，与目标时长差异: 0.00秒
2025-07-28 22:17:14,869 - INFO - 方案 #1 调整/填充后最终总时长: 6.04秒
2025-07-28 22:17:14,869 - INFO - 方案 #1 添加到方案列表
2025-07-28 22:17:14,869 - INFO - 开始生成方案 #2
2025-07-28 22:17:14,869 - INFO - 方案 #2: 为字幕#2838选择初始化overlap场景id=2661
2025-07-28 22:17:14,869 - INFO - 方案 #2: 初始选择后，当前总时长=2.52秒
2025-07-28 22:17:14,869 - INFO - 方案 #2: 额外between选择后，当前总时长=2.52秒
2025-07-28 22:17:14,869 - INFO - 方案 #2: 场景总时长(2.52秒)小于音频时长(6.04秒)，需要延伸填充
2025-07-28 22:17:14,869 - INFO - 方案 #2: 最后一个场景ID: 2661
2025-07-28 22:17:14,869 - INFO - 方案 #2: 找到最后一个场景在原始列表中的索引: 2660
2025-07-28 22:17:14,869 - INFO - 方案 #2: 需要填充时长: 3.52秒
2025-07-28 22:17:14,869 - INFO - 方案 #2: 跳过已使用的场景: scene_id=2662
2025-07-28 22:17:14,869 - INFO - 方案 #2: 追加场景 scene_id=2663 (完整时长 2.88秒)
2025-07-28 22:17:14,869 - INFO - 方案 #2: 追加场景 scene_id=2664 (裁剪至 0.64秒)
2025-07-28 22:17:14,869 - INFO - 方案 #2: 成功填充至目标时长
2025-07-28 22:17:14,869 - INFO - 方案 #2 调整/填充后最终总时长: 6.04秒
2025-07-28 22:17:14,869 - INFO - 方案 #2 添加到方案列表
2025-07-28 22:17:14,869 - INFO - 开始生成方案 #3
2025-07-28 22:17:14,869 - WARNING - 方案 #3: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:17:14,869 - INFO - 开始生成方案 #4
2025-07-28 22:17:14,869 - WARNING - 方案 #4: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:17:14,869 - INFO - 开始生成方案 #5
2025-07-28 22:17:14,869 - WARNING - 方案 #5: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:17:14,869 - INFO - 开始生成方案 #6
2025-07-28 22:17:14,869 - WARNING - 方案 #6: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:17:14,869 - INFO - ========== 字幕 #60 的 2 套有效场景方案生成完成 ==========
2025-07-28 22:17:14,869 - INFO - 
----- 处理字幕 #60 的方案 #1 -----
2025-07-28 22:17:14,869 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\60_1.mp4
2025-07-28 22:17:14,870 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpmwc_xzu7
2025-07-28 22:17:14,870 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2662.mp4 (确认存在: True)
2025-07-28 22:17:14,870 - INFO - 添加场景ID=2662，时长=3.60秒，累计时长=3.60秒
2025-07-28 22:17:14,870 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2665.mp4 (确认存在: True)
2025-07-28 22:17:14,870 - INFO - 添加场景ID=2665，时长=2.60秒，累计时长=6.20秒
2025-07-28 22:17:14,870 - INFO - 准备合并 2 个场景文件，总时长约 6.20秒
2025-07-28 22:17:14,870 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2662.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2665.mp4'

2025-07-28 22:17:14,871 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpmwc_xzu7\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpmwc_xzu7\temp_combined.mp4
2025-07-28 22:17:15,013 - INFO - 合并后的视频时长: 6.25秒，目标音频时长: 6.04秒
2025-07-28 22:17:15,013 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpmwc_xzu7\temp_combined.mp4 -ss 0 -to 6.035 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\60_1.mp4
2025-07-28 22:17:15,373 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 22:17:15,373 - INFO - 目标音频时长: 6.04秒
2025-07-28 22:17:15,373 - INFO - 实际视频时长: 6.06秒
2025-07-28 22:17:15,373 - INFO - 时长差异: 0.03秒 (0.46%)
2025-07-28 22:17:15,373 - INFO - ==========================================
2025-07-28 22:17:15,373 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 22:17:15,373 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\60_1.mp4
2025-07-28 22:17:15,374 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpmwc_xzu7
2025-07-28 22:17:15,422 - INFO - 方案 #1 处理完成:
2025-07-28 22:17:15,422 - INFO -   - 音频时长: 6.04秒
2025-07-28 22:17:15,422 - INFO -   - 视频时长: 6.06秒
2025-07-28 22:17:15,422 - INFO -   - 时长差异: 0.03秒 (0.46%)
2025-07-28 22:17:15,422 - INFO - 
----- 处理字幕 #60 的方案 #2 -----
2025-07-28 22:17:15,422 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\60_2.mp4
2025-07-28 22:17:15,422 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpcic5ubfa
2025-07-28 22:17:15,423 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2661.mp4 (确认存在: True)
2025-07-28 22:17:15,423 - INFO - 添加场景ID=2661，时长=2.52秒，累计时长=2.52秒
2025-07-28 22:17:15,423 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2663.mp4 (确认存在: True)
2025-07-28 22:17:15,423 - INFO - 添加场景ID=2663，时长=2.88秒，累计时长=5.40秒
2025-07-28 22:17:15,423 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2664.mp4 (确认存在: True)
2025-07-28 22:17:15,423 - INFO - 添加场景ID=2664，时长=5.56秒，累计时长=10.96秒
2025-07-28 22:17:15,423 - INFO - 场景总时长(10.96秒)已达到音频时长(6.04秒)的1.5倍，停止添加场景
2025-07-28 22:17:15,423 - INFO - 准备合并 3 个场景文件，总时长约 10.96秒
2025-07-28 22:17:15,423 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2661.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2663.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2664.mp4'

2025-07-28 22:17:15,423 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpcic5ubfa\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpcic5ubfa\temp_combined.mp4
2025-07-28 22:17:15,576 - INFO - 合并后的视频时长: 11.03秒，目标音频时长: 6.04秒
2025-07-28 22:17:15,576 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpcic5ubfa\temp_combined.mp4 -ss 0 -to 6.035 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\60_2.mp4
2025-07-28 22:17:15,916 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 22:17:15,916 - INFO - 目标音频时长: 6.04秒
2025-07-28 22:17:15,916 - INFO - 实际视频时长: 6.06秒
2025-07-28 22:17:15,916 - INFO - 时长差异: 0.03秒 (0.46%)
2025-07-28 22:17:15,916 - INFO - ==========================================
2025-07-28 22:17:15,916 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 22:17:15,916 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\60_2.mp4
2025-07-28 22:17:15,917 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpcic5ubfa
2025-07-28 22:17:15,962 - INFO - 方案 #2 处理完成:
2025-07-28 22:17:15,962 - INFO -   - 音频时长: 6.04秒
2025-07-28 22:17:15,962 - INFO -   - 视频时长: 6.06秒
2025-07-28 22:17:15,962 - INFO -   - 时长差异: 0.03秒 (0.46%)
2025-07-28 22:17:15,962 - INFO - 
字幕 #60 处理完成，成功生成 2/2 套方案
2025-07-28 22:17:15,962 - INFO - 生成的视频文件:
2025-07-28 22:17:15,962 - INFO -   1. F:/github/aicut_auto/newcut_ai\60_1.mp4
2025-07-28 22:17:15,962 - INFO -   2. F:/github/aicut_auto/newcut_ai\60_2.mp4
2025-07-28 22:17:15,962 - INFO - ========== 字幕 #60 处理结束 ==========

