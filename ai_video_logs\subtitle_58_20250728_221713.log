2025-07-28 22:17:13,721 - INFO - ========== 字幕 #58 处理开始 ==========
2025-07-28 22:17:13,721 - INFO - 字幕内容: 心机女面如死灰地接下圣旨，眼神中充满了怨毒，发誓要让侯府夫人后悔。
2025-07-28 22:17:13,721 - INFO - 字幕序号: [2708, 2711]
2025-07-28 22:17:13,722 - INFO - 音频文件详情:
2025-07-28 22:17:13,722 - INFO -   - 路径: output\58.wav
2025-07-28 22:17:13,722 - INFO -   - 时长: 6.88秒
2025-07-28 22:17:13,722 - INFO -   - 验证音频时长: 6.88秒
2025-07-28 22:17:13,722 - INFO - 字幕时间戳信息:
2025-07-28 22:17:13,722 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-28 22:17:13,722 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-28 22:17:13,722 - INFO -   - 根据生成的音频时长(6.88秒)已调整字幕时间戳
2025-07-28 22:17:13,722 - INFO - ========== 开始为字幕 #58 生成 6 套场景方案 ==========
2025-07-28 22:17:13,722 - INFO - 开始查找字幕序号 [2708, 2711] 对应的场景，共有 2710 个场景可选
2025-07-28 22:17:13,723 - INFO - 找到related_overlap场景: scene_id=2575, 字幕#2708
2025-07-28 22:17:13,723 - INFO - 找到related_overlap场景: scene_id=2576, 字幕#2708
2025-07-28 22:17:13,723 - INFO - 找到related_overlap场景: scene_id=2578, 字幕#2711
2025-07-28 22:17:13,723 - INFO - 找到related_overlap场景: scene_id=2579, 字幕#2711
2025-07-28 22:17:13,724 - INFO - 字幕 #2708 找到 2 个overlap场景, 0 个between场景
2025-07-28 22:17:13,724 - INFO - 字幕 #2711 找到 2 个overlap场景, 0 个between场景
2025-07-28 22:17:13,724 - INFO - 共收集 4 个未使用的overlap场景和 0 个未使用的between场景
2025-07-28 22:17:13,724 - INFO - 开始生成方案 #1
2025-07-28 22:17:13,724 - INFO - 方案 #1: 为字幕#2708选择初始化overlap场景id=2575
2025-07-28 22:17:13,724 - INFO - 方案 #1: 为字幕#2711选择初始化overlap场景id=2578
2025-07-28 22:17:13,724 - INFO - 方案 #1: 初始选择后，当前总时长=5.24秒
2025-07-28 22:17:13,724 - INFO - 方案 #1: 额外添加overlap场景id=2579, 当前总时长=6.52秒
2025-07-28 22:17:13,724 - INFO - 方案 #1: 额外添加overlap场景id=2576, 当前总时长=12.72秒
2025-07-28 22:17:13,724 - INFO - 方案 #1: 额外between选择后，当前总时长=12.72秒
2025-07-28 22:17:13,724 - INFO - 方案 #1: 场景总时长(12.72秒)大于音频时长(6.88秒)，需要裁剪
2025-07-28 22:17:13,724 - INFO - 调整前总时长: 12.72秒, 目标时长: 6.88秒
2025-07-28 22:17:13,724 - INFO - 需要裁剪 5.84秒
2025-07-28 22:17:13,724 - INFO - 裁剪单个场景会导致时长过短，尝试裁剪多个场景
2025-07-28 22:17:13,724 - INFO - 裁剪场景ID=2576：从6.20秒裁剪至1.86秒
2025-07-28 22:17:13,724 - INFO - 裁剪场景ID=2575：从3.64秒裁剪至2.14秒
2025-07-28 22:17:13,724 - INFO - 调整后总时长: 6.88秒，与目标时长差异: 0.00秒
2025-07-28 22:17:13,724 - INFO - 方案 #1 调整/填充后最终总时长: 6.88秒
2025-07-28 22:17:13,724 - INFO - 方案 #1 添加到方案列表
2025-07-28 22:17:13,724 - INFO - 开始生成方案 #2
2025-07-28 22:17:13,724 - WARNING - 方案 #2: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:17:13,724 - INFO - 开始生成方案 #3
2025-07-28 22:17:13,724 - WARNING - 方案 #3: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:17:13,724 - INFO - 开始生成方案 #4
2025-07-28 22:17:13,724 - WARNING - 方案 #4: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:17:13,724 - INFO - 开始生成方案 #5
2025-07-28 22:17:13,724 - WARNING - 方案 #5: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:17:13,724 - INFO - 开始生成方案 #6
2025-07-28 22:17:13,724 - WARNING - 方案 #6: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:17:13,724 - INFO - ========== 字幕 #58 的 1 套有效场景方案生成完成 ==========
2025-07-28 22:17:13,724 - INFO - 
----- 处理字幕 #58 的方案 #1 -----
2025-07-28 22:17:13,724 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\58_1.mp4
2025-07-28 22:17:13,725 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp30u6s5fe
2025-07-28 22:17:13,725 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2575.mp4 (确认存在: True)
2025-07-28 22:17:13,725 - INFO - 添加场景ID=2575，时长=3.64秒，累计时长=3.64秒
2025-07-28 22:17:13,725 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2578.mp4 (确认存在: True)
2025-07-28 22:17:13,725 - INFO - 添加场景ID=2578，时长=1.60秒，累计时长=5.24秒
2025-07-28 22:17:13,725 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2579.mp4 (确认存在: True)
2025-07-28 22:17:13,725 - INFO - 添加场景ID=2579，时长=1.28秒，累计时长=6.52秒
2025-07-28 22:17:13,725 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2576.mp4 (确认存在: True)
2025-07-28 22:17:13,725 - INFO - 添加场景ID=2576，时长=6.20秒，累计时长=12.72秒
2025-07-28 22:17:13,725 - INFO - 场景总时长(12.72秒)已达到音频时长(6.88秒)的1.5倍，停止添加场景
2025-07-28 22:17:13,725 - INFO - 准备合并 4 个场景文件，总时长约 12.72秒
2025-07-28 22:17:13,726 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2575.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2578.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2579.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2576.mp4'

2025-07-28 22:17:13,726 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp30u6s5fe\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp30u6s5fe\temp_combined.mp4
2025-07-28 22:17:13,903 - INFO - 合并后的视频时长: 12.81秒，目标音频时长: 6.88秒
2025-07-28 22:17:13,905 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp30u6s5fe\temp_combined.mp4 -ss 0 -to 6.879 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\58_1.mp4
2025-07-28 22:17:14,289 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 22:17:14,289 - INFO - 目标音频时长: 6.88秒
2025-07-28 22:17:14,289 - INFO - 实际视频时长: 6.90秒
2025-07-28 22:17:14,290 - INFO - 时长差异: 0.02秒 (0.35%)
2025-07-28 22:17:14,290 - INFO - ==========================================
2025-07-28 22:17:14,290 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 22:17:14,290 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\58_1.mp4
2025-07-28 22:17:14,291 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp30u6s5fe
2025-07-28 22:17:14,337 - INFO - 方案 #1 处理完成:
2025-07-28 22:17:14,337 - INFO -   - 音频时长: 6.88秒
2025-07-28 22:17:14,337 - INFO -   - 视频时长: 6.90秒
2025-07-28 22:17:14,337 - INFO -   - 时长差异: 0.02秒 (0.35%)
2025-07-28 22:17:14,337 - INFO - 
字幕 #58 处理完成，成功生成 1/1 套方案
2025-07-28 22:17:14,337 - INFO - 生成的视频文件:
2025-07-28 22:17:14,337 - INFO -   1. F:/github/aicut_auto/newcut_ai\58_1.mp4
2025-07-28 22:17:14,337 - INFO - ========== 字幕 #58 处理结束 ==========

