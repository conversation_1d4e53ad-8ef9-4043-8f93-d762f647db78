2025-07-28 22:17:04,650 - INFO - ========== 字幕 #47 处理开始 ==========
2025-07-28 22:17:04,650 - INFO - 字幕内容: 她又设下毒计，企图构陷二哥“欺男霸女”，让其被割掉命根，幽禁一生。
2025-07-28 22:17:04,650 - INFO - 字幕序号: [2147, 2152]
2025-07-28 22:17:04,650 - INFO - 音频文件详情:
2025-07-28 22:17:04,650 - INFO -   - 路径: output\47.wav
2025-07-28 22:17:04,650 - INFO -   - 时长: 6.28秒
2025-07-28 22:17:04,650 - INFO -   - 验证音频时长: 6.28秒
2025-07-28 22:17:04,650 - INFO - 字幕时间戳信息:
2025-07-28 22:17:04,650 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-28 22:17:04,651 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-28 22:17:04,651 - INFO -   - 根据生成的音频时长(6.28秒)已调整字幕时间戳
2025-07-28 22:17:04,651 - INFO - ========== 开始为字幕 #47 生成 6 套场景方案 ==========
2025-07-28 22:17:04,651 - INFO - 开始查找字幕序号 [2147, 2152] 对应的场景，共有 2710 个场景可选
2025-07-28 22:17:04,651 - INFO - 找到related_overlap场景: scene_id=2016, 字幕#2147
2025-07-28 22:17:04,651 - INFO - 找到related_overlap场景: scene_id=2020, 字幕#2152
2025-07-28 22:17:04,651 - INFO - 找到related_overlap场景: scene_id=2021, 字幕#2152
2025-07-28 22:17:04,652 - INFO - 找到related_between场景: scene_id=2017, 字幕#2147
2025-07-28 22:17:04,652 - INFO - 字幕 #2147 找到 1 个overlap场景, 1 个between场景
2025-07-28 22:17:04,652 - INFO - 字幕 #2152 找到 2 个overlap场景, 0 个between场景
2025-07-28 22:17:04,652 - INFO - 共收集 3 个未使用的overlap场景和 1 个未使用的between场景
2025-07-28 22:17:04,652 - INFO - 开始生成方案 #1
2025-07-28 22:17:04,652 - INFO - 方案 #1: 为字幕#2147选择初始化overlap场景id=2016
2025-07-28 22:17:04,652 - INFO - 方案 #1: 为字幕#2152选择初始化overlap场景id=2020
2025-07-28 22:17:04,652 - INFO - 方案 #1: 初始选择后，当前总时长=3.88秒
2025-07-28 22:17:04,652 - INFO - 方案 #1: 额外添加overlap场景id=2021, 当前总时长=7.20秒
2025-07-28 22:17:04,652 - INFO - 方案 #1: 额外between选择后，当前总时长=7.20秒
2025-07-28 22:17:04,652 - INFO - 方案 #1: 场景总时长(7.20秒)大于音频时长(6.28秒)，需要裁剪
2025-07-28 22:17:04,652 - INFO - 调整前总时长: 7.20秒, 目标时长: 6.28秒
2025-07-28 22:17:04,652 - INFO - 需要裁剪 0.92秒
2025-07-28 22:17:04,652 - INFO - 裁剪最长场景ID=2021：从3.32秒裁剪至2.40秒
2025-07-28 22:17:04,652 - INFO - 调整后总时长: 6.28秒，与目标时长差异: 0.00秒
2025-07-28 22:17:04,652 - INFO - 方案 #1 调整/填充后最终总时长: 6.28秒
2025-07-28 22:17:04,652 - INFO - 方案 #1 添加到方案列表
2025-07-28 22:17:04,652 - INFO - 开始生成方案 #2
2025-07-28 22:17:04,652 - INFO - 方案 #2: 初始选择后，当前总时长=0.00秒
2025-07-28 22:17:04,652 - INFO - 方案 #2: 为字幕#2147选择初始化between场景id=2017
2025-07-28 22:17:04,652 - INFO - 方案 #2: 额外between选择后，当前总时长=0.60秒
2025-07-28 22:17:04,652 - INFO - 方案 #2: 场景总时长(0.60秒)小于音频时长(6.28秒)，需要延伸填充
2025-07-28 22:17:04,652 - INFO - 方案 #2: 最后一个场景ID: 2017
2025-07-28 22:17:04,652 - INFO - 方案 #2: 找到最后一个场景在原始列表中的索引: 2016
2025-07-28 22:17:04,652 - INFO - 方案 #2: 需要填充时长: 5.68秒
2025-07-28 22:17:04,652 - INFO - 方案 #2: 追加场景 scene_id=2018 (完整时长 2.28秒)
2025-07-28 22:17:04,652 - INFO - 方案 #2: 追加场景 scene_id=2019 (完整时长 1.88秒)
2025-07-28 22:17:04,652 - INFO - 方案 #2: 跳过已使用的场景: scene_id=2020
2025-07-28 22:17:04,652 - INFO - 方案 #2: 跳过已使用的场景: scene_id=2021
2025-07-28 22:17:04,652 - INFO - 方案 #2: 追加场景 scene_id=2022 (完整时长 1.44秒)
2025-07-28 22:17:04,652 - INFO - 方案 #2: 追加场景 scene_id=2023 (裁剪至 0.08秒)
2025-07-28 22:17:04,652 - INFO - 方案 #2: 成功填充至目标时长
2025-07-28 22:17:04,652 - INFO - 方案 #2 调整/填充后最终总时长: 6.28秒
2025-07-28 22:17:04,652 - INFO - 方案 #2 添加到方案列表
2025-07-28 22:17:04,652 - INFO - 开始生成方案 #3
2025-07-28 22:17:04,652 - WARNING - 方案 #3: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:17:04,652 - INFO - 开始生成方案 #4
2025-07-28 22:17:04,652 - WARNING - 方案 #4: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:17:04,652 - INFO - 开始生成方案 #5
2025-07-28 22:17:04,652 - WARNING - 方案 #5: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:17:04,652 - INFO - 开始生成方案 #6
2025-07-28 22:17:04,652 - WARNING - 方案 #6: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:17:04,652 - INFO - ========== 字幕 #47 的 2 套有效场景方案生成完成 ==========
2025-07-28 22:17:04,653 - INFO - 
----- 处理字幕 #47 的方案 #1 -----
2025-07-28 22:17:04,653 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\47_1.mp4
2025-07-28 22:17:04,653 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp3gaxej6d
2025-07-28 22:17:04,653 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2016.mp4 (确认存在: True)
2025-07-28 22:17:04,653 - INFO - 添加场景ID=2016，时长=2.44秒，累计时长=2.44秒
2025-07-28 22:17:04,653 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2020.mp4 (确认存在: True)
2025-07-28 22:17:04,653 - INFO - 添加场景ID=2020，时长=1.44秒，累计时长=3.88秒
2025-07-28 22:17:04,653 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2021.mp4 (确认存在: True)
2025-07-28 22:17:04,653 - INFO - 添加场景ID=2021，时长=3.32秒，累计时长=7.20秒
2025-07-28 22:17:04,654 - INFO - 准备合并 3 个场景文件，总时长约 7.20秒
2025-07-28 22:17:04,654 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2016.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2020.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2021.mp4'

2025-07-28 22:17:04,654 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp3gaxej6d\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp3gaxej6d\temp_combined.mp4
2025-07-28 22:17:04,793 - INFO - 合并后的视频时长: 7.27秒，目标音频时长: 6.28秒
2025-07-28 22:17:04,793 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp3gaxej6d\temp_combined.mp4 -ss 0 -to 6.282 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\47_1.mp4
2025-07-28 22:17:05,137 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 22:17:05,137 - INFO - 目标音频时长: 6.28秒
2025-07-28 22:17:05,137 - INFO - 实际视频时长: 6.34秒
2025-07-28 22:17:05,137 - INFO - 时长差异: 0.06秒 (0.97%)
2025-07-28 22:17:05,137 - INFO - ==========================================
2025-07-28 22:17:05,137 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 22:17:05,137 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\47_1.mp4
2025-07-28 22:17:05,138 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp3gaxej6d
2025-07-28 22:17:05,182 - INFO - 方案 #1 处理完成:
2025-07-28 22:17:05,182 - INFO -   - 音频时长: 6.28秒
2025-07-28 22:17:05,182 - INFO -   - 视频时长: 6.34秒
2025-07-28 22:17:05,182 - INFO -   - 时长差异: 0.06秒 (0.97%)
2025-07-28 22:17:05,182 - INFO - 
----- 处理字幕 #47 的方案 #2 -----
2025-07-28 22:17:05,183 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\47_2.mp4
2025-07-28 22:17:05,183 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpp6gmh3ij
2025-07-28 22:17:05,183 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2017.mp4 (确认存在: True)
2025-07-28 22:17:05,183 - INFO - 添加场景ID=2017，时长=0.60秒，累计时长=0.60秒
2025-07-28 22:17:05,183 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2018.mp4 (确认存在: True)
2025-07-28 22:17:05,183 - INFO - 添加场景ID=2018，时长=2.28秒，累计时长=2.88秒
2025-07-28 22:17:05,183 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2019.mp4 (确认存在: True)
2025-07-28 22:17:05,183 - INFO - 添加场景ID=2019，时长=1.88秒，累计时长=4.76秒
2025-07-28 22:17:05,183 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2022.mp4 (确认存在: True)
2025-07-28 22:17:05,183 - INFO - 添加场景ID=2022，时长=1.44秒，累计时长=6.20秒
2025-07-28 22:17:05,183 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2023.mp4 (确认存在: True)
2025-07-28 22:17:05,183 - INFO - 添加场景ID=2023，时长=0.88秒，累计时长=7.08秒
2025-07-28 22:17:05,183 - INFO - 准备合并 5 个场景文件，总时长约 7.08秒
2025-07-28 22:17:05,183 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2017.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2018.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2019.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2022.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2023.mp4'

2025-07-28 22:17:05,185 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpp6gmh3ij\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpp6gmh3ij\temp_combined.mp4
2025-07-28 22:17:05,382 - INFO - 合并后的视频时长: 7.20秒，目标音频时长: 6.28秒
2025-07-28 22:17:05,382 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpp6gmh3ij\temp_combined.mp4 -ss 0 -to 6.282 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\47_2.mp4
2025-07-28 22:17:05,748 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 22:17:05,748 - INFO - 目标音频时长: 6.28秒
2025-07-28 22:17:05,748 - INFO - 实际视频时长: 6.34秒
2025-07-28 22:17:05,748 - INFO - 时长差异: 0.06秒 (0.97%)
2025-07-28 22:17:05,748 - INFO - ==========================================
2025-07-28 22:17:05,748 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 22:17:05,748 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\47_2.mp4
2025-07-28 22:17:05,749 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpp6gmh3ij
2025-07-28 22:17:05,795 - INFO - 方案 #2 处理完成:
2025-07-28 22:17:05,795 - INFO -   - 音频时长: 6.28秒
2025-07-28 22:17:05,795 - INFO -   - 视频时长: 6.34秒
2025-07-28 22:17:05,795 - INFO -   - 时长差异: 0.06秒 (0.97%)
2025-07-28 22:17:05,795 - INFO - 
字幕 #47 处理完成，成功生成 2/2 套方案
2025-07-28 22:17:05,795 - INFO - 生成的视频文件:
2025-07-28 22:17:05,795 - INFO -   1. F:/github/aicut_auto/newcut_ai\47_1.mp4
2025-07-28 22:17:05,795 - INFO -   2. F:/github/aicut_auto/newcut_ai\47_2.mp4
2025-07-28 22:17:05,795 - INFO - ========== 字幕 #47 处理结束 ==========

