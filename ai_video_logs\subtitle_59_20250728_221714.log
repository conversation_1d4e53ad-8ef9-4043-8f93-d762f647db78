2025-07-28 22:17:14,338 - INFO - ========== 字幕 #59 处理开始 ==========
2025-07-28 22:17:14,338 - INFO - 字幕内容: 她最后挣扎，爆出真千金其实早已溺亡的秘密，指认眼前的女主是冒牌货。
2025-07-28 22:17:14,338 - INFO - 字幕序号: [2826, 2828]
2025-07-28 22:17:14,338 - INFO - 音频文件详情:
2025-07-28 22:17:14,338 - INFO -   - 路径: output\59.wav
2025-07-28 22:17:14,338 - INFO -   - 时长: 5.32秒
2025-07-28 22:17:14,338 - INFO -   - 验证音频时长: 5.32秒
2025-07-28 22:17:14,339 - INFO - 字幕时间戳信息:
2025-07-28 22:17:14,348 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-28 22:17:14,348 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-28 22:17:14,348 - INFO -   - 根据生成的音频时长(5.32秒)已调整字幕时间戳
2025-07-28 22:17:14,348 - INFO - ========== 开始为字幕 #59 生成 6 套场景方案 ==========
2025-07-28 22:17:14,348 - INFO - 开始查找字幕序号 [2826, 2828] 对应的场景，共有 2710 个场景可选
2025-07-28 22:17:14,349 - INFO - 找到related_overlap场景: scene_id=2655, 字幕#2826
2025-07-28 22:17:14,349 - INFO - 字幕 #2826 找到 1 个overlap场景, 0 个between场景
2025-07-28 22:17:14,349 - INFO - 字幕 #2828 找到 0 个overlap场景, 0 个between场景
2025-07-28 22:17:14,349 - WARNING - 字幕 #2828 没有找到任何匹配场景!
2025-07-28 22:17:14,349 - INFO - 共收集 1 个未使用的overlap场景和 0 个未使用的between场景
2025-07-28 22:17:14,349 - INFO - 开始生成方案 #1
2025-07-28 22:17:14,349 - INFO - 方案 #1: 为字幕#2826选择初始化overlap场景id=2655
2025-07-28 22:17:14,349 - INFO - 方案 #1: 初始选择后，当前总时长=2.60秒
2025-07-28 22:17:14,349 - INFO - 方案 #1: 额外between选择后，当前总时长=2.60秒
2025-07-28 22:17:14,349 - INFO - 方案 #1: 场景总时长(2.60秒)小于音频时长(5.32秒)，需要延伸填充
2025-07-28 22:17:14,349 - INFO - 方案 #1: 最后一个场景ID: 2655
2025-07-28 22:17:14,350 - INFO - 方案 #1: 找到最后一个场景在原始列表中的索引: 2654
2025-07-28 22:17:14,350 - INFO - 方案 #1: 需要填充时长: 2.72秒
2025-07-28 22:17:14,350 - INFO - 方案 #1: 追加场景 scene_id=2656 (完整时长 2.08秒)
2025-07-28 22:17:14,350 - INFO - 方案 #1: 追加场景 scene_id=2657 (裁剪至 0.64秒)
2025-07-28 22:17:14,350 - INFO - 方案 #1: 成功填充至目标时长
2025-07-28 22:17:14,350 - INFO - 方案 #1 调整/填充后最终总时长: 5.32秒
2025-07-28 22:17:14,350 - INFO - 方案 #1 添加到方案列表
2025-07-28 22:17:14,350 - INFO - 开始生成方案 #2
2025-07-28 22:17:14,350 - WARNING - 方案 #2: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:17:14,350 - INFO - 开始生成方案 #3
2025-07-28 22:17:14,350 - WARNING - 方案 #3: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:17:14,350 - INFO - 开始生成方案 #4
2025-07-28 22:17:14,350 - WARNING - 方案 #4: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:17:14,350 - INFO - 开始生成方案 #5
2025-07-28 22:17:14,350 - WARNING - 方案 #5: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:17:14,350 - INFO - 开始生成方案 #6
2025-07-28 22:17:14,350 - WARNING - 方案 #6: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:17:14,350 - INFO - ========== 字幕 #59 的 1 套有效场景方案生成完成 ==========
2025-07-28 22:17:14,350 - INFO - 
----- 处理字幕 #59 的方案 #1 -----
2025-07-28 22:17:14,350 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\59_1.mp4
2025-07-28 22:17:14,350 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpfr7vwcr8
2025-07-28 22:17:14,351 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2655.mp4 (确认存在: True)
2025-07-28 22:17:14,351 - INFO - 添加场景ID=2655，时长=2.60秒，累计时长=2.60秒
2025-07-28 22:17:14,351 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2656.mp4 (确认存在: True)
2025-07-28 22:17:14,351 - INFO - 添加场景ID=2656，时长=2.08秒，累计时长=4.68秒
2025-07-28 22:17:14,351 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2657.mp4 (确认存在: True)
2025-07-28 22:17:14,351 - INFO - 添加场景ID=2657，时长=2.56秒，累计时长=7.24秒
2025-07-28 22:17:14,351 - INFO - 准备合并 3 个场景文件，总时长约 7.24秒
2025-07-28 22:17:14,351 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2655.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2656.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2657.mp4'

2025-07-28 22:17:14,351 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpfr7vwcr8\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpfr7vwcr8\temp_combined.mp4
2025-07-28 22:17:14,497 - INFO - 合并后的视频时长: 7.31秒，目标音频时长: 5.32秒
2025-07-28 22:17:14,498 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpfr7vwcr8\temp_combined.mp4 -ss 0 -to 5.32 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\59_1.mp4
2025-07-28 22:17:14,822 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 22:17:14,822 - INFO - 目标音频时长: 5.32秒
2025-07-28 22:17:14,822 - INFO - 实际视频时长: 5.34秒
2025-07-28 22:17:14,822 - INFO - 时长差异: 0.02秒 (0.44%)
2025-07-28 22:17:14,822 - INFO - ==========================================
2025-07-28 22:17:14,822 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 22:17:14,822 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\59_1.mp4
2025-07-28 22:17:14,823 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpfr7vwcr8
2025-07-28 22:17:14,865 - INFO - 方案 #1 处理完成:
2025-07-28 22:17:14,865 - INFO -   - 音频时长: 5.32秒
2025-07-28 22:17:14,865 - INFO -   - 视频时长: 5.34秒
2025-07-28 22:17:14,865 - INFO -   - 时长差异: 0.02秒 (0.44%)
2025-07-28 22:17:14,865 - INFO - 
字幕 #59 处理完成，成功生成 1/1 套方案
2025-07-28 22:17:14,865 - INFO - 生成的视频文件:
2025-07-28 22:17:14,865 - INFO -   1. F:/github/aicut_auto/newcut_ai\59_1.mp4
2025-07-28 22:17:14,865 - INFO - ========== 字幕 #59 处理结束 ==========

