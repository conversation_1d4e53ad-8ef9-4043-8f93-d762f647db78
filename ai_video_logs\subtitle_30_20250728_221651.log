2025-07-28 22:16:51,878 - INFO - ========== 字幕 #30 处理开始 ==========
2025-07-28 22:16:51,878 - INFO - 字幕内容: 侯爷痛心疾首，不敢相信养育了十八年的女儿，竟如此恶毒地算计整个侯府。
2025-07-28 22:16:51,878 - INFO - 字幕序号: [950, 952]
2025-07-28 22:16:51,878 - INFO - 音频文件详情:
2025-07-28 22:16:51,878 - INFO -   - 路径: output\30.wav
2025-07-28 22:16:51,878 - INFO -   - 时长: 5.94秒
2025-07-28 22:16:51,878 - INFO -   - 验证音频时长: 5.94秒
2025-07-28 22:16:51,878 - INFO - 字幕时间戳信息:
2025-07-28 22:16:51,879 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-28 22:16:51,879 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-28 22:16:51,879 - INFO -   - 根据生成的音频时长(5.94秒)已调整字幕时间戳
2025-07-28 22:16:51,879 - INFO - ========== 开始为字幕 #30 生成 6 套场景方案 ==========
2025-07-28 22:16:51,879 - INFO - 开始查找字幕序号 [950, 952] 对应的场景，共有 2710 个场景可选
2025-07-28 22:16:51,879 - INFO - 找到related_overlap场景: scene_id=982, 字幕#950
2025-07-28 22:16:51,879 - INFO - 找到related_overlap场景: scene_id=983, 字幕#950
2025-07-28 22:16:51,880 - INFO - 字幕 #950 找到 2 个overlap场景, 0 个between场景
2025-07-28 22:16:51,880 - INFO - 字幕 #952 找到 0 个overlap场景, 0 个between场景
2025-07-28 22:16:51,880 - WARNING - 字幕 #952 没有找到任何匹配场景!
2025-07-28 22:16:51,880 - INFO - 共收集 2 个未使用的overlap场景和 0 个未使用的between场景
2025-07-28 22:16:51,880 - INFO - 开始生成方案 #1
2025-07-28 22:16:51,880 - INFO - 方案 #1: 为字幕#950选择初始化overlap场景id=983
2025-07-28 22:16:51,880 - INFO - 方案 #1: 初始选择后，当前总时长=2.84秒
2025-07-28 22:16:51,880 - INFO - 方案 #1: 额外添加overlap场景id=982, 当前总时长=4.60秒
2025-07-28 22:16:51,880 - INFO - 方案 #1: 额外between选择后，当前总时长=4.60秒
2025-07-28 22:16:51,880 - INFO - 方案 #1: 场景总时长(4.60秒)小于音频时长(5.94秒)，需要延伸填充
2025-07-28 22:16:51,880 - INFO - 方案 #1: 最后一个场景ID: 982
2025-07-28 22:16:51,880 - INFO - 方案 #1: 找到最后一个场景在原始列表中的索引: 981
2025-07-28 22:16:51,880 - INFO - 方案 #1: 需要填充时长: 1.34秒
2025-07-28 22:16:51,880 - INFO - 方案 #1: 跳过已使用的场景: scene_id=983
2025-07-28 22:16:51,880 - INFO - 方案 #1: 追加场景 scene_id=984 (裁剪至 1.34秒)
2025-07-28 22:16:51,880 - INFO - 方案 #1: 成功填充至目标时长
2025-07-28 22:16:51,880 - INFO - 方案 #1 调整/填充后最终总时长: 5.94秒
2025-07-28 22:16:51,880 - INFO - 方案 #1 添加到方案列表
2025-07-28 22:16:51,880 - INFO - 开始生成方案 #2
2025-07-28 22:16:51,880 - WARNING - 方案 #2: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:51,880 - INFO - 开始生成方案 #3
2025-07-28 22:16:51,880 - WARNING - 方案 #3: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:51,880 - INFO - 开始生成方案 #4
2025-07-28 22:16:51,880 - WARNING - 方案 #4: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:51,880 - INFO - 开始生成方案 #5
2025-07-28 22:16:51,880 - WARNING - 方案 #5: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:51,880 - INFO - 开始生成方案 #6
2025-07-28 22:16:51,880 - WARNING - 方案 #6: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:51,880 - INFO - ========== 字幕 #30 的 1 套有效场景方案生成完成 ==========
2025-07-28 22:16:51,880 - INFO - 
----- 处理字幕 #30 的方案 #1 -----
2025-07-28 22:16:51,880 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\30_1.mp4
2025-07-28 22:16:51,881 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp0z4833dm
2025-07-28 22:16:51,881 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\983.mp4 (确认存在: True)
2025-07-28 22:16:51,881 - INFO - 添加场景ID=983，时长=2.84秒，累计时长=2.84秒
2025-07-28 22:16:51,881 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\982.mp4 (确认存在: True)
2025-07-28 22:16:51,881 - INFO - 添加场景ID=982，时长=1.76秒，累计时长=4.60秒
2025-07-28 22:16:51,881 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\984.mp4 (确认存在: True)
2025-07-28 22:16:51,881 - INFO - 添加场景ID=984，时长=1.96秒，累计时长=6.56秒
2025-07-28 22:16:51,881 - INFO - 准备合并 3 个场景文件，总时长约 6.56秒
2025-07-28 22:16:51,882 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/983.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/982.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/984.mp4'

2025-07-28 22:16:51,882 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp0z4833dm\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp0z4833dm\temp_combined.mp4
2025-07-28 22:16:52,037 - INFO - 合并后的视频时长: 6.63秒，目标音频时长: 5.94秒
2025-07-28 22:16:52,037 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp0z4833dm\temp_combined.mp4 -ss 0 -to 5.936 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\30_1.mp4
2025-07-28 22:16:52,395 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 22:16:52,395 - INFO - 目标音频时长: 5.94秒
2025-07-28 22:16:52,395 - INFO - 实际视频时长: 5.98秒
2025-07-28 22:16:52,395 - INFO - 时长差异: 0.05秒 (0.79%)
2025-07-28 22:16:52,395 - INFO - ==========================================
2025-07-28 22:16:52,395 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 22:16:52,395 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\30_1.mp4
2025-07-28 22:16:52,396 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp0z4833dm
2025-07-28 22:16:52,443 - INFO - 方案 #1 处理完成:
2025-07-28 22:16:52,443 - INFO -   - 音频时长: 5.94秒
2025-07-28 22:16:52,443 - INFO -   - 视频时长: 5.98秒
2025-07-28 22:16:52,443 - INFO -   - 时长差异: 0.05秒 (0.79%)
2025-07-28 22:16:52,443 - INFO - 
字幕 #30 处理完成，成功生成 1/1 套方案
2025-07-28 22:16:52,443 - INFO - 生成的视频文件:
2025-07-28 22:16:52,443 - INFO -   1. F:/github/aicut_auto/newcut_ai\30_1.mp4
2025-07-28 22:16:52,443 - INFO - ========== 字幕 #30 处理结束 ==========

