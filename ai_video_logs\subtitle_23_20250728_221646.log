2025-07-28 22:16:46,917 - INFO - ========== 字幕 #23 处理开始 ==========
2025-07-28 22:16:46,917 - INFO - 字幕内容: 老祖再次发动技能，让这恶毒心声响彻全场，二哥终于听见自己要当“替罪羊”的事实。
2025-07-28 22:16:46,917 - INFO - 字幕序号: [760, 762]
2025-07-28 22:16:46,917 - INFO - 音频文件详情:
2025-07-28 22:16:46,917 - INFO -   - 路径: output\23.wav
2025-07-28 22:16:46,917 - INFO -   - 时长: 5.83秒
2025-07-28 22:16:46,917 - INFO -   - 验证音频时长: 5.83秒
2025-07-28 22:16:46,917 - INFO - 字幕时间戳信息:
2025-07-28 22:16:46,917 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-28 22:16:46,917 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-28 22:16:46,917 - INFO -   - 根据生成的音频时长(5.83秒)已调整字幕时间戳
2025-07-28 22:16:46,917 - INFO - ========== 开始为字幕 #23 生成 6 套场景方案 ==========
2025-07-28 22:16:46,917 - INFO - 开始查找字幕序号 [760, 762] 对应的场景，共有 2710 个场景可选
2025-07-28 22:16:46,918 - INFO - 找到related_overlap场景: scene_id=780, 字幕#760
2025-07-28 22:16:46,918 - INFO - 找到related_overlap场景: scene_id=781, 字幕#762
2025-07-28 22:16:46,918 - INFO - 找到related_between场景: scene_id=779, 字幕#760
2025-07-28 22:16:46,918 - INFO - 字幕 #760 找到 1 个overlap场景, 1 个between场景
2025-07-28 22:16:46,918 - INFO - 字幕 #762 找到 1 个overlap场景, 0 个between场景
2025-07-28 22:16:46,918 - INFO - 共收集 2 个未使用的overlap场景和 1 个未使用的between场景
2025-07-28 22:16:46,918 - INFO - 开始生成方案 #1
2025-07-28 22:16:46,918 - INFO - 方案 #1: 为字幕#760选择初始化overlap场景id=780
2025-07-28 22:16:46,918 - INFO - 方案 #1: 为字幕#762选择初始化overlap场景id=781
2025-07-28 22:16:46,918 - INFO - 方案 #1: 初始选择后，当前总时长=4.24秒
2025-07-28 22:16:46,918 - INFO - 方案 #1: 额外between选择后，当前总时长=4.24秒
2025-07-28 22:16:46,918 - INFO - 方案 #1: 额外添加between场景id=779, 当前总时长=5.24秒
2025-07-28 22:16:46,918 - INFO - 方案 #1: 场景总时长(5.24秒)小于音频时长(5.83秒)，需要延伸填充
2025-07-28 22:16:46,918 - INFO - 方案 #1: 最后一个场景ID: 779
2025-07-28 22:16:46,918 - INFO - 方案 #1: 找到最后一个场景在原始列表中的索引: 778
2025-07-28 22:16:46,918 - INFO - 方案 #1: 需要填充时长: 0.59秒
2025-07-28 22:16:46,918 - INFO - 方案 #1: 跳过已使用的场景: scene_id=780
2025-07-28 22:16:46,918 - INFO - 方案 #1: 跳过已使用的场景: scene_id=781
2025-07-28 22:16:46,918 - INFO - 方案 #1: 追加场景 scene_id=782 (裁剪至 0.59秒)
2025-07-28 22:16:46,918 - INFO - 方案 #1: 成功填充至目标时长
2025-07-28 22:16:46,918 - INFO - 方案 #1 调整/填充后最终总时长: 5.83秒
2025-07-28 22:16:46,918 - INFO - 方案 #1 添加到方案列表
2025-07-28 22:16:46,918 - INFO - 开始生成方案 #2
2025-07-28 22:16:46,918 - WARNING - 方案 #2: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:46,918 - INFO - 开始生成方案 #3
2025-07-28 22:16:46,919 - WARNING - 方案 #3: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:46,919 - INFO - 开始生成方案 #4
2025-07-28 22:16:46,919 - WARNING - 方案 #4: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:46,919 - INFO - 开始生成方案 #5
2025-07-28 22:16:46,919 - WARNING - 方案 #5: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:46,919 - INFO - 开始生成方案 #6
2025-07-28 22:16:46,919 - WARNING - 方案 #6: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:46,919 - INFO - ========== 字幕 #23 的 1 套有效场景方案生成完成 ==========
2025-07-28 22:16:46,919 - INFO - 
----- 处理字幕 #23 的方案 #1 -----
2025-07-28 22:16:46,919 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\23_1.mp4
2025-07-28 22:16:46,919 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp7ptpj028
2025-07-28 22:16:46,919 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\780.mp4 (确认存在: True)
2025-07-28 22:16:46,919 - INFO - 添加场景ID=780，时长=2.36秒，累计时长=2.36秒
2025-07-28 22:16:46,919 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\781.mp4 (确认存在: True)
2025-07-28 22:16:46,919 - INFO - 添加场景ID=781，时长=1.88秒，累计时长=4.24秒
2025-07-28 22:16:46,920 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\779.mp4 (确认存在: True)
2025-07-28 22:16:46,920 - INFO - 添加场景ID=779，时长=1.00秒，累计时长=5.24秒
2025-07-28 22:16:46,920 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\782.mp4 (确认存在: True)
2025-07-28 22:16:46,920 - INFO - 添加场景ID=782，时长=2.52秒，累计时长=7.76秒
2025-07-28 22:16:46,920 - INFO - 准备合并 4 个场景文件，总时长约 7.76秒
2025-07-28 22:16:46,920 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/780.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/781.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/779.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/782.mp4'

2025-07-28 22:16:46,920 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp7ptpj028\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp7ptpj028\temp_combined.mp4
2025-07-28 22:16:47,065 - INFO - 合并后的视频时长: 7.85秒，目标音频时长: 5.83秒
2025-07-28 22:16:47,065 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp7ptpj028\temp_combined.mp4 -ss 0 -to 5.832 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\23_1.mp4
2025-07-28 22:16:47,408 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 22:16:47,409 - INFO - 目标音频时长: 5.83秒
2025-07-28 22:16:47,409 - INFO - 实际视频时长: 5.86秒
2025-07-28 22:16:47,409 - INFO - 时长差异: 0.03秒 (0.53%)
2025-07-28 22:16:47,409 - INFO - ==========================================
2025-07-28 22:16:47,409 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 22:16:47,409 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\23_1.mp4
2025-07-28 22:16:47,409 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp7ptpj028
2025-07-28 22:16:47,459 - INFO - 方案 #1 处理完成:
2025-07-28 22:16:47,459 - INFO -   - 音频时长: 5.83秒
2025-07-28 22:16:47,459 - INFO -   - 视频时长: 5.86秒
2025-07-28 22:16:47,459 - INFO -   - 时长差异: 0.03秒 (0.53%)
2025-07-28 22:16:47,459 - INFO - 
字幕 #23 处理完成，成功生成 1/1 套方案
2025-07-28 22:16:47,459 - INFO - 生成的视频文件:
2025-07-28 22:16:47,459 - INFO -   1. F:/github/aicut_auto/newcut_ai\23_1.mp4
2025-07-28 22:16:47,459 - INFO - ========== 字幕 #23 处理结束 ==========

