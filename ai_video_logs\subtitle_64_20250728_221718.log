2025-07-28 22:17:18,181 - INFO - ========== 字幕 #64 处理开始 ==========
2025-07-28 22:17:18,181 - INFO - 字幕内容: 一场盛大的婚礼，为这场精彩绝伦的逆袭，画上了最圆满的句号。
2025-07-28 22:17:18,181 - INFO - 字幕序号: [2900, 2902]
2025-07-28 22:17:18,181 - INFO - 音频文件详情:
2025-07-28 22:17:18,181 - INFO -   - 路径: output\64.wav
2025-07-28 22:17:18,181 - INFO -   - 时长: 3.08秒
2025-07-28 22:17:18,181 - INFO -   - 验证音频时长: 3.08秒
2025-07-28 22:17:18,181 - INFO - 字幕时间戳信息:
2025-07-28 22:17:18,181 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-28 22:17:18,182 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-28 22:17:18,182 - INFO -   - 根据生成的音频时长(3.08秒)已调整字幕时间戳
2025-07-28 22:17:18,182 - INFO - ========== 开始为字幕 #64 生成 6 套场景方案 ==========
2025-07-28 22:17:18,182 - INFO - 开始查找字幕序号 [2900, 2902] 对应的场景，共有 2710 个场景可选
2025-07-28 22:17:18,182 - INFO - 找到related_overlap场景: scene_id=2697, 字幕#2900
2025-07-28 22:17:18,182 - INFO - 找到related_overlap场景: scene_id=2698, 字幕#2900
2025-07-28 22:17:18,182 - INFO - 找到related_overlap场景: scene_id=2705, 字幕#2902
2025-07-28 22:17:18,183 - INFO - 找到related_between场景: scene_id=2702, 字幕#2902
2025-07-28 22:17:18,183 - INFO - 找到related_between场景: scene_id=2703, 字幕#2902
2025-07-28 22:17:18,183 - INFO - 找到related_between场景: scene_id=2704, 字幕#2902
2025-07-28 22:17:18,183 - INFO - 找到related_between场景: scene_id=2706, 字幕#2902
2025-07-28 22:17:18,183 - INFO - 字幕 #2900 找到 2 个overlap场景, 0 个between场景
2025-07-28 22:17:18,183 - INFO - 字幕 #2902 找到 1 个overlap场景, 4 个between场景
2025-07-28 22:17:18,183 - INFO - 共收集 3 个未使用的overlap场景和 4 个未使用的between场景
2025-07-28 22:17:18,183 - INFO - 开始生成方案 #1
2025-07-28 22:17:18,183 - INFO - 方案 #1: 为字幕#2900选择初始化overlap场景id=2697
2025-07-28 22:17:18,183 - INFO - 方案 #1: 为字幕#2902选择初始化overlap场景id=2705
2025-07-28 22:17:18,183 - INFO - 方案 #1: 初始选择后，当前总时长=4.56秒
2025-07-28 22:17:18,183 - INFO - 方案 #1: 额外between选择后，当前总时长=4.56秒
2025-07-28 22:17:18,183 - INFO - 方案 #1: 场景总时长(4.56秒)大于音频时长(3.08秒)，需要裁剪
2025-07-28 22:17:18,183 - INFO - 调整前总时长: 4.56秒, 目标时长: 3.08秒
2025-07-28 22:17:18,183 - INFO - 需要裁剪 1.48秒
2025-07-28 22:17:18,183 - INFO - 裁剪最长场景ID=2705：从2.84秒裁剪至1.36秒
2025-07-28 22:17:18,183 - INFO - 调整后总时长: 3.08秒，与目标时长差异: 0.00秒
2025-07-28 22:17:18,183 - INFO - 方案 #1 调整/填充后最终总时长: 3.08秒
2025-07-28 22:17:18,183 - INFO - 方案 #1 添加到方案列表
2025-07-28 22:17:18,183 - INFO - 开始生成方案 #2
2025-07-28 22:17:18,183 - INFO - 方案 #2: 为字幕#2900选择初始化overlap场景id=2698
2025-07-28 22:17:18,183 - INFO - 方案 #2: 初始选择后，当前总时长=5.28秒
2025-07-28 22:17:18,183 - INFO - 方案 #2: 为字幕#2902选择初始化between场景id=2702
2025-07-28 22:17:18,183 - INFO - 方案 #2: 额外between选择后，当前总时长=7.16秒
2025-07-28 22:17:18,183 - INFO - 方案 #2: 场景总时长(7.16秒)大于音频时长(3.08秒)，需要裁剪
2025-07-28 22:17:18,183 - INFO - 调整前总时长: 7.16秒, 目标时长: 3.08秒
2025-07-28 22:17:18,183 - INFO - 需要裁剪 4.08秒
2025-07-28 22:17:18,183 - INFO - 裁剪单个场景会导致时长过短，尝试裁剪多个场景
2025-07-28 22:17:18,183 - INFO - 裁剪场景ID=2698：从5.28秒裁剪至1.58秒
2025-07-28 22:17:18,183 - INFO - 裁剪场景ID=2702：从1.88秒裁剪至1.58秒
2025-07-28 22:17:18,183 - WARNING - 通过裁剪无法达到目标时长，需要移除场景，剩余需裁剪时长: 0.09秒
2025-07-28 22:17:18,183 - INFO - 调整后总时长: 3.17秒，与目标时长差异: 0.09秒
2025-07-28 22:17:18,183 - INFO - 方案 #2 调整/填充后最终总时长: 3.17秒
2025-07-28 22:17:18,183 - INFO - 方案 #2 添加到方案列表
2025-07-28 22:17:18,183 - INFO - 开始生成方案 #3
2025-07-28 22:17:18,183 - INFO - 方案 #3: 初始选择后，当前总时长=0.00秒
2025-07-28 22:17:18,184 - INFO - 方案 #3: 为字幕#2902选择初始化between场景id=2704
2025-07-28 22:17:18,184 - INFO - 方案 #3: 额外between选择后，当前总时长=2.08秒
2025-07-28 22:17:18,184 - INFO - 方案 #3: 额外添加between场景id=2703, 当前总时长=4.08秒
2025-07-28 22:17:18,184 - INFO - 方案 #3: 场景总时长(4.08秒)大于音频时长(3.08秒)，需要裁剪
2025-07-28 22:17:18,184 - INFO - 调整前总时长: 4.08秒, 目标时长: 3.08秒
2025-07-28 22:17:18,184 - INFO - 需要裁剪 1.00秒
2025-07-28 22:17:18,184 - INFO - 裁剪最长场景ID=2704：从2.08秒裁剪至1.08秒
2025-07-28 22:17:18,184 - INFO - 调整后总时长: 3.08秒，与目标时长差异: 0.00秒
2025-07-28 22:17:18,184 - INFO - 方案 #3 调整/填充后最终总时长: 3.08秒
2025-07-28 22:17:18,184 - INFO - 方案 #3 添加到方案列表
2025-07-28 22:17:18,184 - INFO - 开始生成方案 #4
2025-07-28 22:17:18,184 - INFO - 方案 #4: 初始选择后，当前总时长=0.00秒
2025-07-28 22:17:18,184 - INFO - 方案 #4: 为字幕#2902选择初始化between场景id=2706
2025-07-28 22:17:18,184 - INFO - 方案 #4: 额外between选择后，当前总时长=1.56秒
2025-07-28 22:17:18,184 - INFO - 方案 #4: 场景总时长(1.56秒)小于音频时长(3.08秒)，需要延伸填充
2025-07-28 22:17:18,184 - INFO - 方案 #4: 最后一个场景ID: 2706
2025-07-28 22:17:18,184 - INFO - 方案 #4: 找到最后一个场景在原始列表中的索引: 2705
2025-07-28 22:17:18,184 - INFO - 方案 #4: 需要填充时长: 1.52秒
2025-07-28 22:17:18,184 - INFO - 方案 #4: 追加场景 scene_id=2707 (裁剪至 1.52秒)
2025-07-28 22:17:18,184 - INFO - 方案 #4: 成功填充至目标时长
2025-07-28 22:17:18,184 - INFO - 方案 #4 调整/填充后最终总时长: 3.08秒
2025-07-28 22:17:18,184 - INFO - 方案 #4 添加到方案列表
2025-07-28 22:17:18,184 - INFO - 开始生成方案 #5
2025-07-28 22:17:18,184 - WARNING - 方案 #5: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:17:18,184 - INFO - 开始生成方案 #6
2025-07-28 22:17:18,184 - WARNING - 方案 #6: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:17:18,184 - INFO - ========== 字幕 #64 的 4 套有效场景方案生成完成 ==========
2025-07-28 22:17:18,184 - INFO - 
----- 处理字幕 #64 的方案 #1 -----
2025-07-28 22:17:18,184 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\64_1.mp4
2025-07-28 22:17:18,184 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpkd9wp0ue
2025-07-28 22:17:18,185 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2697.mp4 (确认存在: True)
2025-07-28 22:17:18,185 - INFO - 添加场景ID=2697，时长=1.72秒，累计时长=1.72秒
2025-07-28 22:17:18,185 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2705.mp4 (确认存在: True)
2025-07-28 22:17:18,185 - INFO - 添加场景ID=2705，时长=2.84秒，累计时长=4.56秒
2025-07-28 22:17:18,185 - INFO - 准备合并 2 个场景文件，总时长约 4.56秒
2025-07-28 22:17:18,185 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2697.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2705.mp4'

2025-07-28 22:17:18,185 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpkd9wp0ue\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpkd9wp0ue\temp_combined.mp4
2025-07-28 22:17:18,302 - INFO - 合并后的视频时长: 4.61秒，目标音频时长: 3.08秒
2025-07-28 22:17:18,302 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpkd9wp0ue\temp_combined.mp4 -ss 0 -to 3.08 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\64_1.mp4
2025-07-28 22:17:18,558 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 22:17:18,558 - INFO - 目标音频时长: 3.08秒
2025-07-28 22:17:18,559 - INFO - 实际视频时长: 3.10秒
2025-07-28 22:17:18,559 - INFO - 时长差异: 0.02秒 (0.75%)
2025-07-28 22:17:18,559 - INFO - ==========================================
2025-07-28 22:17:18,559 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 22:17:18,559 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\64_1.mp4
2025-07-28 22:17:18,559 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpkd9wp0ue
2025-07-28 22:17:18,602 - INFO - 方案 #1 处理完成:
2025-07-28 22:17:18,602 - INFO -   - 音频时长: 3.08秒
2025-07-28 22:17:18,602 - INFO -   - 视频时长: 3.10秒
2025-07-28 22:17:18,602 - INFO -   - 时长差异: 0.02秒 (0.75%)
2025-07-28 22:17:18,602 - INFO - 
----- 处理字幕 #64 的方案 #2 -----
2025-07-28 22:17:18,602 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\64_2.mp4
2025-07-28 22:17:18,602 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpnqy00386
2025-07-28 22:17:18,603 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2698.mp4 (确认存在: True)
2025-07-28 22:17:18,603 - INFO - 添加场景ID=2698，时长=5.28秒，累计时长=5.28秒
2025-07-28 22:17:18,603 - INFO - 场景总时长(5.28秒)已达到音频时长(3.08秒)的1.5倍，停止添加场景
2025-07-28 22:17:18,603 - INFO - 准备合并 1 个场景文件，总时长约 5.28秒
2025-07-28 22:17:18,603 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2698.mp4'

2025-07-28 22:17:18,603 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpnqy00386\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpnqy00386\temp_combined.mp4
2025-07-28 22:17:18,736 - INFO - 合并后的视频时长: 5.30秒，目标音频时长: 3.08秒
2025-07-28 22:17:18,736 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpnqy00386\temp_combined.mp4 -ss 0 -to 3.08 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\64_2.mp4
2025-07-28 22:17:19,004 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 22:17:19,004 - INFO - 目标音频时长: 3.08秒
2025-07-28 22:17:19,004 - INFO - 实际视频时长: 3.10秒
2025-07-28 22:17:19,004 - INFO - 时长差异: 0.02秒 (0.75%)
2025-07-28 22:17:19,004 - INFO - ==========================================
2025-07-28 22:17:19,004 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 22:17:19,004 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\64_2.mp4
2025-07-28 22:17:19,005 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpnqy00386
2025-07-28 22:17:19,053 - INFO - 方案 #2 处理完成:
2025-07-28 22:17:19,053 - INFO -   - 音频时长: 3.08秒
2025-07-28 22:17:19,053 - INFO -   - 视频时长: 3.10秒
2025-07-28 22:17:19,053 - INFO -   - 时长差异: 0.02秒 (0.75%)
2025-07-28 22:17:19,053 - INFO - 
----- 处理字幕 #64 的方案 #3 -----
2025-07-28 22:17:19,053 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\64_3.mp4
2025-07-28 22:17:19,053 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpp0g6l5lg
2025-07-28 22:17:19,053 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2704.mp4 (确认存在: True)
2025-07-28 22:17:19,053 - INFO - 添加场景ID=2704，时长=2.08秒，累计时长=2.08秒
2025-07-28 22:17:19,053 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2703.mp4 (确认存在: True)
2025-07-28 22:17:19,053 - INFO - 添加场景ID=2703，时长=2.00秒，累计时长=4.08秒
2025-07-28 22:17:19,055 - INFO - 准备合并 2 个场景文件，总时长约 4.08秒
2025-07-28 22:17:19,055 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2704.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2703.mp4'

2025-07-28 22:17:19,055 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpp0g6l5lg\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpp0g6l5lg\temp_combined.mp4
2025-07-28 22:17:19,192 - INFO - 合并后的视频时长: 4.13秒，目标音频时长: 3.08秒
2025-07-28 22:17:19,192 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpp0g6l5lg\temp_combined.mp4 -ss 0 -to 3.08 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\64_3.mp4
2025-07-28 22:17:19,460 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 22:17:19,460 - INFO - 目标音频时长: 3.08秒
2025-07-28 22:17:19,460 - INFO - 实际视频时长: 3.10秒
2025-07-28 22:17:19,460 - INFO - 时长差异: 0.02秒 (0.75%)
2025-07-28 22:17:19,460 - INFO - ==========================================
2025-07-28 22:17:19,460 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 22:17:19,460 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\64_3.mp4
2025-07-28 22:17:19,460 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpp0g6l5lg
2025-07-28 22:17:19,505 - INFO - 方案 #3 处理完成:
2025-07-28 22:17:19,505 - INFO -   - 音频时长: 3.08秒
2025-07-28 22:17:19,505 - INFO -   - 视频时长: 3.10秒
2025-07-28 22:17:19,505 - INFO -   - 时长差异: 0.02秒 (0.75%)
2025-07-28 22:17:19,505 - INFO - 
----- 处理字幕 #64 的方案 #4 -----
2025-07-28 22:17:19,505 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\64_4.mp4
2025-07-28 22:17:19,505 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp_g_gdn56
2025-07-28 22:17:19,506 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2706.mp4 (确认存在: True)
2025-07-28 22:17:19,506 - INFO - 添加场景ID=2706，时长=1.56秒，累计时长=1.56秒
2025-07-28 22:17:19,506 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2707.mp4 (确认存在: True)
2025-07-28 22:17:19,506 - INFO - 添加场景ID=2707，时长=5.16秒，累计时长=6.72秒
2025-07-28 22:17:19,506 - INFO - 场景总时长(6.72秒)已达到音频时长(3.08秒)的1.5倍，停止添加场景
2025-07-28 22:17:19,506 - INFO - 准备合并 2 个场景文件，总时长约 6.72秒
2025-07-28 22:17:19,506 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2706.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2707.mp4'

2025-07-28 22:17:19,506 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp_g_gdn56\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp_g_gdn56\temp_combined.mp4
2025-07-28 22:17:19,658 - INFO - 合并后的视频时长: 6.77秒，目标音频时长: 3.08秒
2025-07-28 22:17:19,658 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp_g_gdn56\temp_combined.mp4 -ss 0 -to 3.08 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\64_4.mp4
2025-07-28 22:17:19,918 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 22:17:19,918 - INFO - 目标音频时长: 3.08秒
2025-07-28 22:17:19,918 - INFO - 实际视频时长: 3.10秒
2025-07-28 22:17:19,918 - INFO - 时长差异: 0.02秒 (0.75%)
2025-07-28 22:17:19,918 - INFO - ==========================================
2025-07-28 22:17:19,919 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 22:17:19,919 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\64_4.mp4
2025-07-28 22:17:19,919 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp_g_gdn56
2025-07-28 22:17:19,964 - INFO - 方案 #4 处理完成:
2025-07-28 22:17:19,964 - INFO -   - 音频时长: 3.08秒
2025-07-28 22:17:19,964 - INFO -   - 视频时长: 3.10秒
2025-07-28 22:17:19,964 - INFO -   - 时长差异: 0.02秒 (0.75%)
2025-07-28 22:17:19,964 - INFO - 
字幕 #64 处理完成，成功生成 4/4 套方案
2025-07-28 22:17:19,964 - INFO - 生成的视频文件:
2025-07-28 22:17:19,964 - INFO -   1. F:/github/aicut_auto/newcut_ai\64_1.mp4
2025-07-28 22:17:19,964 - INFO -   2. F:/github/aicut_auto/newcut_ai\64_2.mp4
2025-07-28 22:17:19,964 - INFO -   3. F:/github/aicut_auto/newcut_ai\64_3.mp4
2025-07-28 22:17:19,964 - INFO -   4. F:/github/aicut_auto/newcut_ai\64_4.mp4
2025-07-28 22:17:19,964 - INFO - ========== 字幕 #64 处理结束 ==========

