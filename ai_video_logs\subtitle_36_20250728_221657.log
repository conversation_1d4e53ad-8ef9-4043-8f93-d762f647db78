2025-07-28 22:16:57,476 - INFO - ========== 字幕 #36 处理开始 ==========
2025-07-28 22:16:57,477 - INFO - 字幕内容: 这个愚蠢的女人，到死都不知道，还没到手的利润，终究只是镜花水月。
2025-07-28 22:16:57,477 - INFO - 字幕序号: [1536]
2025-07-28 22:16:57,477 - INFO - 音频文件详情:
2025-07-28 22:16:57,477 - INFO -   - 路径: output\36.wav
2025-07-28 22:16:57,477 - INFO -   - 时长: 4.32秒
2025-07-28 22:16:57,477 - INFO -   - 验证音频时长: 4.32秒
2025-07-28 22:16:57,477 - INFO - 字幕时间戳信息:
2025-07-28 22:16:57,486 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-28 22:16:57,486 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-28 22:16:57,486 - INFO -   - 根据生成的音频时长(4.32秒)已调整字幕时间戳
2025-07-28 22:16:57,486 - INFO - ========== 开始为字幕 #36 生成 6 套场景方案 ==========
2025-07-28 22:16:57,487 - INFO - 开始查找字幕序号 [1536] 对应的场景，共有 2710 个场景可选
2025-07-28 22:16:57,487 - INFO - 找到related_overlap场景: scene_id=1444, 字幕#1536
2025-07-28 22:16:57,487 - INFO - 找到related_overlap场景: scene_id=1445, 字幕#1536
2025-07-28 22:16:57,488 - INFO - 字幕 #1536 找到 2 个overlap场景, 0 个between场景
2025-07-28 22:16:57,488 - INFO - 共收集 2 个未使用的overlap场景和 0 个未使用的between场景
2025-07-28 22:16:57,488 - INFO - 开始生成方案 #1
2025-07-28 22:16:57,488 - INFO - 方案 #1: 为字幕#1536选择初始化overlap场景id=1445
2025-07-28 22:16:57,488 - INFO - 方案 #1: 初始选择后，当前总时长=2.44秒
2025-07-28 22:16:57,488 - INFO - 方案 #1: 额外添加overlap场景id=1444, 当前总时长=5.08秒
2025-07-28 22:16:57,488 - INFO - 方案 #1: 额外between选择后，当前总时长=5.08秒
2025-07-28 22:16:57,488 - INFO - 方案 #1: 场景总时长(5.08秒)大于音频时长(4.32秒)，需要裁剪
2025-07-28 22:16:57,488 - INFO - 调整前总时长: 5.08秒, 目标时长: 4.32秒
2025-07-28 22:16:57,488 - INFO - 需要裁剪 0.76秒
2025-07-28 22:16:57,488 - INFO - 裁剪最长场景ID=1444：从2.64秒裁剪至1.88秒
2025-07-28 22:16:57,488 - INFO - 调整后总时长: 4.32秒，与目标时长差异: 0.00秒
2025-07-28 22:16:57,488 - INFO - 方案 #1 调整/填充后最终总时长: 4.32秒
2025-07-28 22:16:57,488 - INFO - 方案 #1 添加到方案列表
2025-07-28 22:16:57,488 - INFO - 开始生成方案 #2
2025-07-28 22:16:57,488 - WARNING - 方案 #2: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:57,488 - INFO - 开始生成方案 #3
2025-07-28 22:16:57,488 - WARNING - 方案 #3: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:57,488 - INFO - 开始生成方案 #4
2025-07-28 22:16:57,488 - WARNING - 方案 #4: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:57,488 - INFO - 开始生成方案 #5
2025-07-28 22:16:57,488 - WARNING - 方案 #5: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:57,488 - INFO - 开始生成方案 #6
2025-07-28 22:16:57,488 - WARNING - 方案 #6: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:57,488 - INFO - ========== 字幕 #36 的 1 套有效场景方案生成完成 ==========
2025-07-28 22:16:57,488 - INFO - 
----- 处理字幕 #36 的方案 #1 -----
2025-07-28 22:16:57,488 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\36_1.mp4
2025-07-28 22:16:57,489 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp4ztmukq2
2025-07-28 22:16:57,489 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1445.mp4 (确认存在: True)
2025-07-28 22:16:57,489 - INFO - 添加场景ID=1445，时长=2.44秒，累计时长=2.44秒
2025-07-28 22:16:57,489 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1444.mp4 (确认存在: True)
2025-07-28 22:16:57,489 - INFO - 添加场景ID=1444，时长=2.64秒，累计时长=5.08秒
2025-07-28 22:16:57,489 - INFO - 准备合并 2 个场景文件，总时长约 5.08秒
2025-07-28 22:16:57,489 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1445.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1444.mp4'

2025-07-28 22:16:57,490 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp4ztmukq2\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp4ztmukq2\temp_combined.mp4
2025-07-28 22:16:57,622 - INFO - 合并后的视频时长: 5.13秒，目标音频时长: 4.32秒
2025-07-28 22:16:57,622 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp4ztmukq2\temp_combined.mp4 -ss 0 -to 4.318 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\36_1.mp4
2025-07-28 22:16:57,919 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 22:16:57,919 - INFO - 目标音频时长: 4.32秒
2025-07-28 22:16:57,919 - INFO - 实际视频时长: 4.34秒
2025-07-28 22:16:57,919 - INFO - 时长差异: 0.03秒 (0.58%)
2025-07-28 22:16:57,919 - INFO - ==========================================
2025-07-28 22:16:57,919 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 22:16:57,919 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\36_1.mp4
2025-07-28 22:16:57,920 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp4ztmukq2
2025-07-28 22:16:57,964 - INFO - 方案 #1 处理完成:
2025-07-28 22:16:57,964 - INFO -   - 音频时长: 4.32秒
2025-07-28 22:16:57,964 - INFO -   - 视频时长: 4.34秒
2025-07-28 22:16:57,964 - INFO -   - 时长差异: 0.03秒 (0.58%)
2025-07-28 22:16:57,964 - INFO - 
字幕 #36 处理完成，成功生成 1/1 套方案
2025-07-28 22:16:57,964 - INFO - 生成的视频文件:
2025-07-28 22:16:57,964 - INFO -   1. F:/github/aicut_auto/newcut_ai\36_1.mp4
2025-07-28 22:16:57,964 - INFO - ========== 字幕 #36 处理结束 ==========

