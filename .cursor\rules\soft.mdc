---
description: 
globs: 
alwaysApply: false
---
# AI视频处理工具桌面应用技术方案

## 项目概述

将现有的命令行AI视频处理工具转换为具有图形用户界面的桌面应用程序，提供更友好的用户体验和实时状态监控。

## 技术架构

### 整体架构设计

```
桌面应用层 (PyWebView)
    ↓
Web界面层 (Vue 3 + Element Plus)
    ↓ WebSocket + RESTful API
后端服务层 (FastAPI)
    ↓
核心业务层 (现有Python模块)
```

### 核心技术栈

#### 后端框架：FastAPI
- **高性能**：基于Starlette和Pydantic，性能优于Flask
- **现代化**：原生支持异步编程和类型提示
- **自动文档**：自动生成OpenAPI/Swagger文档
- **WebSocket支持**：内置WebSocket支持，无需额外插件
- **数据验证**：基于Pydantic的自动数据验证

#### 前端框架：Vue 3 + Element Plus
- **Vue 3**：现代化的响应式框架，支持Composition API
- **Element Plus**：成熟的UI组件库，提供丰富的组件
- **Pinia**：现代化的状态管理库
- **TypeScript**：类型安全的开发体验

#### 桌面集成：PyWebView
- **原生窗口**：创建原生桌面应用窗口
- **Web视图嵌入**：嵌入Web界面
- **系统集成**：支持系统托盘、文件对话框等

## 项目结构

```
aicut_auto/
├── backend/                 # 后端服务
│   ├── app/
│   │   ├── __init__.py
│   │   ├── main.py         # FastAPI应用入口
│   │   ├── api/            # API路由
│   │   │   ├── __init__.py
│   │   │   ├── video.py    # 视频处理API
│   │   │   ├── watermark.py # 水印处理API
│   │   │   ├── subtitle.py # 字幕处理API
│   │   │   └── system.py   # 系统监控API
│   │   ├── core/           # 核心业务逻辑
│   │   │   ├── __init__.py
│   │   │   ├── video_processor.py
│   │   │   ├── watermark_remover.py
│   │   │   └── subtitle_handler.py
│   │   ├── models/         # 数据模型
│   │   │   ├── __init__.py
│   │   │   ├── video.py
│   │   │   └── task.py
│   │   ├── services/       # 业务服务
│   │   │   ├── __init__.py
│   │   │   ├── task_manager.py
│   │   │   └── websocket_manager.py
│   │   └── utils/          # 工具函数
│   │       ├── __init__.py
│   │       ├── logger.py
│   │       └── config.py
│   └── requirements.txt
├── frontend/                # 前端界面
│   ├── public/
│   ├── src/
│   │   ├── components/     # Vue组件
│   │   │   ├── VideoProcessor.vue
│   │   │   ├── WatermarkRemover.vue
│   │   │   ├── SubtitleEditor.vue
│   │   │   └── LogViewer.vue
│   │   ├── views/          # 页面视图
│   │   │   ├── Home.vue
│   │   │   ├── VideoProcessing.vue
│   │   │   └── Settings.vue
│   │   ├── stores/         # Pinia状态管理
│   │   │   ├── task.js
│   │   │   └── system.js
│   │   ├── utils/          # 工具函数
│   │   │   ├── api.js
│   │   │   └── websocket.js
│   │   ├── App.vue
│   │   └── main.js
│   ├── package.json
│   └── vite.config.js
├── desktop/                 # 桌面应用
│   ├── main.py             # PyWebView入口
│   ├── config.py           # 配置文件
│   └── build.py            # 打包脚本
├── legacy/                  # 现有代码模块
│   ├── main.py
│   ├── pipeline.py
│   ├── paddle_test.py
│   └── ...
└── docs/                    # 文档
    ├── api.md
    └── deployment.md
```

## 核心功能模块

### 1. 视频处理模块
- **视频合并**：支持多个视频文件的智能合并
- **视频分割**：基于字幕时间戳的智能分割
- **格式转换**：支持多种视频格式转换
- **GPU加速**：支持NVIDIA、AMD、Intel GPU加速

### 2. 水印去除模块
- **AI检测**：使用PaddleOCR进行水印检测
- **批量处理**：支持批量水印去除
- **区域选择**：支持手动选择水印区域
- **实时预览**：处理过程实时预览

### 3. 字幕处理模块
- **格式支持**：支持SRT、ASS等多种字幕格式
- **翻译功能**：集成DeepSeek API进行翻译
- **语音合成**：文本转语音功能
- **时间轴调整**：字幕时间轴编辑

### 4. 系统监控模块
- **资源监控**：CPU、GPU、内存使用率监控
- **任务管理**：任务队列和进度管理
- **日志系统**：多级别日志记录和查看
- **性能优化**：动态批处理大小调整

## 通信机制

### RESTful API
使用FastAPI构建RESTful API，提供以下端点：

```python
# 视频处理
POST /api/video/process      # 开始视频处理
GET  /api/video/status/{id}  # 获取处理状态
POST /api/video/merge        # 视频合并
POST /api/video/split        # 视频分割

# 水印处理
POST /api/watermark/detect   # 水印检测
POST /api/watermark/remove   # 水印去除

# 字幕处理
POST /api/subtitle/translate # 字幕翻译
POST /api/subtitle/synthesize # 语音合成

# 系统监控
GET  /api/system/status      # 系统状态
GET  /api/system/logs        # 获取日志
```

### WebSocket实时通信
使用FastAPI的WebSocket支持实现实时通信：

```python
# WebSocket端点
WS /ws/logs          # 实时日志推送
WS /ws/progress      # 任务进度推送
WS /ws/system        # 系统状态推送
```

## 日志系统设计

### 三层日志架构

1. **开发日志**：输出到CMD窗口
   - 级别：DEBUG及以上
   - 格式：详细的调试信息
   - 用途：开发调试

2. **持久化日志**：保存到app.log文件
   - 级别：INFO及以上
   - 格式：结构化日志
   - 用途：问题排查和审计

3. **UI日志**：推送到Web界面
   - 级别：INFO及以上
   - 格式：用户友好的消息
   - 用途：用户状态反馈

### 日志配置示例

```python
# backend/app/utils/logger.py
import logging
from logging.handlers import RotatingFileHandler
from fastapi import WebSocket

class LogManager:
    def __init__(self):
        self.setup_console_logger()
        self.setup_file_logger()
        self.websocket_clients = set()
    
    def setup_console_logger(self):
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.DEBUG)
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        console_handler.setFormatter(formatter)
        
    def setup_file_logger(self):
        file_handler = RotatingFileHandler(
            'app.log', maxBytes=10*1024*1024, backupCount=5
        )
        file_handler.setLevel(logging.INFO)
        
    async def broadcast_log(self, message: str, level: str):
        for websocket in self.websocket_clients:
            await websocket.send_json({
                "type": "log",
                "level": level,
                "message": message,
                "timestamp": datetime.now().isoformat()
            })
```

## 实施计划

### 第一阶段：基础框架搭建（1-2周）
1. 搭建FastAPI后端框架
2. 创建Vue 3前端项目
3. 配置PyWebView桌面集成
4. 实现基础的WebSocket通信
5. 搭建日志系统

### 第二阶段：核心功能迁移（2-3周）
1. 迁移视频处理核心逻辑
2. 实现水印去除功能API
3. 集成字幕处理模块
4. 开发前端界面组件
5. 实现任务管理系统

### 第三阶段：用户体验优化（1-2周）
1. 完善UI/UX设计
2. 添加拖拽上传功能
3. 实现实时进度显示
4. 优化性能和错误处理
5. 添加配置管理界面

### 第四阶段：桌面应用集成（1周）
1. 完善PyWebView集成
2. 添加系统托盘功能
3. 实现文件关联
4. 打包和分发配置
5. 测试和优化

## 技术优势

### FastAPI相比Flask的优势
1. **性能**：基于ASGI，性能比Flask高2-3倍
2. **现代化**：原生支持异步编程和类型提示
3. **文档**：自动生成API文档，便于开发和测试
4. **验证**：内置数据验证，减少错误
5. **WebSocket**：原生支持，无需额外配置

### 架构优势
1. **模块化**：清晰的分层架构，便于维护
2. **可扩展**：支持水平扩展和功能扩展
3. **用户友好**：现代化的Web界面
4. **跨平台**：支持Windows、macOS、Linux
5. **高性能**：GPU加速和多线程处理

## 部署和分发

### 开发环境
```bash
# 后端
cd backend
pip install -r requirements.txt
uvicorn app.main:app --reload

# 前端
cd frontend
npm install
npm run dev

# 桌面应用
cd desktop
python main.py
```

### 生产打包
```bash
# 前端构建
cd frontend
npm run build

# 桌面应用打包
cd desktop
pyinstaller --onefile --windowed main.py
```

## 风险评估和解决方案

### 技术风险
1. **性能风险**：大文件处理可能导致内存不足
   - 解决方案：流式处理和内存管理优化

2. **兼容性风险**：不同操作系统的兼容性问题
   - 解决方案：充分测试和条件编译

3. **依赖风险**：第三方库的版本兼容性
   - 解决方案：锁定版本和定期更新

### 用户体验风险
1. **学习成本**：从命令行到GUI的转换
   - 解决方案：提供详细文档和教程

2. **性能感知**：处理时间较长可能影响用户体验
   - 解决方案：实时进度显示和预估时间

## 总结

本方案采用FastAPI + Vue 3 + PyWebView的现代化技术栈，将现有的AI视频处理工具转换为用户友好的桌面应用。通过模块化设计、实时通信和完善的日志系统，提供高性能、易用性和可维护性的解决方案。


该方案充分利用了FastAPI的高性能和现代化特性，相比传统的Flask方案具有更好的性能表现和开发体验，能够满足AI视频处理工具的复杂需求和高性能要求。