2025-07-28 22:16:44,219 - INFO - ========== 字幕 #19 处理开始 ==========
2025-07-28 22:16:44,219 - INFO - 字幕内容: 侯爷夫妇听罢，惊觉养了十八年的女儿竟是如此蛇蝎心肠，二哥更是如遭雷击。
2025-07-28 22:16:44,219 - INFO - 字幕序号: [513, 533]
2025-07-28 22:16:44,219 - INFO - 音频文件详情:
2025-07-28 22:16:44,219 - INFO -   - 路径: output\19.wav
2025-07-28 22:16:44,219 - INFO -   - 时长: 6.16秒
2025-07-28 22:16:44,219 - INFO -   - 验证音频时长: 6.16秒
2025-07-28 22:16:44,219 - INFO - 字幕时间戳信息:
2025-07-28 22:16:44,229 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-28 22:16:44,229 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-28 22:16:44,229 - INFO -   - 根据生成的音频时长(6.16秒)已调整字幕时间戳
2025-07-28 22:16:44,229 - INFO - ========== 开始为字幕 #19 生成 6 套场景方案 ==========
2025-07-28 22:16:44,229 - INFO - 开始查找字幕序号 [513, 533] 对应的场景，共有 2710 个场景可选
2025-07-28 22:16:44,229 - INFO - 找到related_overlap场景: scene_id=536, 字幕#513
2025-07-28 22:16:44,229 - INFO - 找到related_overlap场景: scene_id=537, 字幕#513
2025-07-28 22:16:44,229 - INFO - 找到related_overlap场景: scene_id=555, 字幕#533
2025-07-28 22:16:44,230 - INFO - 字幕 #513 找到 2 个overlap场景, 0 个between场景
2025-07-28 22:16:44,230 - INFO - 字幕 #533 找到 1 个overlap场景, 0 个between场景
2025-07-28 22:16:44,230 - INFO - 共收集 3 个未使用的overlap场景和 0 个未使用的between场景
2025-07-28 22:16:44,230 - INFO - 开始生成方案 #1
2025-07-28 22:16:44,230 - INFO - 方案 #1: 为字幕#513选择初始化overlap场景id=537
2025-07-28 22:16:44,230 - INFO - 方案 #1: 为字幕#533选择初始化overlap场景id=555
2025-07-28 22:16:44,230 - INFO - 方案 #1: 初始选择后，当前总时长=5.08秒
2025-07-28 22:16:44,230 - INFO - 方案 #1: 额外添加overlap场景id=536, 当前总时长=6.56秒
2025-07-28 22:16:44,230 - INFO - 方案 #1: 额外between选择后，当前总时长=6.56秒
2025-07-28 22:16:44,230 - INFO - 方案 #1: 场景总时长(6.56秒)大于音频时长(6.16秒)，需要裁剪
2025-07-28 22:16:44,230 - INFO - 调整前总时长: 6.56秒, 目标时长: 6.16秒
2025-07-28 22:16:44,230 - INFO - 需要裁剪 0.40秒
2025-07-28 22:16:44,230 - INFO - 裁剪最长场景ID=555：从3.60秒裁剪至3.20秒
2025-07-28 22:16:44,230 - INFO - 调整后总时长: 6.16秒，与目标时长差异: 0.00秒
2025-07-28 22:16:44,230 - INFO - 方案 #1 调整/填充后最终总时长: 6.16秒
2025-07-28 22:16:44,231 - INFO - 方案 #1 添加到方案列表
2025-07-28 22:16:44,231 - INFO - 开始生成方案 #2
2025-07-28 22:16:44,231 - WARNING - 方案 #2: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:44,231 - INFO - 开始生成方案 #3
2025-07-28 22:16:44,231 - WARNING - 方案 #3: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:44,231 - INFO - 开始生成方案 #4
2025-07-28 22:16:44,231 - WARNING - 方案 #4: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:44,231 - INFO - 开始生成方案 #5
2025-07-28 22:16:44,231 - WARNING - 方案 #5: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:44,231 - INFO - 开始生成方案 #6
2025-07-28 22:16:44,231 - WARNING - 方案 #6: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:44,231 - INFO - ========== 字幕 #19 的 1 套有效场景方案生成完成 ==========
2025-07-28 22:16:44,231 - INFO - 
----- 处理字幕 #19 的方案 #1 -----
2025-07-28 22:16:44,231 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\19_1.mp4
2025-07-28 22:16:44,231 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpaykx5kvk
2025-07-28 22:16:44,232 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\537.mp4 (确认存在: True)
2025-07-28 22:16:44,232 - INFO - 添加场景ID=537，时长=1.48秒，累计时长=1.48秒
2025-07-28 22:16:44,232 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\555.mp4 (确认存在: True)
2025-07-28 22:16:44,232 - INFO - 添加场景ID=555，时长=3.60秒，累计时长=5.08秒
2025-07-28 22:16:44,232 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\536.mp4 (确认存在: True)
2025-07-28 22:16:44,232 - INFO - 添加场景ID=536，时长=1.48秒，累计时长=6.56秒
2025-07-28 22:16:44,232 - INFO - 准备合并 3 个场景文件，总时长约 6.56秒
2025-07-28 22:16:44,232 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/537.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/555.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/536.mp4'

2025-07-28 22:16:44,232 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpaykx5kvk\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpaykx5kvk\temp_combined.mp4
2025-07-28 22:16:44,455 - INFO - 合并后的视频时长: 6.63秒，目标音频时长: 6.16秒
2025-07-28 22:16:44,455 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpaykx5kvk\temp_combined.mp4 -ss 0 -to 6.157 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\19_1.mp4
2025-07-28 22:16:44,850 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 22:16:44,850 - INFO - 目标音频时长: 6.16秒
2025-07-28 22:16:44,850 - INFO - 实际视频时长: 6.18秒
2025-07-28 22:16:44,850 - INFO - 时长差异: 0.03秒 (0.42%)
2025-07-28 22:16:44,850 - INFO - ==========================================
2025-07-28 22:16:44,850 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 22:16:44,850 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\19_1.mp4
2025-07-28 22:16:44,850 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpaykx5kvk
2025-07-28 22:16:44,900 - INFO - 方案 #1 处理完成:
2025-07-28 22:16:44,900 - INFO -   - 音频时长: 6.16秒
2025-07-28 22:16:44,900 - INFO -   - 视频时长: 6.18秒
2025-07-28 22:16:44,900 - INFO -   - 时长差异: 0.03秒 (0.42%)
2025-07-28 22:16:44,900 - INFO - 
字幕 #19 处理完成，成功生成 1/1 套方案
2025-07-28 22:16:44,900 - INFO - 生成的视频文件:
2025-07-28 22:16:44,900 - INFO -   1. F:/github/aicut_auto/newcut_ai\19_1.mp4
2025-07-28 22:16:44,900 - INFO - ========== 字幕 #19 处理结束 ==========

