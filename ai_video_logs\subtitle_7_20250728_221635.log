2025-07-28 22:16:35,549 - INFO - ========== 字幕 #7 处理开始 ==========
2025-07-28 22:16:35,549 - INFO - 字幕内容: 侯爷和夫人听闻此言，非但没有责怪，反而认为这是真千金在帮假千金通便，是好心相助。
2025-07-28 22:16:35,549 - INFO - 字幕序号: [137, 141]
2025-07-28 22:16:35,549 - INFO - 音频文件详情:
2025-07-28 22:16:35,549 - INFO -   - 路径: output\7.wav
2025-07-28 22:16:35,549 - INFO -   - 时长: 5.53秒
2025-07-28 22:16:35,550 - INFO -   - 验证音频时长: 5.53秒
2025-07-28 22:16:35,550 - INFO - 字幕时间戳信息:
2025-07-28 22:16:35,559 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-28 22:16:35,559 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-28 22:16:35,559 - INFO -   - 根据生成的音频时长(5.53秒)已调整字幕时间戳
2025-07-28 22:16:35,559 - INFO - ========== 开始为字幕 #7 生成 6 套场景方案 ==========
2025-07-28 22:16:35,559 - INFO - 开始查找字幕序号 [137, 141] 对应的场景，共有 2710 个场景可选
2025-07-28 22:16:35,559 - INFO - 找到related_overlap场景: scene_id=144, 字幕#137
2025-07-28 22:16:35,560 - INFO - 找到related_overlap场景: scene_id=145, 字幕#137
2025-07-28 22:16:35,560 - INFO - 找到related_overlap场景: scene_id=148, 字幕#141
2025-07-28 22:16:35,561 - INFO - 字幕 #137 找到 2 个overlap场景, 0 个between场景
2025-07-28 22:16:35,561 - INFO - 字幕 #141 找到 1 个overlap场景, 0 个between场景
2025-07-28 22:16:35,561 - INFO - 共收集 3 个未使用的overlap场景和 0 个未使用的between场景
2025-07-28 22:16:35,561 - INFO - 开始生成方案 #1
2025-07-28 22:16:35,561 - INFO - 方案 #1: 为字幕#137选择初始化overlap场景id=144
2025-07-28 22:16:35,561 - INFO - 方案 #1: 为字幕#141选择初始化overlap场景id=148
2025-07-28 22:16:35,561 - INFO - 方案 #1: 初始选择后，当前总时长=3.88秒
2025-07-28 22:16:35,561 - INFO - 方案 #1: 额外添加overlap场景id=145, 当前总时长=5.76秒
2025-07-28 22:16:35,561 - INFO - 方案 #1: 额外between选择后，当前总时长=5.76秒
2025-07-28 22:16:35,561 - INFO - 方案 #1: 场景总时长(5.76秒)大于音频时长(5.53秒)，需要裁剪
2025-07-28 22:16:35,561 - INFO - 调整前总时长: 5.76秒, 目标时长: 5.53秒
2025-07-28 22:16:35,561 - INFO - 需要裁剪 0.23秒
2025-07-28 22:16:35,561 - INFO - 裁剪最长场景ID=148：从2.84秒裁剪至2.61秒
2025-07-28 22:16:35,561 - INFO - 调整后总时长: 5.53秒，与目标时长差异: 0.00秒
2025-07-28 22:16:35,561 - INFO - 方案 #1 调整/填充后最终总时长: 5.53秒
2025-07-28 22:16:35,561 - INFO - 方案 #1 添加到方案列表
2025-07-28 22:16:35,561 - INFO - 开始生成方案 #2
2025-07-28 22:16:35,561 - WARNING - 方案 #2: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:35,561 - INFO - 开始生成方案 #3
2025-07-28 22:16:35,561 - WARNING - 方案 #3: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:35,561 - INFO - 开始生成方案 #4
2025-07-28 22:16:35,561 - WARNING - 方案 #4: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:35,561 - INFO - 开始生成方案 #5
2025-07-28 22:16:35,561 - WARNING - 方案 #5: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:35,561 - INFO - 开始生成方案 #6
2025-07-28 22:16:35,561 - WARNING - 方案 #6: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:35,561 - INFO - ========== 字幕 #7 的 1 套有效场景方案生成完成 ==========
2025-07-28 22:16:35,561 - INFO - 
----- 处理字幕 #7 的方案 #1 -----
2025-07-28 22:16:35,561 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\7_1.mp4
2025-07-28 22:16:35,562 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpjzmpdw_d
2025-07-28 22:16:35,562 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\144.mp4 (确认存在: True)
2025-07-28 22:16:35,562 - INFO - 添加场景ID=144，时长=1.04秒，累计时长=1.04秒
2025-07-28 22:16:35,562 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\148.mp4 (确认存在: True)
2025-07-28 22:16:35,562 - INFO - 添加场景ID=148，时长=2.84秒，累计时长=3.88秒
2025-07-28 22:16:35,562 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\145.mp4 (确认存在: True)
2025-07-28 22:16:35,562 - INFO - 添加场景ID=145，时长=1.88秒，累计时长=5.76秒
2025-07-28 22:16:35,562 - INFO - 准备合并 3 个场景文件，总时长约 5.76秒
2025-07-28 22:16:35,562 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/144.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/148.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/145.mp4'

2025-07-28 22:16:35,563 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpjzmpdw_d\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpjzmpdw_d\temp_combined.mp4
2025-07-28 22:16:35,717 - INFO - 合并后的视频时长: 5.83秒，目标音频时长: 5.53秒
2025-07-28 22:16:35,717 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpjzmpdw_d\temp_combined.mp4 -ss 0 -to 5.526 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\7_1.mp4
2025-07-28 22:16:36,044 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 22:16:36,044 - INFO - 目标音频时长: 5.53秒
2025-07-28 22:16:36,044 - INFO - 实际视频时长: 5.58秒
2025-07-28 22:16:36,044 - INFO - 时长差异: 0.06秒 (1.03%)
2025-07-28 22:16:36,044 - INFO - ==========================================
2025-07-28 22:16:36,044 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 22:16:36,044 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\7_1.mp4
2025-07-28 22:16:36,044 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpjzmpdw_d
2025-07-28 22:16:36,097 - INFO - 方案 #1 处理完成:
2025-07-28 22:16:36,098 - INFO -   - 音频时长: 5.53秒
2025-07-28 22:16:36,098 - INFO -   - 视频时长: 5.58秒
2025-07-28 22:16:36,098 - INFO -   - 时长差异: 0.06秒 (1.03%)
2025-07-28 22:16:36,098 - INFO - 
字幕 #7 处理完成，成功生成 1/1 套方案
2025-07-28 22:16:36,098 - INFO - 生成的视频文件:
2025-07-28 22:16:36,098 - INFO -   1. F:/github/aicut_auto/newcut_ai\7_1.mp4
2025-07-28 22:16:36,098 - INFO - ========== 字幕 #7 处理结束 ==========

