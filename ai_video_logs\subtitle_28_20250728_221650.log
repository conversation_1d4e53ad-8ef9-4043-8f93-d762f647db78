2025-07-28 22:16:50,763 - INFO - ========== 字幕 #28 处理开始 ==========
2025-07-28 22:16:50,763 - INFO - 字幕内容: 侯爷夫人闻声大怒，这才明白养了十八年的竟是只企图反噬主人的白眼狼。
2025-07-28 22:16:50,763 - INFO - 字幕序号: [914, 918]
2025-07-28 22:16:50,763 - INFO - 音频文件详情:
2025-07-28 22:16:50,763 - INFO -   - 路径: output\28.wav
2025-07-28 22:16:50,763 - INFO -   - 时长: 5.39秒
2025-07-28 22:16:50,764 - INFO -   - 验证音频时长: 5.39秒
2025-07-28 22:16:50,764 - INFO - 字幕时间戳信息:
2025-07-28 22:16:50,764 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-28 22:16:50,764 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-28 22:16:50,764 - INFO -   - 根据生成的音频时长(5.39秒)已调整字幕时间戳
2025-07-28 22:16:50,764 - INFO - ========== 开始为字幕 #28 生成 6 套场景方案 ==========
2025-07-28 22:16:50,764 - INFO - 开始查找字幕序号 [914, 918] 对应的场景，共有 2710 个场景可选
2025-07-28 22:16:50,764 - INFO - 找到related_overlap场景: scene_id=943, 字幕#914
2025-07-28 22:16:50,764 - INFO - 找到related_overlap场景: scene_id=944, 字幕#918
2025-07-28 22:16:50,764 - INFO - 找到related_overlap场景: scene_id=945, 字幕#918
2025-07-28 22:16:50,765 - INFO - 字幕 #914 找到 1 个overlap场景, 0 个between场景
2025-07-28 22:16:50,765 - INFO - 字幕 #918 找到 2 个overlap场景, 0 个between场景
2025-07-28 22:16:50,765 - INFO - 共收集 3 个未使用的overlap场景和 0 个未使用的between场景
2025-07-28 22:16:50,765 - INFO - 开始生成方案 #1
2025-07-28 22:16:50,765 - INFO - 方案 #1: 为字幕#914选择初始化overlap场景id=943
2025-07-28 22:16:50,765 - INFO - 方案 #1: 为字幕#918选择初始化overlap场景id=945
2025-07-28 22:16:50,765 - INFO - 方案 #1: 初始选择后，当前总时长=4.92秒
2025-07-28 22:16:50,766 - INFO - 方案 #1: 额外添加overlap场景id=944, 当前总时长=7.28秒
2025-07-28 22:16:50,766 - INFO - 方案 #1: 额外between选择后，当前总时长=7.28秒
2025-07-28 22:16:50,766 - INFO - 方案 #1: 场景总时长(7.28秒)大于音频时长(5.39秒)，需要裁剪
2025-07-28 22:16:50,766 - INFO - 调整前总时长: 7.28秒, 目标时长: 5.39秒
2025-07-28 22:16:50,766 - INFO - 需要裁剪 1.89秒
2025-07-28 22:16:50,766 - INFO - 裁剪最长场景ID=943：从3.12秒裁剪至1.23秒
2025-07-28 22:16:50,766 - INFO - 调整后总时长: 5.39秒，与目标时长差异: 0.00秒
2025-07-28 22:16:50,766 - INFO - 方案 #1 调整/填充后最终总时长: 5.39秒
2025-07-28 22:16:50,766 - INFO - 方案 #1 添加到方案列表
2025-07-28 22:16:50,766 - INFO - 开始生成方案 #2
2025-07-28 22:16:50,766 - WARNING - 方案 #2: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:50,766 - INFO - 开始生成方案 #3
2025-07-28 22:16:50,766 - WARNING - 方案 #3: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:50,766 - INFO - 开始生成方案 #4
2025-07-28 22:16:50,766 - WARNING - 方案 #4: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:50,766 - INFO - 开始生成方案 #5
2025-07-28 22:16:50,766 - WARNING - 方案 #5: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:50,766 - INFO - 开始生成方案 #6
2025-07-28 22:16:50,766 - WARNING - 方案 #6: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:50,766 - INFO - ========== 字幕 #28 的 1 套有效场景方案生成完成 ==========
2025-07-28 22:16:50,766 - INFO - 
----- 处理字幕 #28 的方案 #1 -----
2025-07-28 22:16:50,766 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\28_1.mp4
2025-07-28 22:16:50,766 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpvt7l1bbx
2025-07-28 22:16:50,767 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\943.mp4 (确认存在: True)
2025-07-28 22:16:50,767 - INFO - 添加场景ID=943，时长=3.12秒，累计时长=3.12秒
2025-07-28 22:16:50,767 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\945.mp4 (确认存在: True)
2025-07-28 22:16:50,767 - INFO - 添加场景ID=945，时长=1.80秒，累计时长=4.92秒
2025-07-28 22:16:50,767 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\944.mp4 (确认存在: True)
2025-07-28 22:16:50,767 - INFO - 添加场景ID=944，时长=2.36秒，累计时长=7.28秒
2025-07-28 22:16:50,767 - INFO - 准备合并 3 个场景文件，总时长约 7.28秒
2025-07-28 22:16:50,767 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/943.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/945.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/944.mp4'

2025-07-28 22:16:50,767 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpvt7l1bbx\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpvt7l1bbx\temp_combined.mp4
2025-07-28 22:16:50,949 - INFO - 合并后的视频时长: 7.35秒，目标音频时长: 5.39秒
2025-07-28 22:16:50,949 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpvt7l1bbx\temp_combined.mp4 -ss 0 -to 5.386 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\28_1.mp4
2025-07-28 22:16:51,295 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 22:16:51,295 - INFO - 目标音频时长: 5.39秒
2025-07-28 22:16:51,295 - INFO - 实际视频时长: 5.42秒
2025-07-28 22:16:51,295 - INFO - 时长差异: 0.04秒 (0.69%)
2025-07-28 22:16:51,295 - INFO - ==========================================
2025-07-28 22:16:51,295 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 22:16:51,295 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\28_1.mp4
2025-07-28 22:16:51,296 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpvt7l1bbx
2025-07-28 22:16:51,343 - INFO - 方案 #1 处理完成:
2025-07-28 22:16:51,343 - INFO -   - 音频时长: 5.39秒
2025-07-28 22:16:51,343 - INFO -   - 视频时长: 5.42秒
2025-07-28 22:16:51,343 - INFO -   - 时长差异: 0.04秒 (0.69%)
2025-07-28 22:16:51,343 - INFO - 
字幕 #28 处理完成，成功生成 1/1 套方案
2025-07-28 22:16:51,343 - INFO - 生成的视频文件:
2025-07-28 22:16:51,343 - INFO -   1. F:/github/aicut_auto/newcut_ai\28_1.mp4
2025-07-28 22:16:51,343 - INFO - ========== 字幕 #28 处理结束 ==========

