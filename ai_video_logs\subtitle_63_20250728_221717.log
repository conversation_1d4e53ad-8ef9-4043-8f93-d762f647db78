2025-07-28 22:17:17,674 - INFO - ========== 字幕 #63 处理开始 ==========
2025-07-28 22:17:17,674 - INFO - 字幕内容: 不久后，世子亲自上门提亲，只等着将这位有趣的千金娶回家。
2025-07-28 22:17:17,674 - INFO - 字幕序号: [2898, 2899]
2025-07-28 22:17:17,674 - INFO - 音频文件详情:
2025-07-28 22:17:17,674 - INFO -   - 路径: output\63.wav
2025-07-28 22:17:17,674 - INFO -   - 时长: 4.37秒
2025-07-28 22:17:17,675 - INFO -   - 验证音频时长: 4.37秒
2025-07-28 22:17:17,675 - INFO - 字幕时间戳信息:
2025-07-28 22:17:17,675 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-28 22:17:17,675 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-28 22:17:17,675 - INFO -   - 根据生成的音频时长(4.37秒)已调整字幕时间戳
2025-07-28 22:17:17,675 - INFO - ========== 开始为字幕 #63 生成 6 套场景方案 ==========
2025-07-28 22:17:17,675 - INFO - 开始查找字幕序号 [2898, 2899] 对应的场景，共有 2710 个场景可选
2025-07-28 22:17:17,676 - INFO - 找到related_overlap场景: scene_id=2695, 字幕#2898
2025-07-28 22:17:17,676 - INFO - 找到related_overlap场景: scene_id=2696, 字幕#2899
2025-07-28 22:17:17,677 - INFO - 字幕 #2898 找到 1 个overlap场景, 0 个between场景
2025-07-28 22:17:17,677 - INFO - 字幕 #2899 找到 1 个overlap场景, 0 个between场景
2025-07-28 22:17:17,677 - INFO - 共收集 2 个未使用的overlap场景和 0 个未使用的between场景
2025-07-28 22:17:17,677 - INFO - 开始生成方案 #1
2025-07-28 22:17:17,677 - INFO - 方案 #1: 为字幕#2898选择初始化overlap场景id=2695
2025-07-28 22:17:17,677 - INFO - 方案 #1: 为字幕#2899选择初始化overlap场景id=2696
2025-07-28 22:17:17,677 - INFO - 方案 #1: 初始选择后，当前总时长=4.44秒
2025-07-28 22:17:17,677 - INFO - 方案 #1: 额外between选择后，当前总时长=4.44秒
2025-07-28 22:17:17,677 - INFO - 方案 #1: 场景总时长(4.44秒)大于音频时长(4.37秒)，需要裁剪
2025-07-28 22:17:17,677 - INFO - 调整前总时长: 4.44秒, 目标时长: 4.37秒
2025-07-28 22:17:17,677 - INFO - 需要裁剪 0.07秒
2025-07-28 22:17:17,677 - INFO - 裁剪最长场景ID=2696：从2.28秒裁剪至2.21秒
2025-07-28 22:17:17,677 - INFO - 调整后总时长: 4.37秒，与目标时长差异: 0.00秒
2025-07-28 22:17:17,677 - INFO - 方案 #1 调整/填充后最终总时长: 4.37秒
2025-07-28 22:17:17,677 - INFO - 方案 #1 添加到方案列表
2025-07-28 22:17:17,677 - INFO - 开始生成方案 #2
2025-07-28 22:17:17,677 - WARNING - 方案 #2: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:17:17,677 - INFO - 开始生成方案 #3
2025-07-28 22:17:17,677 - WARNING - 方案 #3: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:17:17,677 - INFO - 开始生成方案 #4
2025-07-28 22:17:17,677 - WARNING - 方案 #4: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:17:17,677 - INFO - 开始生成方案 #5
2025-07-28 22:17:17,677 - WARNING - 方案 #5: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:17:17,677 - INFO - 开始生成方案 #6
2025-07-28 22:17:17,677 - WARNING - 方案 #6: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:17:17,677 - INFO - ========== 字幕 #63 的 1 套有效场景方案生成完成 ==========
2025-07-28 22:17:17,677 - INFO - 
----- 处理字幕 #63 的方案 #1 -----
2025-07-28 22:17:17,677 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\63_1.mp4
2025-07-28 22:17:17,678 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpgxf21w_7
2025-07-28 22:17:17,679 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2695.mp4 (确认存在: True)
2025-07-28 22:17:17,679 - INFO - 添加场景ID=2695，时长=2.16秒，累计时长=2.16秒
2025-07-28 22:17:17,679 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2696.mp4 (确认存在: True)
2025-07-28 22:17:17,679 - INFO - 添加场景ID=2696，时长=2.28秒，累计时长=4.44秒
2025-07-28 22:17:17,679 - INFO - 准备合并 2 个场景文件，总时长约 4.44秒
2025-07-28 22:17:17,679 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2695.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2696.mp4'

2025-07-28 22:17:17,679 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpgxf21w_7\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpgxf21w_7\temp_combined.mp4
2025-07-28 22:17:17,825 - INFO - 合并后的视频时长: 4.49秒，目标音频时长: 4.37秒
2025-07-28 22:17:17,825 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpgxf21w_7\temp_combined.mp4 -ss 0 -to 4.374 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\63_1.mp4
2025-07-28 22:17:18,125 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 22:17:18,125 - INFO - 目标音频时长: 4.37秒
2025-07-28 22:17:18,125 - INFO - 实际视频时长: 4.42秒
2025-07-28 22:17:18,125 - INFO - 时长差异: 0.05秒 (1.12%)
2025-07-28 22:17:18,125 - INFO - ==========================================
2025-07-28 22:17:18,125 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 22:17:18,125 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\63_1.mp4
2025-07-28 22:17:18,126 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpgxf21w_7
2025-07-28 22:17:18,180 - INFO - 方案 #1 处理完成:
2025-07-28 22:17:18,180 - INFO -   - 音频时长: 4.37秒
2025-07-28 22:17:18,180 - INFO -   - 视频时长: 4.42秒
2025-07-28 22:17:18,180 - INFO -   - 时长差异: 0.05秒 (1.12%)
2025-07-28 22:17:18,180 - INFO - 
字幕 #63 处理完成，成功生成 1/1 套方案
2025-07-28 22:17:18,180 - INFO - 生成的视频文件:
2025-07-28 22:17:18,180 - INFO -   1. F:/github/aicut_auto/newcut_ai\63_1.mp4
2025-07-28 22:17:18,180 - INFO - ========== 字幕 #63 处理结束 ==========

