2025-07-28 22:16:38,229 - INFO - ========== 字幕 #11 处理开始 ==========
2025-07-28 22:16:38,229 - INFO - 字幕内容: 她悄然改动心声，内容竟变成侯爷私下准备了礼物，被送给了名叫“柳月”的丫鬟！
2025-07-28 22:16:38,229 - INFO - 字幕序号: [295, 296]
2025-07-28 22:16:38,229 - INFO - 音频文件详情:
2025-07-28 22:16:38,229 - INFO -   - 路径: output\11.wav
2025-07-28 22:16:38,229 - INFO -   - 时长: 6.56秒
2025-07-28 22:16:38,229 - INFO -   - 验证音频时长: 6.56秒
2025-07-28 22:16:38,229 - INFO - 字幕时间戳信息:
2025-07-28 22:16:38,229 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-28 22:16:38,229 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-28 22:16:38,229 - INFO -   - 根据生成的音频时长(6.56秒)已调整字幕时间戳
2025-07-28 22:16:38,229 - INFO - ========== 开始为字幕 #11 生成 6 套场景方案 ==========
2025-07-28 22:16:38,229 - INFO - 开始查找字幕序号 [295, 296] 对应的场景，共有 2710 个场景可选
2025-07-28 22:16:38,229 - INFO - 找到related_overlap场景: scene_id=317, 字幕#295
2025-07-28 22:16:38,230 - INFO - 找到related_between场景: scene_id=316, 字幕#295
2025-07-28 22:16:38,230 - INFO - 字幕 #295 找到 1 个overlap场景, 1 个between场景
2025-07-28 22:16:38,230 - INFO - 字幕 #296 找到 0 个overlap场景, 0 个between场景
2025-07-28 22:16:38,230 - WARNING - 字幕 #296 没有找到任何匹配场景!
2025-07-28 22:16:38,230 - INFO - 共收集 1 个未使用的overlap场景和 1 个未使用的between场景
2025-07-28 22:16:38,230 - INFO - 开始生成方案 #1
2025-07-28 22:16:38,230 - INFO - 方案 #1: 为字幕#295选择初始化overlap场景id=317
2025-07-28 22:16:38,230 - INFO - 方案 #1: 初始选择后，当前总时长=3.24秒
2025-07-28 22:16:38,230 - INFO - 方案 #1: 额外between选择后，当前总时长=3.24秒
2025-07-28 22:16:38,230 - INFO - 方案 #1: 额外添加between场景id=316, 当前总时长=6.48秒
2025-07-28 22:16:38,230 - INFO - 方案 #1: 场景总时长(6.48秒)小于音频时长(6.56秒)，需要延伸填充
2025-07-28 22:16:38,230 - INFO - 方案 #1: 最后一个场景ID: 316
2025-07-28 22:16:38,230 - INFO - 方案 #1: 找到最后一个场景在原始列表中的索引: 315
2025-07-28 22:16:38,230 - INFO - 方案 #1: 需要填充时长: 0.08秒
2025-07-28 22:16:38,230 - INFO - 方案 #1: 跳过已使用的场景: scene_id=317
2025-07-28 22:16:38,230 - INFO - 方案 #1: 追加场景 scene_id=318 (裁剪至 0.08秒)
2025-07-28 22:16:38,230 - INFO - 方案 #1: 成功填充至目标时长
2025-07-28 22:16:38,230 - INFO - 方案 #1 调整/填充后最终总时长: 6.56秒
2025-07-28 22:16:38,230 - INFO - 方案 #1 添加到方案列表
2025-07-28 22:16:38,230 - INFO - 开始生成方案 #2
2025-07-28 22:16:38,231 - WARNING - 方案 #2: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:38,231 - INFO - 开始生成方案 #3
2025-07-28 22:16:38,231 - WARNING - 方案 #3: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:38,231 - INFO - 开始生成方案 #4
2025-07-28 22:16:38,231 - WARNING - 方案 #4: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:38,231 - INFO - 开始生成方案 #5
2025-07-28 22:16:38,231 - WARNING - 方案 #5: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:38,231 - INFO - 开始生成方案 #6
2025-07-28 22:16:38,231 - WARNING - 方案 #6: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:38,231 - INFO - ========== 字幕 #11 的 1 套有效场景方案生成完成 ==========
2025-07-28 22:16:38,231 - INFO - 
----- 处理字幕 #11 的方案 #1 -----
2025-07-28 22:16:38,231 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\11_1.mp4
2025-07-28 22:16:38,231 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp2iu9qdfu
2025-07-28 22:16:38,231 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\317.mp4 (确认存在: True)
2025-07-28 22:16:38,231 - INFO - 添加场景ID=317，时长=3.24秒，累计时长=3.24秒
2025-07-28 22:16:38,232 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\316.mp4 (确认存在: True)
2025-07-28 22:16:38,232 - INFO - 添加场景ID=316，时长=3.24秒，累计时长=6.48秒
2025-07-28 22:16:38,232 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\318.mp4 (确认存在: True)
2025-07-28 22:16:38,232 - INFO - 添加场景ID=318，时长=1.20秒，累计时长=7.68秒
2025-07-28 22:16:38,232 - INFO - 准备合并 3 个场景文件，总时长约 7.68秒
2025-07-28 22:16:38,232 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/317.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/316.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/318.mp4'

2025-07-28 22:16:38,232 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp2iu9qdfu\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp2iu9qdfu\temp_combined.mp4
2025-07-28 22:16:38,386 - INFO - 合并后的视频时长: 7.75秒，目标音频时长: 6.56秒
2025-07-28 22:16:38,386 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp2iu9qdfu\temp_combined.mp4 -ss 0 -to 6.557 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\11_1.mp4
2025-07-28 22:16:38,763 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 22:16:38,763 - INFO - 目标音频时长: 6.56秒
2025-07-28 22:16:38,763 - INFO - 实际视频时长: 6.58秒
2025-07-28 22:16:38,763 - INFO - 时长差异: 0.03秒 (0.40%)
2025-07-28 22:16:38,763 - INFO - ==========================================
2025-07-28 22:16:38,764 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 22:16:38,764 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\11_1.mp4
2025-07-28 22:16:38,764 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp2iu9qdfu
2025-07-28 22:16:38,809 - INFO - 方案 #1 处理完成:
2025-07-28 22:16:38,809 - INFO -   - 音频时长: 6.56秒
2025-07-28 22:16:38,809 - INFO -   - 视频时长: 6.58秒
2025-07-28 22:16:38,809 - INFO -   - 时长差异: 0.03秒 (0.40%)
2025-07-28 22:16:38,809 - INFO - 
字幕 #11 处理完成，成功生成 1/1 套方案
2025-07-28 22:16:38,809 - INFO - 生成的视频文件:
2025-07-28 22:16:38,809 - INFO -   1. F:/github/aicut_auto/newcut_ai\11_1.mp4
2025-07-28 22:16:38,809 - INFO - ========== 字幕 #11 处理结束 ==========

