2025-07-28 22:16:37,210 - INFO - ========== 字幕 #10 处理开始 ==========
2025-07-28 22:16:37,210 - INFO - 字幕内容: 老祖冷笑一声，正好系统升级，能修改的字数又多了一个，这下有好戏看了。
2025-07-28 22:16:37,210 - INFO - 字幕序号: [293, 294]
2025-07-28 22:16:37,211 - INFO - 音频文件详情:
2025-07-28 22:16:37,211 - INFO -   - 路径: output\10.wav
2025-07-28 22:16:37,211 - INFO -   - 时长: 4.74秒
2025-07-28 22:16:37,211 - INFO -   - 验证音频时长: 4.74秒
2025-07-28 22:16:37,211 - INFO - 字幕时间戳信息:
2025-07-28 22:16:37,220 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-28 22:16:37,220 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-28 22:16:37,220 - INFO -   - 根据生成的音频时长(4.74秒)已调整字幕时间戳
2025-07-28 22:16:37,220 - INFO - ========== 开始为字幕 #10 生成 6 套场景方案 ==========
2025-07-28 22:16:37,220 - INFO - 开始查找字幕序号 [293, 294] 对应的场景，共有 2710 个场景可选
2025-07-28 22:16:37,220 - INFO - 找到related_overlap场景: scene_id=314, 字幕#293
2025-07-28 22:16:37,220 - INFO - 找到related_overlap场景: scene_id=315, 字幕#294
2025-07-28 22:16:37,221 - INFO - 找到related_between场景: scene_id=316, 字幕#294
2025-07-28 22:16:37,222 - INFO - 字幕 #293 找到 1 个overlap场景, 0 个between场景
2025-07-28 22:16:37,222 - INFO - 字幕 #294 找到 1 个overlap场景, 1 个between场景
2025-07-28 22:16:37,222 - INFO - 共收集 2 个未使用的overlap场景和 1 个未使用的between场景
2025-07-28 22:16:37,222 - INFO - 开始生成方案 #1
2025-07-28 22:16:37,222 - INFO - 方案 #1: 为字幕#293选择初始化overlap场景id=314
2025-07-28 22:16:37,222 - INFO - 方案 #1: 为字幕#294选择初始化overlap场景id=315
2025-07-28 22:16:37,222 - INFO - 方案 #1: 初始选择后，当前总时长=4.88秒
2025-07-28 22:16:37,222 - INFO - 方案 #1: 额外between选择后，当前总时长=4.88秒
2025-07-28 22:16:37,222 - INFO - 方案 #1: 场景总时长(4.88秒)大于音频时长(4.74秒)，需要裁剪
2025-07-28 22:16:37,222 - INFO - 调整前总时长: 4.88秒, 目标时长: 4.74秒
2025-07-28 22:16:37,222 - INFO - 需要裁剪 0.14秒
2025-07-28 22:16:37,222 - INFO - 裁剪最长场景ID=314：从3.08秒裁剪至2.94秒
2025-07-28 22:16:37,222 - INFO - 调整后总时长: 4.74秒，与目标时长差异: 0.00秒
2025-07-28 22:16:37,222 - INFO - 方案 #1 调整/填充后最终总时长: 4.74秒
2025-07-28 22:16:37,222 - INFO - 方案 #1 添加到方案列表
2025-07-28 22:16:37,222 - INFO - 开始生成方案 #2
2025-07-28 22:16:37,222 - INFO - 方案 #2: 初始选择后，当前总时长=0.00秒
2025-07-28 22:16:37,222 - INFO - 方案 #2: 为字幕#294选择初始化between场景id=316
2025-07-28 22:16:37,222 - INFO - 方案 #2: 额外between选择后，当前总时长=3.24秒
2025-07-28 22:16:37,222 - INFO - 方案 #2: 场景总时长(3.24秒)小于音频时长(4.74秒)，需要延伸填充
2025-07-28 22:16:37,222 - INFO - 方案 #2: 最后一个场景ID: 316
2025-07-28 22:16:37,222 - INFO - 方案 #2: 找到最后一个场景在原始列表中的索引: 315
2025-07-28 22:16:37,222 - INFO - 方案 #2: 需要填充时长: 1.50秒
2025-07-28 22:16:37,222 - INFO - 方案 #2: 追加场景 scene_id=317 (裁剪至 1.50秒)
2025-07-28 22:16:37,222 - INFO - 方案 #2: 成功填充至目标时长
2025-07-28 22:16:37,222 - INFO - 方案 #2 调整/填充后最终总时长: 4.74秒
2025-07-28 22:16:37,222 - INFO - 方案 #2 添加到方案列表
2025-07-28 22:16:37,222 - INFO - 开始生成方案 #3
2025-07-28 22:16:37,222 - WARNING - 方案 #3: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:37,222 - INFO - 开始生成方案 #4
2025-07-28 22:16:37,222 - WARNING - 方案 #4: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:37,222 - INFO - 开始生成方案 #5
2025-07-28 22:16:37,222 - WARNING - 方案 #5: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:37,222 - INFO - 开始生成方案 #6
2025-07-28 22:16:37,222 - WARNING - 方案 #6: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:37,222 - INFO - ========== 字幕 #10 的 2 套有效场景方案生成完成 ==========
2025-07-28 22:16:37,222 - INFO - 
----- 处理字幕 #10 的方案 #1 -----
2025-07-28 22:16:37,222 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\10_1.mp4
2025-07-28 22:16:37,223 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp6m9c2pxd
2025-07-28 22:16:37,223 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\314.mp4 (确认存在: True)
2025-07-28 22:16:37,223 - INFO - 添加场景ID=314，时长=3.08秒，累计时长=3.08秒
2025-07-28 22:16:37,223 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\315.mp4 (确认存在: True)
2025-07-28 22:16:37,223 - INFO - 添加场景ID=315，时长=1.80秒，累计时长=4.88秒
2025-07-28 22:16:37,224 - INFO - 准备合并 2 个场景文件，总时长约 4.88秒
2025-07-28 22:16:37,224 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/314.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/315.mp4'

2025-07-28 22:16:37,224 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp6m9c2pxd\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp6m9c2pxd\temp_combined.mp4
2025-07-28 22:16:37,352 - INFO - 合并后的视频时长: 4.93秒，目标音频时长: 4.74秒
2025-07-28 22:16:37,352 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp6m9c2pxd\temp_combined.mp4 -ss 0 -to 4.736 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\10_1.mp4
2025-07-28 22:16:37,653 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 22:16:37,653 - INFO - 目标音频时长: 4.74秒
2025-07-28 22:16:37,653 - INFO - 实际视频时长: 4.78秒
2025-07-28 22:16:37,653 - INFO - 时长差异: 0.05秒 (0.99%)
2025-07-28 22:16:37,653 - INFO - ==========================================
2025-07-28 22:16:37,654 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 22:16:37,654 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\10_1.mp4
2025-07-28 22:16:37,654 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp6m9c2pxd
2025-07-28 22:16:37,697 - INFO - 方案 #1 处理完成:
2025-07-28 22:16:37,697 - INFO -   - 音频时长: 4.74秒
2025-07-28 22:16:37,697 - INFO -   - 视频时长: 4.78秒
2025-07-28 22:16:37,697 - INFO -   - 时长差异: 0.05秒 (0.99%)
2025-07-28 22:16:37,697 - INFO - 
----- 处理字幕 #10 的方案 #2 -----
2025-07-28 22:16:37,697 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\10_2.mp4
2025-07-28 22:16:37,698 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpyh9prsli
2025-07-28 22:16:37,698 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\316.mp4 (确认存在: True)
2025-07-28 22:16:37,698 - INFO - 添加场景ID=316，时长=3.24秒，累计时长=3.24秒
2025-07-28 22:16:37,698 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\317.mp4 (确认存在: True)
2025-07-28 22:16:37,698 - INFO - 添加场景ID=317，时长=3.24秒，累计时长=6.48秒
2025-07-28 22:16:37,698 - INFO - 准备合并 2 个场景文件，总时长约 6.48秒
2025-07-28 22:16:37,699 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/316.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/317.mp4'

2025-07-28 22:16:37,699 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpyh9prsli\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpyh9prsli\temp_combined.mp4
2025-07-28 22:16:37,824 - INFO - 合并后的视频时长: 6.53秒，目标音频时长: 4.74秒
2025-07-28 22:16:37,824 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpyh9prsli\temp_combined.mp4 -ss 0 -to 4.736 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\10_2.mp4
2025-07-28 22:16:38,174 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 22:16:38,174 - INFO - 目标音频时长: 4.74秒
2025-07-28 22:16:38,174 - INFO - 实际视频时长: 4.78秒
2025-07-28 22:16:38,174 - INFO - 时长差异: 0.05秒 (0.99%)
2025-07-28 22:16:38,174 - INFO - ==========================================
2025-07-28 22:16:38,174 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 22:16:38,174 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\10_2.mp4
2025-07-28 22:16:38,175 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpyh9prsli
2025-07-28 22:16:38,219 - INFO - 方案 #2 处理完成:
2025-07-28 22:16:38,219 - INFO -   - 音频时长: 4.74秒
2025-07-28 22:16:38,219 - INFO -   - 视频时长: 4.78秒
2025-07-28 22:16:38,219 - INFO -   - 时长差异: 0.05秒 (0.99%)
2025-07-28 22:16:38,219 - INFO - 
字幕 #10 处理完成，成功生成 2/2 套方案
2025-07-28 22:16:38,219 - INFO - 生成的视频文件:
2025-07-28 22:16:38,219 - INFO -   1. F:/github/aicut_auto/newcut_ai\10_1.mp4
2025-07-28 22:16:38,219 - INFO -   2. F:/github/aicut_auto/newcut_ai\10_2.mp4
2025-07-28 22:16:38,219 - INFO - ========== 字幕 #10 处理结束 ==========

