2025-07-28 22:16:45,436 - INFO - ========== 字幕 #21 处理开始 ==========
2025-07-28 22:16:45,436 - INFO - 字幕内容: 宴会上，心机女故作摔倒想扑进国公世子怀中，被老祖识破后，她竟反咬一口是姐姐推了她。
2025-07-28 22:16:45,436 - INFO - 字幕序号: [703, 713]
2025-07-28 22:16:45,436 - INFO - 音频文件详情:
2025-07-28 22:16:45,436 - INFO -   - 路径: output\21.wav
2025-07-28 22:16:45,436 - INFO -   - 时长: 5.67秒
2025-07-28 22:16:45,436 - INFO -   - 验证音频时长: 5.67秒
2025-07-28 22:16:45,436 - INFO - 字幕时间戳信息:
2025-07-28 22:16:45,446 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-28 22:16:45,446 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-28 22:16:45,446 - INFO -   - 根据生成的音频时长(5.67秒)已调整字幕时间戳
2025-07-28 22:16:45,446 - INFO - ========== 开始为字幕 #21 生成 6 套场景方案 ==========
2025-07-28 22:16:45,446 - INFO - 开始查找字幕序号 [703, 713] 对应的场景，共有 2710 个场景可选
2025-07-28 22:16:45,446 - INFO - 找到related_overlap场景: scene_id=715, 字幕#703
2025-07-28 22:16:45,446 - INFO - 找到related_overlap场景: scene_id=738, 字幕#713
2025-07-28 22:16:45,447 - INFO - 字幕 #703 找到 1 个overlap场景, 0 个between场景
2025-07-28 22:16:45,447 - INFO - 字幕 #713 找到 1 个overlap场景, 0 个between场景
2025-07-28 22:16:45,447 - INFO - 共收集 2 个未使用的overlap场景和 0 个未使用的between场景
2025-07-28 22:16:45,447 - INFO - 开始生成方案 #1
2025-07-28 22:16:45,447 - INFO - 方案 #1: 为字幕#703选择初始化overlap场景id=715
2025-07-28 22:16:45,447 - INFO - 方案 #1: 为字幕#713选择初始化overlap场景id=738
2025-07-28 22:16:45,447 - INFO - 方案 #1: 初始选择后，当前总时长=5.96秒
2025-07-28 22:16:45,447 - INFO - 方案 #1: 额外between选择后，当前总时长=5.96秒
2025-07-28 22:16:45,447 - INFO - 方案 #1: 场景总时长(5.96秒)大于音频时长(5.67秒)，需要裁剪
2025-07-28 22:16:45,447 - INFO - 调整前总时长: 5.96秒, 目标时长: 5.67秒
2025-07-28 22:16:45,447 - INFO - 需要裁剪 0.29秒
2025-07-28 22:16:45,447 - INFO - 裁剪最长场景ID=738：从3.88秒裁剪至3.59秒
2025-07-28 22:16:45,447 - INFO - 调整后总时长: 5.67秒，与目标时长差异: 0.00秒
2025-07-28 22:16:45,447 - INFO - 方案 #1 调整/填充后最终总时长: 5.67秒
2025-07-28 22:16:45,447 - INFO - 方案 #1 添加到方案列表
2025-07-28 22:16:45,447 - INFO - 开始生成方案 #2
2025-07-28 22:16:45,447 - WARNING - 方案 #2: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:45,447 - INFO - 开始生成方案 #3
2025-07-28 22:16:45,447 - WARNING - 方案 #3: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:45,447 - INFO - 开始生成方案 #4
2025-07-28 22:16:45,447 - WARNING - 方案 #4: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:45,447 - INFO - 开始生成方案 #5
2025-07-28 22:16:45,447 - WARNING - 方案 #5: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:45,447 - INFO - 开始生成方案 #6
2025-07-28 22:16:45,447 - WARNING - 方案 #6: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:45,447 - INFO - ========== 字幕 #21 的 1 套有效场景方案生成完成 ==========
2025-07-28 22:16:45,447 - INFO - 
----- 处理字幕 #21 的方案 #1 -----
2025-07-28 22:16:45,447 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\21_1.mp4
2025-07-28 22:16:45,447 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpggf41d_8
2025-07-28 22:16:45,448 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\715.mp4 (确认存在: True)
2025-07-28 22:16:45,448 - INFO - 添加场景ID=715，时长=2.08秒，累计时长=2.08秒
2025-07-28 22:16:45,448 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\738.mp4 (确认存在: True)
2025-07-28 22:16:45,448 - INFO - 添加场景ID=738，时长=3.88秒，累计时长=5.96秒
2025-07-28 22:16:45,448 - INFO - 准备合并 2 个场景文件，总时长约 5.96秒
2025-07-28 22:16:45,448 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/715.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/738.mp4'

2025-07-28 22:16:45,448 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpggf41d_8\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpggf41d_8\temp_combined.mp4
2025-07-28 22:16:45,576 - INFO - 合并后的视频时长: 6.01秒，目标音频时长: 5.67秒
2025-07-28 22:16:45,576 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpggf41d_8\temp_combined.mp4 -ss 0 -to 5.673 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\21_1.mp4
2025-07-28 22:16:45,933 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 22:16:45,934 - INFO - 目标音频时长: 5.67秒
2025-07-28 22:16:45,934 - INFO - 实际视频时长: 5.70秒
2025-07-28 22:16:45,934 - INFO - 时长差异: 0.03秒 (0.53%)
2025-07-28 22:16:45,934 - INFO - ==========================================
2025-07-28 22:16:45,934 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 22:16:45,934 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\21_1.mp4
2025-07-28 22:16:45,934 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpggf41d_8
2025-07-28 22:16:45,977 - INFO - 方案 #1 处理完成:
2025-07-28 22:16:45,977 - INFO -   - 音频时长: 5.67秒
2025-07-28 22:16:45,977 - INFO -   - 视频时长: 5.70秒
2025-07-28 22:16:45,977 - INFO -   - 时长差异: 0.03秒 (0.53%)
2025-07-28 22:16:45,978 - INFO - 
字幕 #21 处理完成，成功生成 1/1 套方案
2025-07-28 22:16:45,978 - INFO - 生成的视频文件:
2025-07-28 22:16:45,978 - INFO -   1. F:/github/aicut_auto/newcut_ai\21_1.mp4
2025-07-28 22:16:45,978 - INFO - ========== 字幕 #21 处理结束 ==========

