2025-07-28 22:16:48,067 - INFO - ========== 字幕 #25 处理开始 ==========
2025-07-28 22:16:48,068 - INFO - 字幕内容: 她笃定地宣称，符咒就藏在真千金身上，要求立刻搜身。
2025-07-28 22:16:48,068 - INFO - 字幕序号: [854, 857]
2025-07-28 22:16:48,068 - INFO - 音频文件详情:
2025-07-28 22:16:48,068 - INFO -   - 路径: output\25.wav
2025-07-28 22:16:48,068 - INFO -   - 时长: 4.25秒
2025-07-28 22:16:48,068 - INFO -   - 验证音频时长: 4.25秒
2025-07-28 22:16:48,068 - INFO - 字幕时间戳信息:
2025-07-28 22:16:48,068 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-28 22:16:48,077 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-28 22:16:48,077 - INFO -   - 根据生成的音频时长(4.25秒)已调整字幕时间戳
2025-07-28 22:16:48,077 - INFO - ========== 开始为字幕 #25 生成 6 套场景方案 ==========
2025-07-28 22:16:48,078 - INFO - 开始查找字幕序号 [854, 857] 对应的场景，共有 2710 个场景可选
2025-07-28 22:16:48,078 - INFO - 找到related_overlap场景: scene_id=882, 字幕#854
2025-07-28 22:16:48,078 - INFO - 找到related_overlap场景: scene_id=883, 字幕#854
2025-07-28 22:16:48,078 - INFO - 找到related_overlap场景: scene_id=886, 字幕#857
2025-07-28 22:16:48,078 - INFO - 找到related_between场景: scene_id=887, 字幕#857
2025-07-28 22:16:48,079 - INFO - 字幕 #854 找到 2 个overlap场景, 0 个between场景
2025-07-28 22:16:48,079 - INFO - 字幕 #857 找到 1 个overlap场景, 1 个between场景
2025-07-28 22:16:48,079 - INFO - 共收集 3 个未使用的overlap场景和 1 个未使用的between场景
2025-07-28 22:16:48,079 - INFO - 开始生成方案 #1
2025-07-28 22:16:48,079 - INFO - 方案 #1: 为字幕#854选择初始化overlap场景id=882
2025-07-28 22:16:48,079 - INFO - 方案 #1: 为字幕#857选择初始化overlap场景id=886
2025-07-28 22:16:48,079 - INFO - 方案 #1: 初始选择后，当前总时长=5.04秒
2025-07-28 22:16:48,079 - INFO - 方案 #1: 额外between选择后，当前总时长=5.04秒
2025-07-28 22:16:48,079 - INFO - 方案 #1: 场景总时长(5.04秒)大于音频时长(4.25秒)，需要裁剪
2025-07-28 22:16:48,079 - INFO - 调整前总时长: 5.04秒, 目标时长: 4.25秒
2025-07-28 22:16:48,079 - INFO - 需要裁剪 0.79秒
2025-07-28 22:16:48,079 - INFO - 裁剪最长场景ID=882：从3.40秒裁剪至2.61秒
2025-07-28 22:16:48,079 - INFO - 调整后总时长: 4.25秒，与目标时长差异: 0.00秒
2025-07-28 22:16:48,079 - INFO - 方案 #1 调整/填充后最终总时长: 4.25秒
2025-07-28 22:16:48,079 - INFO - 方案 #1 添加到方案列表
2025-07-28 22:16:48,079 - INFO - 开始生成方案 #2
2025-07-28 22:16:48,079 - INFO - 方案 #2: 为字幕#854选择初始化overlap场景id=883
2025-07-28 22:16:48,079 - INFO - 方案 #2: 初始选择后，当前总时长=2.24秒
2025-07-28 22:16:48,079 - INFO - 方案 #2: 为字幕#857选择初始化between场景id=887
2025-07-28 22:16:48,079 - INFO - 方案 #2: 额外between选择后，当前总时长=3.52秒
2025-07-28 22:16:48,079 - INFO - 方案 #2: 场景总时长(3.52秒)小于音频时长(4.25秒)，需要延伸填充
2025-07-28 22:16:48,079 - INFO - 方案 #2: 最后一个场景ID: 887
2025-07-28 22:16:48,079 - INFO - 方案 #2: 找到最后一个场景在原始列表中的索引: 886
2025-07-28 22:16:48,079 - INFO - 方案 #2: 需要填充时长: 0.73秒
2025-07-28 22:16:48,079 - INFO - 方案 #2: 追加场景 scene_id=888 (裁剪至 0.73秒)
2025-07-28 22:16:48,079 - INFO - 方案 #2: 成功填充至目标时长
2025-07-28 22:16:48,079 - INFO - 方案 #2 调整/填充后最终总时长: 4.25秒
2025-07-28 22:16:48,079 - INFO - 方案 #2 添加到方案列表
2025-07-28 22:16:48,079 - INFO - 开始生成方案 #3
2025-07-28 22:16:48,079 - WARNING - 方案 #3: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:48,079 - INFO - 开始生成方案 #4
2025-07-28 22:16:48,079 - WARNING - 方案 #4: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:48,079 - INFO - 开始生成方案 #5
2025-07-28 22:16:48,079 - WARNING - 方案 #5: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:48,079 - INFO - 开始生成方案 #6
2025-07-28 22:16:48,079 - WARNING - 方案 #6: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:48,079 - INFO - ========== 字幕 #25 的 2 套有效场景方案生成完成 ==========
2025-07-28 22:16:48,079 - INFO - 
----- 处理字幕 #25 的方案 #1 -----
2025-07-28 22:16:48,079 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\25_1.mp4
2025-07-28 22:16:48,080 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpije4ria_
2025-07-28 22:16:48,080 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\882.mp4 (确认存在: True)
2025-07-28 22:16:48,080 - INFO - 添加场景ID=882，时长=3.40秒，累计时长=3.40秒
2025-07-28 22:16:48,081 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\886.mp4 (确认存在: True)
2025-07-28 22:16:48,081 - INFO - 添加场景ID=886，时长=1.64秒，累计时长=5.04秒
2025-07-28 22:16:48,081 - INFO - 准备合并 2 个场景文件，总时长约 5.04秒
2025-07-28 22:16:48,081 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/882.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/886.mp4'

2025-07-28 22:16:48,081 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpije4ria_\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpije4ria_\temp_combined.mp4
2025-07-28 22:16:48,221 - INFO - 合并后的视频时长: 5.09秒，目标音频时长: 4.25秒
2025-07-28 22:16:48,221 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpije4ria_\temp_combined.mp4 -ss 0 -to 4.249 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\25_1.mp4
2025-07-28 22:16:48,519 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 22:16:48,519 - INFO - 目标音频时长: 4.25秒
2025-07-28 22:16:48,519 - INFO - 实际视频时长: 4.30秒
2025-07-28 22:16:48,519 - INFO - 时长差异: 0.05秒 (1.27%)
2025-07-28 22:16:48,519 - INFO - ==========================================
2025-07-28 22:16:48,519 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 22:16:48,519 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\25_1.mp4
2025-07-28 22:16:48,520 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpije4ria_
2025-07-28 22:16:48,566 - INFO - 方案 #1 处理完成:
2025-07-28 22:16:48,566 - INFO -   - 音频时长: 4.25秒
2025-07-28 22:16:48,566 - INFO -   - 视频时长: 4.30秒
2025-07-28 22:16:48,566 - INFO -   - 时长差异: 0.05秒 (1.27%)
2025-07-28 22:16:48,566 - INFO - 
----- 处理字幕 #25 的方案 #2 -----
2025-07-28 22:16:48,566 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\25_2.mp4
2025-07-28 22:16:48,566 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpt_z_qvme
2025-07-28 22:16:48,567 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\883.mp4 (确认存在: True)
2025-07-28 22:16:48,567 - INFO - 添加场景ID=883，时长=2.24秒，累计时长=2.24秒
2025-07-28 22:16:48,567 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\887.mp4 (确认存在: True)
2025-07-28 22:16:48,567 - INFO - 添加场景ID=887，时长=1.28秒，累计时长=3.52秒
2025-07-28 22:16:48,567 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\888.mp4 (确认存在: True)
2025-07-28 22:16:48,567 - INFO - 添加场景ID=888，时长=1.32秒，累计时长=4.84秒
2025-07-28 22:16:48,567 - INFO - 准备合并 3 个场景文件，总时长约 4.84秒
2025-07-28 22:16:48,567 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/883.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/887.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/888.mp4'

2025-07-28 22:16:48,567 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpt_z_qvme\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpt_z_qvme\temp_combined.mp4
2025-07-28 22:16:48,744 - INFO - 合并后的视频时长: 4.91秒，目标音频时长: 4.25秒
2025-07-28 22:16:48,744 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpt_z_qvme\temp_combined.mp4 -ss 0 -to 4.249 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\25_2.mp4
2025-07-28 22:16:49,065 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 22:16:49,065 - INFO - 目标音频时长: 4.25秒
2025-07-28 22:16:49,065 - INFO - 实际视频时长: 4.30秒
2025-07-28 22:16:49,065 - INFO - 时长差异: 0.05秒 (1.27%)
2025-07-28 22:16:49,065 - INFO - ==========================================
2025-07-28 22:16:49,065 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 22:16:49,065 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\25_2.mp4
2025-07-28 22:16:49,066 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpt_z_qvme
2025-07-28 22:16:49,109 - INFO - 方案 #2 处理完成:
2025-07-28 22:16:49,109 - INFO -   - 音频时长: 4.25秒
2025-07-28 22:16:49,109 - INFO -   - 视频时长: 4.30秒
2025-07-28 22:16:49,109 - INFO -   - 时长差异: 0.05秒 (1.27%)
2025-07-28 22:16:49,109 - INFO - 
字幕 #25 处理完成，成功生成 2/2 套方案
2025-07-28 22:16:49,109 - INFO - 生成的视频文件:
2025-07-28 22:16:49,109 - INFO -   1. F:/github/aicut_auto/newcut_ai\25_1.mp4
2025-07-28 22:16:49,109 - INFO -   2. F:/github/aicut_auto/newcut_ai\25_2.mp4
2025-07-28 22:16:49,109 - INFO - ========== 字幕 #25 处理结束 ==========

