2025-07-28 22:16:36,631 - INFO - ========== 字幕 #9 处理开始 ==========
2025-07-28 22:16:36,631 - INFO - 字幕内容: 心机女贼心不死，又想用一招栽赃嫁祸，通过心声污蔑一个婢女偷了侯爷给夫人的生辰礼。
2025-07-28 22:16:36,631 - INFO - 字幕序号: [255, 257]
2025-07-28 22:16:36,631 - INFO - 音频文件详情:
2025-07-28 22:16:36,631 - INFO -   - 路径: output\9.wav
2025-07-28 22:16:36,631 - INFO -   - 时长: 6.63秒
2025-07-28 22:16:36,631 - INFO -   - 验证音频时长: 6.63秒
2025-07-28 22:16:36,632 - INFO - 字幕时间戳信息:
2025-07-28 22:16:36,632 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-28 22:16:36,632 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-28 22:16:36,632 - INFO -   - 根据生成的音频时长(6.63秒)已调整字幕时间戳
2025-07-28 22:16:36,632 - INFO - ========== 开始为字幕 #9 生成 6 套场景方案 ==========
2025-07-28 22:16:36,632 - INFO - 开始查找字幕序号 [255, 257] 对应的场景，共有 2710 个场景可选
2025-07-28 22:16:36,632 - INFO - 找到related_overlap场景: scene_id=288, 字幕#255
2025-07-28 22:16:36,632 - INFO - 找到related_overlap场景: scene_id=289, 字幕#257
2025-07-28 22:16:36,633 - INFO - 字幕 #255 找到 1 个overlap场景, 0 个between场景
2025-07-28 22:16:36,633 - INFO - 字幕 #257 找到 1 个overlap场景, 0 个between场景
2025-07-28 22:16:36,633 - INFO - 共收集 2 个未使用的overlap场景和 0 个未使用的between场景
2025-07-28 22:16:36,633 - INFO - 开始生成方案 #1
2025-07-28 22:16:36,633 - INFO - 方案 #1: 为字幕#255选择初始化overlap场景id=288
2025-07-28 22:16:36,633 - INFO - 方案 #1: 为字幕#257选择初始化overlap场景id=289
2025-07-28 22:16:36,633 - INFO - 方案 #1: 初始选择后，当前总时长=8.60秒
2025-07-28 22:16:36,633 - INFO - 方案 #1: 额外between选择后，当前总时长=8.60秒
2025-07-28 22:16:36,633 - INFO - 方案 #1: 场景总时长(8.60秒)大于音频时长(6.63秒)，需要裁剪
2025-07-28 22:16:36,633 - INFO - 调整前总时长: 8.60秒, 目标时长: 6.63秒
2025-07-28 22:16:36,633 - INFO - 需要裁剪 1.96秒
2025-07-28 22:16:36,633 - INFO - 裁剪最长场景ID=289：从6.52秒裁剪至4.55秒
2025-07-28 22:16:36,633 - INFO - 调整后总时长: 6.63秒，与目标时长差异: 0.00秒
2025-07-28 22:16:36,633 - INFO - 方案 #1 调整/填充后最终总时长: 6.63秒
2025-07-28 22:16:36,633 - INFO - 方案 #1 添加到方案列表
2025-07-28 22:16:36,633 - INFO - 开始生成方案 #2
2025-07-28 22:16:36,633 - WARNING - 方案 #2: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:36,633 - INFO - 开始生成方案 #3
2025-07-28 22:16:36,633 - WARNING - 方案 #3: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:36,633 - INFO - 开始生成方案 #4
2025-07-28 22:16:36,633 - WARNING - 方案 #4: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:36,633 - INFO - 开始生成方案 #5
2025-07-28 22:16:36,633 - WARNING - 方案 #5: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:36,633 - INFO - 开始生成方案 #6
2025-07-28 22:16:36,633 - WARNING - 方案 #6: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:36,633 - INFO - ========== 字幕 #9 的 1 套有效场景方案生成完成 ==========
2025-07-28 22:16:36,633 - INFO - 
----- 处理字幕 #9 的方案 #1 -----
2025-07-28 22:16:36,633 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\9_1.mp4
2025-07-28 22:16:36,634 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpkzynr71z
2025-07-28 22:16:36,634 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\288.mp4 (确认存在: True)
2025-07-28 22:16:36,634 - INFO - 添加场景ID=288，时长=2.08秒，累计时长=2.08秒
2025-07-28 22:16:36,634 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\289.mp4 (确认存在: True)
2025-07-28 22:16:36,634 - INFO - 添加场景ID=289，时长=6.52秒，累计时长=8.60秒
2025-07-28 22:16:36,634 - INFO - 准备合并 2 个场景文件，总时长约 8.60秒
2025-07-28 22:16:36,634 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/288.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/289.mp4'

2025-07-28 22:16:36,634 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpkzynr71z\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpkzynr71z\temp_combined.mp4
2025-07-28 22:16:36,796 - INFO - 合并后的视频时长: 8.65秒，目标音频时长: 6.63秒
2025-07-28 22:16:36,796 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpkzynr71z\temp_combined.mp4 -ss 0 -to 6.635 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\9_1.mp4
2025-07-28 22:16:37,155 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 22:16:37,155 - INFO - 目标音频时长: 6.63秒
2025-07-28 22:16:37,155 - INFO - 实际视频时长: 6.66秒
2025-07-28 22:16:37,155 - INFO - 时长差异: 0.03秒 (0.42%)
2025-07-28 22:16:37,155 - INFO - ==========================================
2025-07-28 22:16:37,155 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 22:16:37,155 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\9_1.mp4
2025-07-28 22:16:37,156 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpkzynr71z
2025-07-28 22:16:37,210 - INFO - 方案 #1 处理完成:
2025-07-28 22:16:37,210 - INFO -   - 音频时长: 6.63秒
2025-07-28 22:16:37,210 - INFO -   - 视频时长: 6.66秒
2025-07-28 22:16:37,210 - INFO -   - 时长差异: 0.03秒 (0.42%)
2025-07-28 22:16:37,210 - INFO - 
字幕 #9 处理完成，成功生成 1/1 套方案
2025-07-28 22:16:37,210 - INFO - 生成的视频文件:
2025-07-28 22:16:37,210 - INFO -   1. F:/github/aicut_auto/newcut_ai\9_1.mp4
2025-07-28 22:16:37,210 - INFO - ========== 字幕 #9 处理结束 ==========

