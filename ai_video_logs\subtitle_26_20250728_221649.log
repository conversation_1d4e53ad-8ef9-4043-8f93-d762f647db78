2025-07-28 22:16:49,109 - INFO - ========== 字幕 #26 处理开始 ==========
2025-07-28 22:16:49,109 - INFO - 字幕内容: 可笑，区区邪术，岂能与修真老祖抗衡？老祖早已用平安符偷天换日。
2025-07-28 22:16:49,109 - INFO - 字幕序号: [877, 880]
2025-07-28 22:16:49,110 - INFO - 音频文件详情:
2025-07-28 22:16:49,110 - INFO -   - 路径: output\26.wav
2025-07-28 22:16:49,110 - INFO -   - 时长: 5.55秒
2025-07-28 22:16:49,110 - INFO -   - 验证音频时长: 5.55秒
2025-07-28 22:16:49,110 - INFO - 字幕时间戳信息:
2025-07-28 22:16:49,110 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-28 22:16:49,110 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-28 22:16:49,110 - INFO -   - 根据生成的音频时长(5.55秒)已调整字幕时间戳
2025-07-28 22:16:49,110 - INFO - ========== 开始为字幕 #26 生成 6 套场景方案 ==========
2025-07-28 22:16:49,110 - INFO - 开始查找字幕序号 [877, 880] 对应的场景，共有 2710 个场景可选
2025-07-28 22:16:49,110 - INFO - 找到related_overlap场景: scene_id=907, 字幕#877
2025-07-28 22:16:49,110 - INFO - 找到related_overlap场景: scene_id=909, 字幕#880
2025-07-28 22:16:49,111 - INFO - 找到related_between场景: scene_id=910, 字幕#880
2025-07-28 22:16:49,111 - INFO - 找到related_between场景: scene_id=911, 字幕#880
2025-07-28 22:16:49,111 - INFO - 找到related_between场景: scene_id=912, 字幕#880
2025-07-28 22:16:49,111 - INFO - 找到related_between场景: scene_id=913, 字幕#880
2025-07-28 22:16:49,111 - INFO - 字幕 #877 找到 1 个overlap场景, 0 个between场景
2025-07-28 22:16:49,111 - INFO - 字幕 #880 找到 1 个overlap场景, 4 个between场景
2025-07-28 22:16:49,111 - INFO - 共收集 2 个未使用的overlap场景和 4 个未使用的between场景
2025-07-28 22:16:49,111 - INFO - 开始生成方案 #1
2025-07-28 22:16:49,111 - INFO - 方案 #1: 为字幕#877选择初始化overlap场景id=907
2025-07-28 22:16:49,111 - INFO - 方案 #1: 为字幕#880选择初始化overlap场景id=909
2025-07-28 22:16:49,111 - INFO - 方案 #1: 初始选择后，当前总时长=7.80秒
2025-07-28 22:16:49,111 - INFO - 方案 #1: 额外between选择后，当前总时长=7.80秒
2025-07-28 22:16:49,112 - INFO - 方案 #1: 场景总时长(7.80秒)大于音频时长(5.55秒)，需要裁剪
2025-07-28 22:16:49,112 - INFO - 调整前总时长: 7.80秒, 目标时长: 5.55秒
2025-07-28 22:16:49,112 - INFO - 需要裁剪 2.25秒
2025-07-28 22:16:49,112 - INFO - 裁剪最长场景ID=909：从4.72秒裁剪至2.47秒
2025-07-28 22:16:49,112 - INFO - 调整后总时长: 5.55秒，与目标时长差异: 0.00秒
2025-07-28 22:16:49,112 - INFO - 方案 #1 调整/填充后最终总时长: 5.55秒
2025-07-28 22:16:49,112 - INFO - 方案 #1 添加到方案列表
2025-07-28 22:16:49,112 - INFO - 开始生成方案 #2
2025-07-28 22:16:49,112 - INFO - 方案 #2: 初始选择后，当前总时长=0.00秒
2025-07-28 22:16:49,112 - INFO - 方案 #2: 为字幕#880选择初始化between场景id=913
2025-07-28 22:16:49,112 - INFO - 方案 #2: 额外between选择后，当前总时长=0.68秒
2025-07-28 22:16:49,112 - INFO - 方案 #2: 额外添加between场景id=912, 当前总时长=2.40秒
2025-07-28 22:16:49,112 - INFO - 方案 #2: 额外添加between场景id=911, 当前总时长=5.08秒
2025-07-28 22:16:49,112 - INFO - 方案 #2: 额外添加between场景id=910, 当前总时长=6.64秒
2025-07-28 22:16:49,112 - INFO - 方案 #2: 场景总时长(6.64秒)大于音频时长(5.55秒)，需要裁剪
2025-07-28 22:16:49,112 - INFO - 调整前总时长: 6.64秒, 目标时长: 5.55秒
2025-07-28 22:16:49,112 - INFO - 需要裁剪 1.09秒
2025-07-28 22:16:49,112 - INFO - 裁剪最长场景ID=911：从2.68秒裁剪至1.59秒
2025-07-28 22:16:49,112 - INFO - 调整后总时长: 5.55秒，与目标时长差异: 0.00秒
2025-07-28 22:16:49,112 - INFO - 方案 #2 调整/填充后最终总时长: 5.55秒
2025-07-28 22:16:49,112 - INFO - 方案 #2 添加到方案列表
2025-07-28 22:16:49,112 - INFO - 开始生成方案 #3
2025-07-28 22:16:49,112 - WARNING - 方案 #3: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:49,112 - INFO - 开始生成方案 #4
2025-07-28 22:16:49,112 - WARNING - 方案 #4: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:49,112 - INFO - 开始生成方案 #5
2025-07-28 22:16:49,112 - WARNING - 方案 #5: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:49,112 - INFO - 开始生成方案 #6
2025-07-28 22:16:49,112 - WARNING - 方案 #6: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:49,112 - INFO - ========== 字幕 #26 的 2 套有效场景方案生成完成 ==========
2025-07-28 22:16:49,112 - INFO - 
----- 处理字幕 #26 的方案 #1 -----
2025-07-28 22:16:49,112 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\26_1.mp4
2025-07-28 22:16:49,112 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpduao0325
2025-07-28 22:16:49,113 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\907.mp4 (确认存在: True)
2025-07-28 22:16:49,113 - INFO - 添加场景ID=907，时长=3.08秒，累计时长=3.08秒
2025-07-28 22:16:49,113 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\909.mp4 (确认存在: True)
2025-07-28 22:16:49,113 - INFO - 添加场景ID=909，时长=4.72秒，累计时长=7.80秒
2025-07-28 22:16:49,113 - INFO - 准备合并 2 个场景文件，总时长约 7.80秒
2025-07-28 22:16:49,113 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/907.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/909.mp4'

2025-07-28 22:16:49,113 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpduao0325\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpduao0325\temp_combined.mp4
2025-07-28 22:16:49,257 - INFO - 合并后的视频时长: 7.85秒，目标音频时长: 5.55秒
2025-07-28 22:16:49,257 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpduao0325\temp_combined.mp4 -ss 0 -to 5.546 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\26_1.mp4
2025-07-28 22:16:49,594 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 22:16:49,594 - INFO - 目标音频时长: 5.55秒
2025-07-28 22:16:49,594 - INFO - 实际视频时长: 5.58秒
2025-07-28 22:16:49,594 - INFO - 时长差异: 0.04秒 (0.67%)
2025-07-28 22:16:49,594 - INFO - ==========================================
2025-07-28 22:16:49,594 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 22:16:49,594 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\26_1.mp4
2025-07-28 22:16:49,596 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpduao0325
2025-07-28 22:16:49,642 - INFO - 方案 #1 处理完成:
2025-07-28 22:16:49,642 - INFO -   - 音频时长: 5.55秒
2025-07-28 22:16:49,642 - INFO -   - 视频时长: 5.58秒
2025-07-28 22:16:49,642 - INFO -   - 时长差异: 0.04秒 (0.67%)
2025-07-28 22:16:49,642 - INFO - 
----- 处理字幕 #26 的方案 #2 -----
2025-07-28 22:16:49,642 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\26_2.mp4
2025-07-28 22:16:49,642 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmprw_5i1o1
2025-07-28 22:16:49,643 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\913.mp4 (确认存在: True)
2025-07-28 22:16:49,643 - INFO - 添加场景ID=913，时长=0.68秒，累计时长=0.68秒
2025-07-28 22:16:49,643 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\912.mp4 (确认存在: True)
2025-07-28 22:16:49,643 - INFO - 添加场景ID=912，时长=1.72秒，累计时长=2.40秒
2025-07-28 22:16:49,643 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\911.mp4 (确认存在: True)
2025-07-28 22:16:49,643 - INFO - 添加场景ID=911，时长=2.68秒，累计时长=5.08秒
2025-07-28 22:16:49,643 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\910.mp4 (确认存在: True)
2025-07-28 22:16:49,643 - INFO - 添加场景ID=910，时长=1.56秒，累计时长=6.64秒
2025-07-28 22:16:49,643 - INFO - 准备合并 4 个场景文件，总时长约 6.64秒
2025-07-28 22:16:49,643 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/913.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/912.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/911.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/910.mp4'

2025-07-28 22:16:49,644 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmprw_5i1o1\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmprw_5i1o1\temp_combined.mp4
2025-07-28 22:16:49,809 - INFO - 合并后的视频时长: 6.73秒，目标音频时长: 5.55秒
2025-07-28 22:16:49,809 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmprw_5i1o1\temp_combined.mp4 -ss 0 -to 5.546 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\26_2.mp4
2025-07-28 22:16:50,136 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 22:16:50,136 - INFO - 目标音频时长: 5.55秒
2025-07-28 22:16:50,136 - INFO - 实际视频时长: 5.58秒
2025-07-28 22:16:50,136 - INFO - 时长差异: 0.04秒 (0.67%)
2025-07-28 22:16:50,136 - INFO - ==========================================
2025-07-28 22:16:50,136 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 22:16:50,136 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\26_2.mp4
2025-07-28 22:16:50,137 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmprw_5i1o1
2025-07-28 22:16:50,181 - INFO - 方案 #2 处理完成:
2025-07-28 22:16:50,181 - INFO -   - 音频时长: 5.55秒
2025-07-28 22:16:50,181 - INFO -   - 视频时长: 5.58秒
2025-07-28 22:16:50,181 - INFO -   - 时长差异: 0.04秒 (0.67%)
2025-07-28 22:16:50,181 - INFO - 
字幕 #26 处理完成，成功生成 2/2 套方案
2025-07-28 22:16:50,181 - INFO - 生成的视频文件:
2025-07-28 22:16:50,181 - INFO -   1. F:/github/aicut_auto/newcut_ai\26_1.mp4
2025-07-28 22:16:50,181 - INFO -   2. F:/github/aicut_auto/newcut_ai\26_2.mp4
2025-07-28 22:16:50,181 - INFO - ========== 字幕 #26 处理结束 ==========

