2025-07-28 22:16:36,098 - INFO - ========== 字幕 #8 处理开始 ==========
2025-07-28 22:16:36,098 - INFO - 字幕内容: 眼看第一计不成，假千金的脸色瞬间变得难看，侯爷甚至怀疑她是不是中邪了。
2025-07-28 22:16:36,098 - INFO - 字幕序号: [143]
2025-07-28 22:16:36,098 - INFO - 音频文件详情:
2025-07-28 22:16:36,099 - INFO -   - 路径: output\8.wav
2025-07-28 22:16:36,099 - INFO -   - 时长: 4.92秒
2025-07-28 22:16:36,099 - INFO -   - 验证音频时长: 4.92秒
2025-07-28 22:16:36,099 - INFO - 字幕时间戳信息:
2025-07-28 22:16:36,109 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-28 22:16:36,109 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-28 22:16:36,109 - INFO -   - 根据生成的音频时长(4.92秒)已调整字幕时间戳
2025-07-28 22:16:36,109 - INFO - ========== 开始为字幕 #8 生成 6 套场景方案 ==========
2025-07-28 22:16:36,109 - INFO - 开始查找字幕序号 [143] 对应的场景，共有 2710 个场景可选
2025-07-28 22:16:36,109 - INFO - 找到related_overlap场景: scene_id=149, 字幕#143
2025-07-28 22:16:36,109 - INFO - 找到related_overlap场景: scene_id=150, 字幕#143
2025-07-28 22:16:36,110 - INFO - 字幕 #143 找到 2 个overlap场景, 0 个between场景
2025-07-28 22:16:36,110 - INFO - 共收集 2 个未使用的overlap场景和 0 个未使用的between场景
2025-07-28 22:16:36,110 - INFO - 开始生成方案 #1
2025-07-28 22:16:36,110 - INFO - 方案 #1: 为字幕#143选择初始化overlap场景id=150
2025-07-28 22:16:36,110 - INFO - 方案 #1: 初始选择后，当前总时长=1.24秒
2025-07-28 22:16:36,110 - INFO - 方案 #1: 额外添加overlap场景id=149, 当前总时长=3.80秒
2025-07-28 22:16:36,110 - INFO - 方案 #1: 额外between选择后，当前总时长=3.80秒
2025-07-28 22:16:36,110 - INFO - 方案 #1: 场景总时长(3.80秒)小于音频时长(4.92秒)，需要延伸填充
2025-07-28 22:16:36,110 - INFO - 方案 #1: 最后一个场景ID: 149
2025-07-28 22:16:36,110 - INFO - 方案 #1: 找到最后一个场景在原始列表中的索引: 148
2025-07-28 22:16:36,110 - INFO - 方案 #1: 需要填充时长: 1.12秒
2025-07-28 22:16:36,110 - INFO - 方案 #1: 跳过已使用的场景: scene_id=150
2025-07-28 22:16:36,110 - INFO - 方案 #1: 追加场景 scene_id=151 (裁剪至 1.12秒)
2025-07-28 22:16:36,110 - INFO - 方案 #1: 成功填充至目标时长
2025-07-28 22:16:36,110 - INFO - 方案 #1 调整/填充后最终总时长: 4.92秒
2025-07-28 22:16:36,110 - INFO - 方案 #1 添加到方案列表
2025-07-28 22:16:36,110 - INFO - 开始生成方案 #2
2025-07-28 22:16:36,110 - WARNING - 方案 #2: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:36,110 - INFO - 开始生成方案 #3
2025-07-28 22:16:36,110 - WARNING - 方案 #3: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:36,110 - INFO - 开始生成方案 #4
2025-07-28 22:16:36,110 - WARNING - 方案 #4: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:36,110 - INFO - 开始生成方案 #5
2025-07-28 22:16:36,110 - WARNING - 方案 #5: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:36,110 - INFO - 开始生成方案 #6
2025-07-28 22:16:36,110 - WARNING - 方案 #6: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:36,110 - INFO - ========== 字幕 #8 的 1 套有效场景方案生成完成 ==========
2025-07-28 22:16:36,111 - INFO - 
----- 处理字幕 #8 的方案 #1 -----
2025-07-28 22:16:36,111 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\8_1.mp4
2025-07-28 22:16:36,111 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpu9z9vsxi
2025-07-28 22:16:36,111 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\150.mp4 (确认存在: True)
2025-07-28 22:16:36,111 - INFO - 添加场景ID=150，时长=1.24秒，累计时长=1.24秒
2025-07-28 22:16:36,111 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\149.mp4 (确认存在: True)
2025-07-28 22:16:36,111 - INFO - 添加场景ID=149，时长=2.56秒，累计时长=3.80秒
2025-07-28 22:16:36,111 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\151.mp4 (确认存在: True)
2025-07-28 22:16:36,111 - INFO - 添加场景ID=151，时长=1.44秒，累计时长=5.24秒
2025-07-28 22:16:36,111 - INFO - 准备合并 3 个场景文件，总时长约 5.24秒
2025-07-28 22:16:36,111 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/150.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/149.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/151.mp4'

2025-07-28 22:16:36,113 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpu9z9vsxi\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpu9z9vsxi\temp_combined.mp4
2025-07-28 22:16:36,271 - INFO - 合并后的视频时长: 5.31秒，目标音频时长: 4.92秒
2025-07-28 22:16:36,271 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpu9z9vsxi\temp_combined.mp4 -ss 0 -to 4.917 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\8_1.mp4
2025-07-28 22:16:36,583 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 22:16:36,583 - INFO - 目标音频时长: 4.92秒
2025-07-28 22:16:36,583 - INFO - 实际视频时长: 4.94秒
2025-07-28 22:16:36,583 - INFO - 时长差异: 0.03秒 (0.53%)
2025-07-28 22:16:36,583 - INFO - ==========================================
2025-07-28 22:16:36,583 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 22:16:36,583 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\8_1.mp4
2025-07-28 22:16:36,584 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpu9z9vsxi
2025-07-28 22:16:36,630 - INFO - 方案 #1 处理完成:
2025-07-28 22:16:36,630 - INFO -   - 音频时长: 4.92秒
2025-07-28 22:16:36,630 - INFO -   - 视频时长: 4.94秒
2025-07-28 22:16:36,630 - INFO -   - 时长差异: 0.03秒 (0.53%)
2025-07-28 22:16:36,630 - INFO - 
字幕 #8 处理完成，成功生成 1/1 套方案
2025-07-28 22:16:36,630 - INFO - 生成的视频文件:
2025-07-28 22:16:36,630 - INFO -   1. F:/github/aicut_auto/newcut_ai\8_1.mp4
2025-07-28 22:16:36,630 - INFO - ========== 字幕 #8 处理结束 ==========

