2025-07-28 22:16:43,020 - INFO - ========== 字幕 #18 处理开始 ==========
2025-07-28 22:16:43,021 - INFO - 字幕内容: 老祖将计就计，直接将她这些恶毒的真实心声公之于众，让所有人都听得一清二楚！
2025-07-28 22:16:43,021 - INFO - 字幕序号: [502, 503]
2025-07-28 22:16:43,021 - INFO - 音频文件详情:
2025-07-28 22:16:43,021 - INFO -   - 路径: output\18.wav
2025-07-28 22:16:43,021 - INFO -   - 时长: 5.94秒
2025-07-28 22:16:43,021 - INFO -   - 验证音频时长: 5.94秒
2025-07-28 22:16:43,021 - INFO - 字幕时间戳信息:
2025-07-28 22:16:43,021 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-28 22:16:43,021 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-28 22:16:43,021 - INFO -   - 根据生成的音频时长(5.94秒)已调整字幕时间戳
2025-07-28 22:16:43,021 - INFO - ========== 开始为字幕 #18 生成 6 套场景方案 ==========
2025-07-28 22:16:43,021 - INFO - 开始查找字幕序号 [502, 503] 对应的场景，共有 2710 个场景可选
2025-07-28 22:16:43,021 - INFO - 找到related_overlap场景: scene_id=522, 字幕#502
2025-07-28 22:16:43,021 - INFO - 找到related_overlap场景: scene_id=523, 字幕#502
2025-07-28 22:16:43,022 - INFO - 找到related_overlap场景: scene_id=525, 字幕#503
2025-07-28 22:16:43,022 - INFO - 找到related_overlap场景: scene_id=526, 字幕#503
2025-07-28 22:16:43,022 - INFO - 找到related_between场景: scene_id=524, 字幕#502
2025-07-28 22:16:43,023 - INFO - 字幕 #502 找到 2 个overlap场景, 1 个between场景
2025-07-28 22:16:43,023 - INFO - 字幕 #503 找到 2 个overlap场景, 0 个between场景
2025-07-28 22:16:43,023 - INFO - 共收集 4 个未使用的overlap场景和 1 个未使用的between场景
2025-07-28 22:16:43,023 - INFO - 开始生成方案 #1
2025-07-28 22:16:43,023 - INFO - 方案 #1: 为字幕#502选择初始化overlap场景id=523
2025-07-28 22:16:43,023 - INFO - 方案 #1: 为字幕#503选择初始化overlap场景id=525
2025-07-28 22:16:43,023 - INFO - 方案 #1: 初始选择后，当前总时长=3.00秒
2025-07-28 22:16:43,023 - INFO - 方案 #1: 额外添加overlap场景id=526, 当前总时长=6.36秒
2025-07-28 22:16:43,023 - INFO - 方案 #1: 额外between选择后，当前总时长=6.36秒
2025-07-28 22:16:43,023 - INFO - 方案 #1: 场景总时长(6.36秒)大于音频时长(5.94秒)，需要裁剪
2025-07-28 22:16:43,023 - INFO - 调整前总时长: 6.36秒, 目标时长: 5.94秒
2025-07-28 22:16:43,023 - INFO - 需要裁剪 0.42秒
2025-07-28 22:16:43,023 - INFO - 裁剪最长场景ID=526：从3.36秒裁剪至2.94秒
2025-07-28 22:16:43,023 - INFO - 调整后总时长: 5.94秒，与目标时长差异: 0.00秒
2025-07-28 22:16:43,023 - INFO - 方案 #1 调整/填充后最终总时长: 5.94秒
2025-07-28 22:16:43,023 - INFO - 方案 #1 添加到方案列表
2025-07-28 22:16:43,023 - INFO - 开始生成方案 #2
2025-07-28 22:16:43,023 - INFO - 方案 #2: 为字幕#502选择初始化overlap场景id=522
2025-07-28 22:16:43,023 - INFO - 方案 #2: 初始选择后，当前总时长=1.44秒
2025-07-28 22:16:43,023 - INFO - 方案 #2: 额外between选择后，当前总时长=1.44秒
2025-07-28 22:16:43,023 - INFO - 方案 #2: 额外添加between场景id=524, 当前总时长=5.04秒
2025-07-28 22:16:43,023 - INFO - 方案 #2: 场景总时长(5.04秒)小于音频时长(5.94秒)，需要延伸填充
2025-07-28 22:16:43,023 - INFO - 方案 #2: 最后一个场景ID: 524
2025-07-28 22:16:43,023 - INFO - 方案 #2: 找到最后一个场景在原始列表中的索引: 523
2025-07-28 22:16:43,023 - INFO - 方案 #2: 需要填充时长: 0.90秒
2025-07-28 22:16:43,023 - INFO - 方案 #2: 跳过已使用的场景: scene_id=525
2025-07-28 22:16:43,023 - INFO - 方案 #2: 跳过已使用的场景: scene_id=526
2025-07-28 22:16:43,023 - INFO - 方案 #2: 追加场景 scene_id=527 (裁剪至 0.90秒)
2025-07-28 22:16:43,023 - INFO - 方案 #2: 成功填充至目标时长
2025-07-28 22:16:43,023 - INFO - 方案 #2 调整/填充后最终总时长: 5.94秒
2025-07-28 22:16:43,023 - INFO - 方案 #2 添加到方案列表
2025-07-28 22:16:43,023 - INFO - 开始生成方案 #3
2025-07-28 22:16:43,023 - WARNING - 方案 #3: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:43,023 - INFO - 开始生成方案 #4
2025-07-28 22:16:43,023 - WARNING - 方案 #4: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:43,023 - INFO - 开始生成方案 #5
2025-07-28 22:16:43,023 - WARNING - 方案 #5: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:43,023 - INFO - 开始生成方案 #6
2025-07-28 22:16:43,023 - WARNING - 方案 #6: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:43,023 - INFO - ========== 字幕 #18 的 2 套有效场景方案生成完成 ==========
2025-07-28 22:16:43,023 - INFO - 
----- 处理字幕 #18 的方案 #1 -----
2025-07-28 22:16:43,023 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\18_1.mp4
2025-07-28 22:16:43,024 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp23gwcmoi
2025-07-28 22:16:43,024 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\523.mp4 (确认存在: True)
2025-07-28 22:16:43,024 - INFO - 添加场景ID=523，时长=1.16秒，累计时长=1.16秒
2025-07-28 22:16:43,024 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\525.mp4 (确认存在: True)
2025-07-28 22:16:43,024 - INFO - 添加场景ID=525，时长=1.84秒，累计时长=3.00秒
2025-07-28 22:16:43,024 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\526.mp4 (确认存在: True)
2025-07-28 22:16:43,024 - INFO - 添加场景ID=526，时长=3.36秒，累计时长=6.36秒
2025-07-28 22:16:43,024 - INFO - 准备合并 3 个场景文件，总时长约 6.36秒
2025-07-28 22:16:43,024 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/523.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/525.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/526.mp4'

2025-07-28 22:16:43,024 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp23gwcmoi\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp23gwcmoi\temp_combined.mp4
2025-07-28 22:16:43,240 - INFO - 合并后的视频时长: 6.43秒，目标音频时长: 5.94秒
2025-07-28 22:16:43,240 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp23gwcmoi\temp_combined.mp4 -ss 0 -to 5.943 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\18_1.mp4
2025-07-28 22:16:43,632 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 22:16:43,632 - INFO - 目标音频时长: 5.94秒
2025-07-28 22:16:43,632 - INFO - 实际视频时长: 5.98秒
2025-07-28 22:16:43,632 - INFO - 时长差异: 0.04秒 (0.67%)
2025-07-28 22:16:43,632 - INFO - ==========================================
2025-07-28 22:16:43,632 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 22:16:43,632 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\18_1.mp4
2025-07-28 22:16:43,633 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp23gwcmoi
2025-07-28 22:16:43,691 - INFO - 方案 #1 处理完成:
2025-07-28 22:16:43,691 - INFO -   - 音频时长: 5.94秒
2025-07-28 22:16:43,691 - INFO -   - 视频时长: 5.98秒
2025-07-28 22:16:43,691 - INFO -   - 时长差异: 0.04秒 (0.67%)
2025-07-28 22:16:43,691 - INFO - 
----- 处理字幕 #18 的方案 #2 -----
2025-07-28 22:16:43,691 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\18_2.mp4
2025-07-28 22:16:43,691 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp70yfmlun
2025-07-28 22:16:43,692 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\522.mp4 (确认存在: True)
2025-07-28 22:16:43,692 - INFO - 添加场景ID=522，时长=1.44秒，累计时长=1.44秒
2025-07-28 22:16:43,692 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\524.mp4 (确认存在: True)
2025-07-28 22:16:43,692 - INFO - 添加场景ID=524，时长=3.60秒，累计时长=5.04秒
2025-07-28 22:16:43,692 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\527.mp4 (确认存在: True)
2025-07-28 22:16:43,692 - INFO - 添加场景ID=527，时长=2.68秒，累计时长=7.72秒
2025-07-28 22:16:43,692 - INFO - 准备合并 3 个场景文件，总时长约 7.72秒
2025-07-28 22:16:43,692 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/522.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/524.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/527.mp4'

2025-07-28 22:16:43,692 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp70yfmlun\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp70yfmlun\temp_combined.mp4
2025-07-28 22:16:43,844 - INFO - 合并后的视频时长: 7.79秒，目标音频时长: 5.94秒
2025-07-28 22:16:43,844 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp70yfmlun\temp_combined.mp4 -ss 0 -to 5.943 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\18_2.mp4
2025-07-28 22:16:44,174 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 22:16:44,174 - INFO - 目标音频时长: 5.94秒
2025-07-28 22:16:44,174 - INFO - 实际视频时长: 5.98秒
2025-07-28 22:16:44,174 - INFO - 时长差异: 0.04秒 (0.67%)
2025-07-28 22:16:44,174 - INFO - ==========================================
2025-07-28 22:16:44,174 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 22:16:44,174 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\18_2.mp4
2025-07-28 22:16:44,174 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp70yfmlun
2025-07-28 22:16:44,218 - INFO - 方案 #2 处理完成:
2025-07-28 22:16:44,218 - INFO -   - 音频时长: 5.94秒
2025-07-28 22:16:44,218 - INFO -   - 视频时长: 5.98秒
2025-07-28 22:16:44,218 - INFO -   - 时长差异: 0.04秒 (0.67%)
2025-07-28 22:16:44,218 - INFO - 
字幕 #18 处理完成，成功生成 2/2 套方案
2025-07-28 22:16:44,218 - INFO - 生成的视频文件:
2025-07-28 22:16:44,218 - INFO -   1. F:/github/aicut_auto/newcut_ai\18_1.mp4
2025-07-28 22:16:44,218 - INFO -   2. F:/github/aicut_auto/newcut_ai\18_2.mp4
2025-07-28 22:16:44,218 - INFO - ========== 字幕 #18 处理结束 ==========

