2025-07-28 22:16:34,458 - INFO - ========== 字幕 #5 处理开始 ==========
2025-07-28 22:16:34,459 - INFO - 字幕内容: 她当着全家人的面，用楚楚可怜的语气，通过心声哭诉姐姐在糕点里下毒。
2025-07-28 22:16:34,459 - INFO - 字幕序号: [126]
2025-07-28 22:16:34,459 - INFO - 音频文件详情:
2025-07-28 22:16:34,459 - INFO -   - 路径: output\5.wav
2025-07-28 22:16:34,459 - INFO -   - 时长: 4.81秒
2025-07-28 22:16:34,459 - INFO -   - 验证音频时长: 4.81秒
2025-07-28 22:16:34,459 - INFO - 字幕时间戳信息:
2025-07-28 22:16:34,468 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-28 22:16:34,468 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-28 22:16:34,469 - INFO -   - 根据生成的音频时长(4.81秒)已调整字幕时间戳
2025-07-28 22:16:34,469 - INFO - ========== 开始为字幕 #5 生成 6 套场景方案 ==========
2025-07-28 22:16:34,469 - INFO - 开始查找字幕序号 [126] 对应的场景，共有 2710 个场景可选
2025-07-28 22:16:34,469 - INFO - 找到related_overlap场景: scene_id=134, 字幕#126
2025-07-28 22:16:34,470 - INFO - 字幕 #126 找到 1 个overlap场景, 0 个between场景
2025-07-28 22:16:34,470 - INFO - 共收集 1 个未使用的overlap场景和 0 个未使用的between场景
2025-07-28 22:16:34,470 - INFO - 开始生成方案 #1
2025-07-28 22:16:34,470 - INFO - 方案 #1: 为字幕#126选择初始化overlap场景id=134
2025-07-28 22:16:34,470 - INFO - 方案 #1: 初始选择后，当前总时长=7.76秒
2025-07-28 22:16:34,470 - INFO - 方案 #1: 额外between选择后，当前总时长=7.76秒
2025-07-28 22:16:34,470 - INFO - 方案 #1: 场景总时长(7.76秒)大于音频时长(4.81秒)，需要裁剪
2025-07-28 22:16:34,470 - INFO - 调整前总时长: 7.76秒, 目标时长: 4.81秒
2025-07-28 22:16:34,470 - INFO - 需要裁剪 2.95秒
2025-07-28 22:16:34,470 - INFO - 裁剪最长场景ID=134：从7.76秒裁剪至4.81秒
2025-07-28 22:16:34,470 - INFO - 调整后总时长: 4.81秒，与目标时长差异: 0.00秒
2025-07-28 22:16:34,470 - INFO - 方案 #1 调整/填充后最终总时长: 4.81秒
2025-07-28 22:16:34,470 - INFO - 方案 #1 添加到方案列表
2025-07-28 22:16:34,470 - INFO - 开始生成方案 #2
2025-07-28 22:16:34,470 - WARNING - 方案 #2: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:34,470 - INFO - 开始生成方案 #3
2025-07-28 22:16:34,470 - WARNING - 方案 #3: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:34,470 - INFO - 开始生成方案 #4
2025-07-28 22:16:34,470 - WARNING - 方案 #4: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:34,470 - INFO - 开始生成方案 #5
2025-07-28 22:16:34,470 - WARNING - 方案 #5: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:34,470 - INFO - 开始生成方案 #6
2025-07-28 22:16:34,470 - WARNING - 方案 #6: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:34,470 - INFO - ========== 字幕 #5 的 1 套有效场景方案生成完成 ==========
2025-07-28 22:16:34,470 - INFO - 
----- 处理字幕 #5 的方案 #1 -----
2025-07-28 22:16:34,470 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\5_1.mp4
2025-07-28 22:16:34,471 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpma2r90_s
2025-07-28 22:16:34,471 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\134.mp4 (确认存在: True)
2025-07-28 22:16:34,471 - INFO - 添加场景ID=134，时长=7.76秒，累计时长=7.76秒
2025-07-28 22:16:34,471 - INFO - 场景总时长(7.76秒)已达到音频时长(4.81秒)的1.5倍，停止添加场景
2025-07-28 22:16:34,471 - INFO - 准备合并 1 个场景文件，总时长约 7.76秒
2025-07-28 22:16:34,471 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/134.mp4'

2025-07-28 22:16:34,471 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpma2r90_s\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpma2r90_s\temp_combined.mp4
2025-07-28 22:16:34,584 - INFO - 合并后的视频时长: 7.78秒，目标音频时长: 4.81秒
2025-07-28 22:16:34,584 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpma2r90_s\temp_combined.mp4 -ss 0 -to 4.807 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\5_1.mp4
2025-07-28 22:16:34,911 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 22:16:34,911 - INFO - 目标音频时长: 4.81秒
2025-07-28 22:16:34,911 - INFO - 实际视频时长: 4.86秒
2025-07-28 22:16:34,911 - INFO - 时长差异: 0.06秒 (1.16%)
2025-07-28 22:16:34,911 - INFO - ==========================================
2025-07-28 22:16:34,911 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 22:16:34,911 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\5_1.mp4
2025-07-28 22:16:34,913 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpma2r90_s
2025-07-28 22:16:34,961 - INFO - 方案 #1 处理完成:
2025-07-28 22:16:34,961 - INFO -   - 音频时长: 4.81秒
2025-07-28 22:16:34,961 - INFO -   - 视频时长: 4.86秒
2025-07-28 22:16:34,961 - INFO -   - 时长差异: 0.06秒 (1.16%)
2025-07-28 22:16:34,961 - INFO - 
字幕 #5 处理完成，成功生成 1/1 套方案
2025-07-28 22:16:34,961 - INFO - 生成的视频文件:
2025-07-28 22:16:34,961 - INFO -   1. F:/github/aicut_auto/newcut_ai\5_1.mp4
2025-07-28 22:16:34,961 - INFO - ========== 字幕 #5 处理结束 ==========

