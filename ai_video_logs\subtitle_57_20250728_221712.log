2025-07-28 22:17:12,645 - INFO - ========== 字幕 #57 处理开始 ==========
2025-07-28 22:17:12,645 - INFO - 字幕内容: 皇帝龙颜大悦，当场拍板，一道圣旨便将这个恶毒女配的最终归宿，彻底钉死。
2025-07-28 22:17:12,645 - INFO - 字幕序号: [2675, 2677]
2025-07-28 22:17:12,645 - INFO - 音频文件详情:
2025-07-28 22:17:12,645 - INFO -   - 路径: output\57.wav
2025-07-28 22:17:12,646 - INFO -   - 时长: 5.69秒
2025-07-28 22:17:12,646 - INFO -   - 验证音频时长: 5.69秒
2025-07-28 22:17:12,646 - INFO - 字幕时间戳信息:
2025-07-28 22:17:12,656 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-28 22:17:12,656 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-28 22:17:12,656 - INFO -   - 根据生成的音频时长(5.69秒)已调整字幕时间戳
2025-07-28 22:17:12,656 - INFO - ========== 开始为字幕 #57 生成 6 套场景方案 ==========
2025-07-28 22:17:12,656 - INFO - 开始查找字幕序号 [2675, 2677] 对应的场景，共有 2710 个场景可选
2025-07-28 22:17:12,657 - INFO - 找到related_overlap场景: scene_id=2548, 字幕#2675
2025-07-28 22:17:12,657 - INFO - 找到related_overlap场景: scene_id=2549, 字幕#2675
2025-07-28 22:17:12,657 - INFO - 找到related_overlap场景: scene_id=2550, 字幕#2677
2025-07-28 22:17:12,657 - INFO - 字幕 #2675 找到 2 个overlap场景, 0 个between场景
2025-07-28 22:17:12,657 - INFO - 字幕 #2677 找到 1 个overlap场景, 0 个between场景
2025-07-28 22:17:12,657 - INFO - 共收集 3 个未使用的overlap场景和 0 个未使用的between场景
2025-07-28 22:17:12,657 - INFO - 开始生成方案 #1
2025-07-28 22:17:12,657 - INFO - 方案 #1: 为字幕#2675选择初始化overlap场景id=2549
2025-07-28 22:17:12,657 - INFO - 方案 #1: 为字幕#2677选择初始化overlap场景id=2550
2025-07-28 22:17:12,657 - INFO - 方案 #1: 初始选择后，当前总时长=6.28秒
2025-07-28 22:17:12,657 - INFO - 方案 #1: 额外between选择后，当前总时长=6.28秒
2025-07-28 22:17:12,657 - INFO - 方案 #1: 场景总时长(6.28秒)大于音频时长(5.69秒)，需要裁剪
2025-07-28 22:17:12,657 - INFO - 调整前总时长: 6.28秒, 目标时长: 5.69秒
2025-07-28 22:17:12,657 - INFO - 需要裁剪 0.59秒
2025-07-28 22:17:12,657 - INFO - 裁剪最长场景ID=2549：从4.08秒裁剪至3.49秒
2025-07-28 22:17:12,657 - INFO - 调整后总时长: 5.69秒，与目标时长差异: 0.00秒
2025-07-28 22:17:12,657 - INFO - 方案 #1 调整/填充后最终总时长: 5.69秒
2025-07-28 22:17:12,657 - INFO - 方案 #1 添加到方案列表
2025-07-28 22:17:12,657 - INFO - 开始生成方案 #2
2025-07-28 22:17:12,657 - INFO - 方案 #2: 为字幕#2675选择初始化overlap场景id=2548
2025-07-28 22:17:12,657 - INFO - 方案 #2: 初始选择后，当前总时长=2.40秒
2025-07-28 22:17:12,657 - INFO - 方案 #2: 额外between选择后，当前总时长=2.40秒
2025-07-28 22:17:12,657 - INFO - 方案 #2: 场景总时长(2.40秒)小于音频时长(5.69秒)，需要延伸填充
2025-07-28 22:17:12,657 - INFO - 方案 #2: 最后一个场景ID: 2548
2025-07-28 22:17:12,658 - INFO - 方案 #2: 找到最后一个场景在原始列表中的索引: 2547
2025-07-28 22:17:12,658 - INFO - 方案 #2: 需要填充时长: 3.29秒
2025-07-28 22:17:12,658 - INFO - 方案 #2: 跳过已使用的场景: scene_id=2549
2025-07-28 22:17:12,658 - INFO - 方案 #2: 跳过已使用的场景: scene_id=2550
2025-07-28 22:17:12,658 - INFO - 方案 #2: 追加场景 scene_id=2551 (完整时长 2.04秒)
2025-07-28 22:17:12,658 - INFO - 方案 #2: 追加场景 scene_id=2552 (裁剪至 1.25秒)
2025-07-28 22:17:12,658 - INFO - 方案 #2: 成功填充至目标时长
2025-07-28 22:17:12,658 - INFO - 方案 #2 调整/填充后最终总时长: 5.69秒
2025-07-28 22:17:12,658 - INFO - 方案 #2 添加到方案列表
2025-07-28 22:17:12,658 - INFO - 开始生成方案 #3
2025-07-28 22:17:12,658 - WARNING - 方案 #3: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:17:12,658 - INFO - 开始生成方案 #4
2025-07-28 22:17:12,658 - WARNING - 方案 #4: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:17:12,658 - INFO - 开始生成方案 #5
2025-07-28 22:17:12,658 - WARNING - 方案 #5: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:17:12,658 - INFO - 开始生成方案 #6
2025-07-28 22:17:12,658 - WARNING - 方案 #6: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:17:12,658 - INFO - ========== 字幕 #57 的 2 套有效场景方案生成完成 ==========
2025-07-28 22:17:12,658 - INFO - 
----- 处理字幕 #57 的方案 #1 -----
2025-07-28 22:17:12,658 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\57_1.mp4
2025-07-28 22:17:12,658 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpzd9kd2ub
2025-07-28 22:17:12,659 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2549.mp4 (确认存在: True)
2025-07-28 22:17:12,659 - INFO - 添加场景ID=2549，时长=4.08秒，累计时长=4.08秒
2025-07-28 22:17:12,659 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2550.mp4 (确认存在: True)
2025-07-28 22:17:12,659 - INFO - 添加场景ID=2550，时长=2.20秒，累计时长=6.28秒
2025-07-28 22:17:12,659 - INFO - 准备合并 2 个场景文件，总时长约 6.28秒
2025-07-28 22:17:12,659 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2549.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2550.mp4'

2025-07-28 22:17:12,659 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpzd9kd2ub\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpzd9kd2ub\temp_combined.mp4
2025-07-28 22:17:12,798 - INFO - 合并后的视频时长: 6.33秒，目标音频时长: 5.69秒
2025-07-28 22:17:12,798 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpzd9kd2ub\temp_combined.mp4 -ss 0 -to 5.692 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\57_1.mp4
2025-07-28 22:17:13,146 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 22:17:13,146 - INFO - 目标音频时长: 5.69秒
2025-07-28 22:17:13,146 - INFO - 实际视频时长: 5.74秒
2025-07-28 22:17:13,146 - INFO - 时长差异: 0.05秒 (0.90%)
2025-07-28 22:17:13,146 - INFO - ==========================================
2025-07-28 22:17:13,146 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 22:17:13,146 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\57_1.mp4
2025-07-28 22:17:13,147 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpzd9kd2ub
2025-07-28 22:17:13,191 - INFO - 方案 #1 处理完成:
2025-07-28 22:17:13,191 - INFO -   - 音频时长: 5.69秒
2025-07-28 22:17:13,191 - INFO -   - 视频时长: 5.74秒
2025-07-28 22:17:13,191 - INFO -   - 时长差异: 0.05秒 (0.90%)
2025-07-28 22:17:13,191 - INFO - 
----- 处理字幕 #57 的方案 #2 -----
2025-07-28 22:17:13,192 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\57_2.mp4
2025-07-28 22:17:13,192 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmplam1y9jb
2025-07-28 22:17:13,192 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2548.mp4 (确认存在: True)
2025-07-28 22:17:13,192 - INFO - 添加场景ID=2548，时长=2.40秒，累计时长=2.40秒
2025-07-28 22:17:13,192 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2551.mp4 (确认存在: True)
2025-07-28 22:17:13,192 - INFO - 添加场景ID=2551，时长=2.04秒，累计时长=4.44秒
2025-07-28 22:17:13,192 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2552.mp4 (确认存在: True)
2025-07-28 22:17:13,192 - INFO - 添加场景ID=2552，时长=3.52秒，累计时长=7.96秒
2025-07-28 22:17:13,193 - INFO - 准备合并 3 个场景文件，总时长约 7.96秒
2025-07-28 22:17:13,193 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2548.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2551.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2552.mp4'

2025-07-28 22:17:13,193 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmplam1y9jb\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmplam1y9jb\temp_combined.mp4
2025-07-28 22:17:13,338 - INFO - 合并后的视频时长: 8.03秒，目标音频时长: 5.69秒
2025-07-28 22:17:13,338 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmplam1y9jb\temp_combined.mp4 -ss 0 -to 5.692 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\57_2.mp4
2025-07-28 22:17:13,665 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 22:17:13,665 - INFO - 目标音频时长: 5.69秒
2025-07-28 22:17:13,665 - INFO - 实际视频时长: 5.74秒
2025-07-28 22:17:13,665 - INFO - 时长差异: 0.05秒 (0.90%)
2025-07-28 22:17:13,665 - INFO - ==========================================
2025-07-28 22:17:13,665 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 22:17:13,665 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\57_2.mp4
2025-07-28 22:17:13,665 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmplam1y9jb
2025-07-28 22:17:13,711 - INFO - 方案 #2 处理完成:
2025-07-28 22:17:13,711 - INFO -   - 音频时长: 5.69秒
2025-07-28 22:17:13,711 - INFO -   - 视频时长: 5.74秒
2025-07-28 22:17:13,711 - INFO -   - 时长差异: 0.05秒 (0.90%)
2025-07-28 22:17:13,711 - INFO - 
字幕 #57 处理完成，成功生成 2/2 套方案
2025-07-28 22:17:13,711 - INFO - 生成的视频文件:
2025-07-28 22:17:13,711 - INFO -   1. F:/github/aicut_auto/newcut_ai\57_1.mp4
2025-07-28 22:17:13,711 - INFO -   2. F:/github/aicut_auto/newcut_ai\57_2.mp4
2025-07-28 22:17:13,711 - INFO - ========== 字幕 #57 处理结束 ==========

