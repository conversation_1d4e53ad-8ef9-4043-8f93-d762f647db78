2025-07-28 22:17:03,613 - INFO - ========== 字幕 #45 处理开始 ==========
2025-07-28 22:17:03,614 - INFO - 字幕内容: 老祖当场揭穿，将汤碗直接扣在她脸上，让她自食恶果。
2025-07-28 22:17:03,614 - INFO - 字幕序号: [2086, 2090]
2025-07-28 22:17:03,614 - INFO - 音频文件详情:
2025-07-28 22:17:03,614 - INFO -   - 路径: output\45.wav
2025-07-28 22:17:03,614 - INFO -   - 时长: 4.42秒
2025-07-28 22:17:03,614 - INFO -   - 验证音频时长: 4.42秒
2025-07-28 22:17:03,614 - INFO - 字幕时间戳信息:
2025-07-28 22:17:03,614 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-28 22:17:03,614 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-28 22:17:03,614 - INFO -   - 根据生成的音频时长(4.42秒)已调整字幕时间戳
2025-07-28 22:17:03,614 - INFO - ========== 开始为字幕 #45 生成 6 套场景方案 ==========
2025-07-28 22:17:03,614 - INFO - 开始查找字幕序号 [2086, 2090] 对应的场景，共有 2710 个场景可选
2025-07-28 22:17:03,615 - INFO - 找到related_overlap场景: scene_id=1948, 字幕#2086
2025-07-28 22:17:03,615 - INFO - 找到related_overlap场景: scene_id=1949, 字幕#2086
2025-07-28 22:17:03,615 - INFO - 找到related_overlap场景: scene_id=1951, 字幕#2090
2025-07-28 22:17:03,615 - INFO - 找到related_overlap场景: scene_id=1952, 字幕#2090
2025-07-28 22:17:03,615 - INFO - 字幕 #2086 找到 2 个overlap场景, 0 个between场景
2025-07-28 22:17:03,615 - INFO - 字幕 #2090 找到 2 个overlap场景, 0 个between场景
2025-07-28 22:17:03,615 - INFO - 共收集 4 个未使用的overlap场景和 0 个未使用的between场景
2025-07-28 22:17:03,615 - INFO - 开始生成方案 #1
2025-07-28 22:17:03,615 - INFO - 方案 #1: 为字幕#2086选择初始化overlap场景id=1948
2025-07-28 22:17:03,615 - INFO - 方案 #1: 为字幕#2090选择初始化overlap场景id=1952
2025-07-28 22:17:03,615 - INFO - 方案 #1: 初始选择后，当前总时长=1.12秒
2025-07-28 22:17:03,615 - INFO - 方案 #1: 额外添加overlap场景id=1949, 当前总时长=2.28秒
2025-07-28 22:17:03,615 - INFO - 方案 #1: 额外添加overlap场景id=1951, 当前总时长=4.60秒
2025-07-28 22:17:03,615 - INFO - 方案 #1: 额外between选择后，当前总时长=4.60秒
2025-07-28 22:17:03,615 - INFO - 方案 #1: 场景总时长(4.60秒)大于音频时长(4.42秒)，需要裁剪
2025-07-28 22:17:03,615 - INFO - 调整前总时长: 4.60秒, 目标时长: 4.42秒
2025-07-28 22:17:03,615 - INFO - 需要裁剪 0.18秒
2025-07-28 22:17:03,615 - INFO - 裁剪最长场景ID=1951：从2.32秒裁剪至2.14秒
2025-07-28 22:17:03,615 - INFO - 调整后总时长: 4.42秒，与目标时长差异: 0.00秒
2025-07-28 22:17:03,615 - INFO - 方案 #1 调整/填充后最终总时长: 4.42秒
2025-07-28 22:17:03,615 - INFO - 方案 #1 添加到方案列表
2025-07-28 22:17:03,615 - INFO - 开始生成方案 #2
2025-07-28 22:17:03,615 - WARNING - 方案 #2: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:17:03,615 - INFO - 开始生成方案 #3
2025-07-28 22:17:03,615 - WARNING - 方案 #3: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:17:03,615 - INFO - 开始生成方案 #4
2025-07-28 22:17:03,615 - WARNING - 方案 #4: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:17:03,617 - INFO - 开始生成方案 #5
2025-07-28 22:17:03,617 - WARNING - 方案 #5: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:17:03,617 - INFO - 开始生成方案 #6
2025-07-28 22:17:03,617 - WARNING - 方案 #6: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:17:03,617 - INFO - ========== 字幕 #45 的 1 套有效场景方案生成完成 ==========
2025-07-28 22:17:03,617 - INFO - 
----- 处理字幕 #45 的方案 #1 -----
2025-07-28 22:17:03,617 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\45_1.mp4
2025-07-28 22:17:03,617 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpy7cpx4pe
2025-07-28 22:17:03,617 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1948.mp4 (确认存在: True)
2025-07-28 22:17:03,617 - INFO - 添加场景ID=1948，时长=0.20秒，累计时长=0.20秒
2025-07-28 22:17:03,618 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1952.mp4 (确认存在: True)
2025-07-28 22:17:03,618 - INFO - 添加场景ID=1952，时长=0.92秒，累计时长=1.12秒
2025-07-28 22:17:03,618 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1949.mp4 (确认存在: True)
2025-07-28 22:17:03,618 - INFO - 添加场景ID=1949，时长=1.16秒，累计时长=2.28秒
2025-07-28 22:17:03,618 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1951.mp4 (确认存在: True)
2025-07-28 22:17:03,618 - INFO - 添加场景ID=1951，时长=2.32秒，累计时长=4.60秒
2025-07-28 22:17:03,618 - INFO - 准备合并 4 个场景文件，总时长约 4.60秒
2025-07-28 22:17:03,618 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1948.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1952.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1949.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1951.mp4'

2025-07-28 22:17:03,618 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpy7cpx4pe\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpy7cpx4pe\temp_combined.mp4
2025-07-28 22:17:03,800 - INFO - 合并后的视频时长: 4.69秒，目标音频时长: 4.42秒
2025-07-28 22:17:03,800 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpy7cpx4pe\temp_combined.mp4 -ss 0 -to 4.417 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\45_1.mp4
2025-07-28 22:17:04,119 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 22:17:04,119 - INFO - 目标音频时长: 4.42秒
2025-07-28 22:17:04,119 - INFO - 实际视频时长: 4.46秒
2025-07-28 22:17:04,119 - INFO - 时长差异: 0.05秒 (1.04%)
2025-07-28 22:17:04,119 - INFO - ==========================================
2025-07-28 22:17:04,119 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 22:17:04,119 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\45_1.mp4
2025-07-28 22:17:04,120 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpy7cpx4pe
2025-07-28 22:17:04,164 - INFO - 方案 #1 处理完成:
2025-07-28 22:17:04,164 - INFO -   - 音频时长: 4.42秒
2025-07-28 22:17:04,164 - INFO -   - 视频时长: 4.46秒
2025-07-28 22:17:04,164 - INFO -   - 时长差异: 0.05秒 (1.04%)
2025-07-28 22:17:04,164 - INFO - 
字幕 #45 处理完成，成功生成 1/1 套方案
2025-07-28 22:17:04,164 - INFO - 生成的视频文件:
2025-07-28 22:17:04,164 - INFO -   1. F:/github/aicut_auto/newcut_ai\45_1.mp4
2025-07-28 22:17:04,164 - INFO - ========== 字幕 #45 处理结束 ==========

