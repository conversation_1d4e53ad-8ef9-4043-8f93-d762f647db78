2025-07-28 22:16:42,083 - INFO - ========== 字幕 #17 处理开始 ==========
2025-07-28 22:16:42,083 - INFO - 字幕内容: 甚至计划着利用完二哥后，就让他替自己顶下窃取机密的弥天大罪。
2025-07-28 22:16:42,083 - INFO - 字幕序号: [497, 499]
2025-07-28 22:16:42,084 - INFO - 音频文件详情:
2025-07-28 22:16:42,084 - INFO -   - 路径: output\17.wav
2025-07-28 22:16:42,084 - INFO -   - 时长: 3.22秒
2025-07-28 22:16:42,084 - INFO -   - 验证音频时长: 3.22秒
2025-07-28 22:16:42,084 - INFO - 字幕时间戳信息:
2025-07-28 22:16:42,084 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-28 22:16:42,084 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-28 22:16:42,084 - INFO -   - 根据生成的音频时长(3.22秒)已调整字幕时间戳
2025-07-28 22:16:42,084 - INFO - ========== 开始为字幕 #17 生成 6 套场景方案 ==========
2025-07-28 22:16:42,084 - INFO - 开始查找字幕序号 [497, 499] 对应的场景，共有 2710 个场景可选
2025-07-28 22:16:42,084 - INFO - 找到related_overlap场景: scene_id=516, 字幕#497
2025-07-28 22:16:42,084 - INFO - 找到related_overlap场景: scene_id=518, 字幕#497
2025-07-28 22:16:42,084 - INFO - 找到related_overlap场景: scene_id=519, 字幕#499
2025-07-28 22:16:42,086 - INFO - 字幕 #497 找到 2 个overlap场景, 0 个between场景
2025-07-28 22:16:42,086 - INFO - 字幕 #499 找到 1 个overlap场景, 0 个between场景
2025-07-28 22:16:42,086 - INFO - 共收集 3 个未使用的overlap场景和 0 个未使用的between场景
2025-07-28 22:16:42,086 - INFO - 开始生成方案 #1
2025-07-28 22:16:42,086 - INFO - 方案 #1: 为字幕#497选择初始化overlap场景id=516
2025-07-28 22:16:42,086 - INFO - 方案 #1: 为字幕#499选择初始化overlap场景id=519
2025-07-28 22:16:42,086 - INFO - 方案 #1: 初始选择后，当前总时长=5.28秒
2025-07-28 22:16:42,086 - INFO - 方案 #1: 额外between选择后，当前总时长=5.28秒
2025-07-28 22:16:42,086 - INFO - 方案 #1: 场景总时长(5.28秒)大于音频时长(3.22秒)，需要裁剪
2025-07-28 22:16:42,086 - INFO - 调整前总时长: 5.28秒, 目标时长: 3.22秒
2025-07-28 22:16:42,086 - INFO - 需要裁剪 2.06秒
2025-07-28 22:16:42,086 - INFO - 裁剪最长场景ID=519：从3.08秒裁剪至1.02秒
2025-07-28 22:16:42,086 - INFO - 调整后总时长: 3.22秒，与目标时长差异: 0.00秒
2025-07-28 22:16:42,086 - INFO - 方案 #1 调整/填充后最终总时长: 3.22秒
2025-07-28 22:16:42,086 - INFO - 方案 #1 添加到方案列表
2025-07-28 22:16:42,086 - INFO - 开始生成方案 #2
2025-07-28 22:16:42,086 - INFO - 方案 #2: 为字幕#497选择初始化overlap场景id=518
2025-07-28 22:16:42,086 - INFO - 方案 #2: 初始选择后，当前总时长=1.88秒
2025-07-28 22:16:42,086 - INFO - 方案 #2: 额外between选择后，当前总时长=1.88秒
2025-07-28 22:16:42,086 - INFO - 方案 #2: 场景总时长(1.88秒)小于音频时长(3.22秒)，需要延伸填充
2025-07-28 22:16:42,086 - INFO - 方案 #2: 最后一个场景ID: 518
2025-07-28 22:16:42,086 - INFO - 方案 #2: 找到最后一个场景在原始列表中的索引: 517
2025-07-28 22:16:42,086 - INFO - 方案 #2: 需要填充时长: 1.34秒
2025-07-28 22:16:42,086 - INFO - 方案 #2: 跳过已使用的场景: scene_id=519
2025-07-28 22:16:42,086 - INFO - 方案 #2: 追加场景 scene_id=520 (裁剪至 1.34秒)
2025-07-28 22:16:42,086 - INFO - 方案 #2: 成功填充至目标时长
2025-07-28 22:16:42,086 - INFO - 方案 #2 调整/填充后最终总时长: 3.22秒
2025-07-28 22:16:42,086 - INFO - 方案 #2 添加到方案列表
2025-07-28 22:16:42,086 - INFO - 开始生成方案 #3
2025-07-28 22:16:42,086 - WARNING - 方案 #3: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:42,086 - INFO - 开始生成方案 #4
2025-07-28 22:16:42,086 - WARNING - 方案 #4: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:42,086 - INFO - 开始生成方案 #5
2025-07-28 22:16:42,086 - WARNING - 方案 #5: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:42,086 - INFO - 开始生成方案 #6
2025-07-28 22:16:42,086 - WARNING - 方案 #6: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:42,086 - INFO - ========== 字幕 #17 的 2 套有效场景方案生成完成 ==========
2025-07-28 22:16:42,086 - INFO - 
----- 处理字幕 #17 的方案 #1 -----
2025-07-28 22:16:42,086 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\17_1.mp4
2025-07-28 22:16:42,087 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp2xfzqvxz
2025-07-28 22:16:42,087 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\516.mp4 (确认存在: True)
2025-07-28 22:16:42,087 - INFO - 添加场景ID=516，时长=2.20秒，累计时长=2.20秒
2025-07-28 22:16:42,087 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\519.mp4 (确认存在: True)
2025-07-28 22:16:42,087 - INFO - 添加场景ID=519，时长=3.08秒，累计时长=5.28秒
2025-07-28 22:16:42,087 - INFO - 场景总时长(5.28秒)已达到音频时长(3.22秒)的1.5倍，停止添加场景
2025-07-28 22:16:42,087 - INFO - 准备合并 2 个场景文件，总时长约 5.28秒
2025-07-28 22:16:42,087 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/516.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/519.mp4'

2025-07-28 22:16:42,087 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp2xfzqvxz\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp2xfzqvxz\temp_combined.mp4
2025-07-28 22:16:42,220 - INFO - 合并后的视频时长: 5.33秒，目标音频时长: 3.22秒
2025-07-28 22:16:42,220 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp2xfzqvxz\temp_combined.mp4 -ss 0 -to 3.223 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\17_1.mp4
2025-07-28 22:16:42,491 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 22:16:42,491 - INFO - 目标音频时长: 3.22秒
2025-07-28 22:16:42,491 - INFO - 实际视频时长: 3.26秒
2025-07-28 22:16:42,491 - INFO - 时长差异: 0.04秒 (1.24%)
2025-07-28 22:16:42,491 - INFO - ==========================================
2025-07-28 22:16:42,491 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 22:16:42,491 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\17_1.mp4
2025-07-28 22:16:42,492 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp2xfzqvxz
2025-07-28 22:16:42,539 - INFO - 方案 #1 处理完成:
2025-07-28 22:16:42,539 - INFO -   - 音频时长: 3.22秒
2025-07-28 22:16:42,539 - INFO -   - 视频时长: 3.26秒
2025-07-28 22:16:42,539 - INFO -   - 时长差异: 0.04秒 (1.24%)
2025-07-28 22:16:42,539 - INFO - 
----- 处理字幕 #17 的方案 #2 -----
2025-07-28 22:16:42,539 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\17_2.mp4
2025-07-28 22:16:42,539 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpcev8zkn0
2025-07-28 22:16:42,540 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\518.mp4 (确认存在: True)
2025-07-28 22:16:42,540 - INFO - 添加场景ID=518，时长=1.88秒，累计时长=1.88秒
2025-07-28 22:16:42,540 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\520.mp4 (确认存在: True)
2025-07-28 22:16:42,540 - INFO - 添加场景ID=520，时长=2.40秒，累计时长=4.28秒
2025-07-28 22:16:42,540 - INFO - 准备合并 2 个场景文件，总时长约 4.28秒
2025-07-28 22:16:42,540 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/518.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/520.mp4'

2025-07-28 22:16:42,540 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpcev8zkn0\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpcev8zkn0\temp_combined.mp4
2025-07-28 22:16:42,692 - INFO - 合并后的视频时长: 4.33秒，目标音频时长: 3.22秒
2025-07-28 22:16:42,692 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpcev8zkn0\temp_combined.mp4 -ss 0 -to 3.223 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\17_2.mp4
2025-07-28 22:16:42,964 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 22:16:42,964 - INFO - 目标音频时长: 3.22秒
2025-07-28 22:16:42,964 - INFO - 实际视频时长: 3.26秒
2025-07-28 22:16:42,964 - INFO - 时长差异: 0.04秒 (1.24%)
2025-07-28 22:16:42,964 - INFO - ==========================================
2025-07-28 22:16:42,964 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 22:16:42,964 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\17_2.mp4
2025-07-28 22:16:42,964 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpcev8zkn0
2025-07-28 22:16:43,019 - INFO - 方案 #2 处理完成:
2025-07-28 22:16:43,019 - INFO -   - 音频时长: 3.22秒
2025-07-28 22:16:43,020 - INFO -   - 视频时长: 3.26秒
2025-07-28 22:16:43,020 - INFO -   - 时长差异: 0.04秒 (1.24%)
2025-07-28 22:16:43,020 - INFO - 
字幕 #17 处理完成，成功生成 2/2 套方案
2025-07-28 22:16:43,020 - INFO - 生成的视频文件:
2025-07-28 22:16:43,020 - INFO -   1. F:/github/aicut_auto/newcut_ai\17_1.mp4
2025-07-28 22:16:43,020 - INFO -   2. F:/github/aicut_auto/newcut_ai\17_2.mp4
2025-07-28 22:16:43,020 - INFO - ========== 字幕 #17 处理结束 ==========

