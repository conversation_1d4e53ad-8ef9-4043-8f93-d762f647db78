2025-07-28 22:17:10,324 - INFO - ========== 字幕 #55 处理开始 ==========
2025-07-28 22:17:10,324 - INFO - 字幕内容: 在皇子挑选之时，老祖施法让他鬼使神差般地越过众人，径直走向了心机女。
2025-07-28 22:17:10,324 - INFO - 字幕序号: [2648, 2651]
2025-07-28 22:17:10,324 - INFO - 音频文件详情:
2025-07-28 22:17:10,324 - INFO -   - 路径: output\55.wav
2025-07-28 22:17:10,324 - INFO -   - 时长: 6.07秒
2025-07-28 22:17:10,325 - INFO -   - 验证音频时长: 6.07秒
2025-07-28 22:17:10,325 - INFO - 字幕时间戳信息:
2025-07-28 22:17:10,325 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-28 22:17:10,325 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-28 22:17:10,325 - INFO -   - 根据生成的音频时长(6.07秒)已调整字幕时间戳
2025-07-28 22:17:10,325 - INFO - ========== 开始为字幕 #55 生成 6 套场景方案 ==========
2025-07-28 22:17:10,325 - INFO - 开始查找字幕序号 [2648, 2651] 对应的场景，共有 2710 个场景可选
2025-07-28 22:17:10,325 - INFO - 找到related_overlap场景: scene_id=2523, 字幕#2648
2025-07-28 22:17:10,325 - INFO - 找到related_overlap场景: scene_id=2524, 字幕#2648
2025-07-28 22:17:10,325 - INFO - 找到related_overlap场景: scene_id=2525, 字幕#2651
2025-07-28 22:17:10,325 - INFO - 找到related_overlap场景: scene_id=2526, 字幕#2651
2025-07-28 22:17:10,327 - INFO - 字幕 #2648 找到 2 个overlap场景, 0 个between场景
2025-07-28 22:17:10,327 - INFO - 字幕 #2651 找到 2 个overlap场景, 0 个between场景
2025-07-28 22:17:10,327 - INFO - 共收集 4 个未使用的overlap场景和 0 个未使用的between场景
2025-07-28 22:17:10,327 - INFO - 开始生成方案 #1
2025-07-28 22:17:10,327 - INFO - 方案 #1: 为字幕#2648选择初始化overlap场景id=2524
2025-07-28 22:17:10,327 - INFO - 方案 #1: 为字幕#2651选择初始化overlap场景id=2525
2025-07-28 22:17:10,327 - INFO - 方案 #1: 初始选择后，当前总时长=5.28秒
2025-07-28 22:17:10,327 - INFO - 方案 #1: 额外添加overlap场景id=2523, 当前总时长=6.92秒
2025-07-28 22:17:10,327 - INFO - 方案 #1: 额外between选择后，当前总时长=6.92秒
2025-07-28 22:17:10,327 - INFO - 方案 #1: 场景总时长(6.92秒)大于音频时长(6.07秒)，需要裁剪
2025-07-28 22:17:10,327 - INFO - 调整前总时长: 6.92秒, 目标时长: 6.07秒
2025-07-28 22:17:10,327 - INFO - 需要裁剪 0.85秒
2025-07-28 22:17:10,327 - INFO - 裁剪最长场景ID=2525：从2.72秒裁剪至1.87秒
2025-07-28 22:17:10,327 - INFO - 调整后总时长: 6.07秒，与目标时长差异: 0.00秒
2025-07-28 22:17:10,327 - INFO - 方案 #1 调整/填充后最终总时长: 6.07秒
2025-07-28 22:17:10,327 - INFO - 方案 #1 添加到方案列表
2025-07-28 22:17:10,327 - INFO - 开始生成方案 #2
2025-07-28 22:17:10,327 - INFO - 方案 #2: 为字幕#2651选择初始化overlap场景id=2526
2025-07-28 22:17:10,327 - INFO - 方案 #2: 初始选择后，当前总时长=2.20秒
2025-07-28 22:17:10,327 - INFO - 方案 #2: 额外between选择后，当前总时长=2.20秒
2025-07-28 22:17:10,327 - INFO - 方案 #2: 场景总时长(2.20秒)小于音频时长(6.07秒)，需要延伸填充
2025-07-28 22:17:10,327 - INFO - 方案 #2: 最后一个场景ID: 2526
2025-07-28 22:17:10,327 - INFO - 方案 #2: 找到最后一个场景在原始列表中的索引: 2525
2025-07-28 22:17:10,327 - INFO - 方案 #2: 需要填充时长: 3.87秒
2025-07-28 22:17:10,327 - INFO - 方案 #2: 追加场景 scene_id=2527 (完整时长 1.40秒)
2025-07-28 22:17:10,327 - INFO - 方案 #2: 追加场景 scene_id=2528 (完整时长 1.84秒)
2025-07-28 22:17:10,327 - INFO - 方案 #2: 追加场景 scene_id=2529 (裁剪至 0.63秒)
2025-07-28 22:17:10,327 - INFO - 方案 #2: 成功填充至目标时长
2025-07-28 22:17:10,327 - INFO - 方案 #2 调整/填充后最终总时长: 6.07秒
2025-07-28 22:17:10,327 - INFO - 方案 #2 添加到方案列表
2025-07-28 22:17:10,327 - INFO - 开始生成方案 #3
2025-07-28 22:17:10,327 - WARNING - 方案 #3: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:17:10,327 - INFO - 开始生成方案 #4
2025-07-28 22:17:10,327 - WARNING - 方案 #4: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:17:10,327 - INFO - 开始生成方案 #5
2025-07-28 22:17:10,327 - WARNING - 方案 #5: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:17:10,327 - INFO - 开始生成方案 #6
2025-07-28 22:17:10,327 - WARNING - 方案 #6: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:17:10,327 - INFO - ========== 字幕 #55 的 2 套有效场景方案生成完成 ==========
2025-07-28 22:17:10,328 - INFO - 
----- 处理字幕 #55 的方案 #1 -----
2025-07-28 22:17:10,328 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\55_1.mp4
2025-07-28 22:17:10,328 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp6rt4qd9w
2025-07-28 22:17:10,328 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2524.mp4 (确认存在: True)
2025-07-28 22:17:10,328 - INFO - 添加场景ID=2524，时长=2.56秒，累计时长=2.56秒
2025-07-28 22:17:10,328 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2525.mp4 (确认存在: True)
2025-07-28 22:17:10,328 - INFO - 添加场景ID=2525，时长=2.72秒，累计时长=5.28秒
2025-07-28 22:17:10,328 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2523.mp4 (确认存在: True)
2025-07-28 22:17:10,328 - INFO - 添加场景ID=2523，时长=1.64秒，累计时长=6.92秒
2025-07-28 22:17:10,329 - INFO - 准备合并 3 个场景文件，总时长约 6.92秒
2025-07-28 22:17:10,329 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2524.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2525.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2523.mp4'

2025-07-28 22:17:10,329 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp6rt4qd9w\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp6rt4qd9w\temp_combined.mp4
2025-07-28 22:17:10,475 - INFO - 合并后的视频时长: 6.99秒，目标音频时长: 6.07秒
2025-07-28 22:17:10,475 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp6rt4qd9w\temp_combined.mp4 -ss 0 -to 6.07 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\55_1.mp4
2025-07-28 22:17:10,827 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 22:17:10,827 - INFO - 目标音频时长: 6.07秒
2025-07-28 22:17:10,827 - INFO - 实际视频时长: 6.10秒
2025-07-28 22:17:10,827 - INFO - 时长差异: 0.03秒 (0.54%)
2025-07-28 22:17:10,827 - INFO - ==========================================
2025-07-28 22:17:10,827 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 22:17:10,827 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\55_1.mp4
2025-07-28 22:17:10,828 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp6rt4qd9w
2025-07-28 22:17:10,873 - INFO - 方案 #1 处理完成:
2025-07-28 22:17:10,873 - INFO -   - 音频时长: 6.07秒
2025-07-28 22:17:10,873 - INFO -   - 视频时长: 6.10秒
2025-07-28 22:17:10,873 - INFO -   - 时长差异: 0.03秒 (0.54%)
2025-07-28 22:17:10,873 - INFO - 
----- 处理字幕 #55 的方案 #2 -----
2025-07-28 22:17:10,873 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\55_2.mp4
2025-07-28 22:17:10,873 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmptaa52k1c
2025-07-28 22:17:10,873 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2526.mp4 (确认存在: True)
2025-07-28 22:17:10,873 - INFO - 添加场景ID=2526，时长=2.20秒，累计时长=2.20秒
2025-07-28 22:17:10,873 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2527.mp4 (确认存在: True)
2025-07-28 22:17:10,873 - INFO - 添加场景ID=2527，时长=1.40秒，累计时长=3.60秒
2025-07-28 22:17:10,873 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2528.mp4 (确认存在: True)
2025-07-28 22:17:10,873 - INFO - 添加场景ID=2528，时长=1.84秒，累计时长=5.44秒
2025-07-28 22:17:10,873 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2529.mp4 (确认存在: True)
2025-07-28 22:17:10,873 - INFO - 添加场景ID=2529，时长=1.40秒，累计时长=6.84秒
2025-07-28 22:17:10,873 - INFO - 准备合并 4 个场景文件，总时长约 6.84秒
2025-07-28 22:17:10,873 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2526.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2527.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2528.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2529.mp4'

2025-07-28 22:17:10,875 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmptaa52k1c\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmptaa52k1c\temp_combined.mp4
2025-07-28 22:17:11,057 - INFO - 合并后的视频时长: 6.93秒，目标音频时长: 6.07秒
2025-07-28 22:17:11,057 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmptaa52k1c\temp_combined.mp4 -ss 0 -to 6.07 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\55_2.mp4
2025-07-28 22:17:11,440 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 22:17:11,440 - INFO - 目标音频时长: 6.07秒
2025-07-28 22:17:11,440 - INFO - 实际视频时长: 6.10秒
2025-07-28 22:17:11,440 - INFO - 时长差异: 0.03秒 (0.54%)
2025-07-28 22:17:11,440 - INFO - ==========================================
2025-07-28 22:17:11,440 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 22:17:11,440 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\55_2.mp4
2025-07-28 22:17:11,441 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmptaa52k1c
2025-07-28 22:17:11,483 - INFO - 方案 #2 处理完成:
2025-07-28 22:17:11,483 - INFO -   - 音频时长: 6.07秒
2025-07-28 22:17:11,483 - INFO -   - 视频时长: 6.10秒
2025-07-28 22:17:11,483 - INFO -   - 时长差异: 0.03秒 (0.54%)
2025-07-28 22:17:11,483 - INFO - 
字幕 #55 处理完成，成功生成 2/2 套方案
2025-07-28 22:17:11,483 - INFO - 生成的视频文件:
2025-07-28 22:17:11,483 - INFO -   1. F:/github/aicut_auto/newcut_ai\55_1.mp4
2025-07-28 22:17:11,483 - INFO -   2. F:/github/aicut_auto/newcut_ai\55_2.mp4
2025-07-28 22:17:11,483 - INFO - ========== 字幕 #55 处理结束 ==========

