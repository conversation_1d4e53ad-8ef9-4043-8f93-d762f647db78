2025-07-28 22:16:38,810 - INFO - ========== 字幕 #12 处理开始 ==========
2025-07-28 22:16:38,810 - INFO - 字幕内容: 此话一出，侯爷瞬间百口莫辩，侯府夫人当场震怒，一场家庭风暴就此引爆。
2025-07-28 22:16:38,810 - INFO - 字幕序号: [302, 303]
2025-07-28 22:16:38,810 - INFO - 音频文件详情:
2025-07-28 22:16:38,810 - INFO -   - 路径: output\12.wav
2025-07-28 22:16:38,810 - INFO -   - 时长: 3.99秒
2025-07-28 22:16:38,810 - INFO -   - 验证音频时长: 3.99秒
2025-07-28 22:16:38,811 - INFO - 字幕时间戳信息:
2025-07-28 22:16:38,820 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-28 22:16:38,821 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-28 22:16:38,821 - INFO -   - 根据生成的音频时长(3.99秒)已调整字幕时间戳
2025-07-28 22:16:38,821 - INFO - ========== 开始为字幕 #12 生成 6 套场景方案 ==========
2025-07-28 22:16:38,821 - INFO - 开始查找字幕序号 [302, 303] 对应的场景，共有 2710 个场景可选
2025-07-28 22:16:38,821 - INFO - 找到related_overlap场景: scene_id=327, 字幕#302
2025-07-28 22:16:38,821 - INFO - 找到related_overlap场景: scene_id=328, 字幕#302
2025-07-28 22:16:38,821 - INFO - 找到related_overlap场景: scene_id=329, 字幕#303
2025-07-28 22:16:38,821 - INFO - 找到related_between场景: scene_id=330, 字幕#303
2025-07-28 22:16:38,821 - INFO - 找到related_between场景: scene_id=331, 字幕#303
2025-07-28 22:16:38,822 - INFO - 字幕 #302 找到 2 个overlap场景, 0 个between场景
2025-07-28 22:16:38,822 - INFO - 字幕 #303 找到 1 个overlap场景, 2 个between场景
2025-07-28 22:16:38,822 - INFO - 共收集 3 个未使用的overlap场景和 2 个未使用的between场景
2025-07-28 22:16:38,822 - INFO - 开始生成方案 #1
2025-07-28 22:16:38,822 - INFO - 方案 #1: 为字幕#302选择初始化overlap场景id=327
2025-07-28 22:16:38,822 - INFO - 方案 #1: 为字幕#303选择初始化overlap场景id=329
2025-07-28 22:16:38,822 - INFO - 方案 #1: 初始选择后，当前总时长=3.16秒
2025-07-28 22:16:38,822 - INFO - 方案 #1: 额外添加overlap场景id=328, 当前总时长=4.52秒
2025-07-28 22:16:38,822 - INFO - 方案 #1: 额外between选择后，当前总时长=4.52秒
2025-07-28 22:16:38,822 - INFO - 方案 #1: 场景总时长(4.52秒)大于音频时长(3.99秒)，需要裁剪
2025-07-28 22:16:38,822 - INFO - 调整前总时长: 4.52秒, 目标时长: 3.99秒
2025-07-28 22:16:38,822 - INFO - 需要裁剪 0.53秒
2025-07-28 22:16:38,822 - INFO - 裁剪最长场景ID=329：从1.68秒裁剪至1.15秒
2025-07-28 22:16:38,822 - INFO - 调整后总时长: 3.99秒，与目标时长差异: 0.00秒
2025-07-28 22:16:38,822 - INFO - 方案 #1 调整/填充后最终总时长: 3.99秒
2025-07-28 22:16:38,822 - INFO - 方案 #1 添加到方案列表
2025-07-28 22:16:38,822 - INFO - 开始生成方案 #2
2025-07-28 22:16:38,822 - INFO - 方案 #2: 初始选择后，当前总时长=0.00秒
2025-07-28 22:16:38,822 - INFO - 方案 #2: 为字幕#303选择初始化between场景id=330
2025-07-28 22:16:38,822 - INFO - 方案 #2: 额外between选择后，当前总时长=1.28秒
2025-07-28 22:16:38,822 - INFO - 方案 #2: 额外添加between场景id=331, 当前总时长=3.84秒
2025-07-28 22:16:38,822 - INFO - 方案 #2: 场景总时长(3.84秒)小于音频时长(3.99秒)，需要延伸填充
2025-07-28 22:16:38,822 - INFO - 方案 #2: 最后一个场景ID: 331
2025-07-28 22:16:38,822 - INFO - 方案 #2: 找到最后一个场景在原始列表中的索引: 330
2025-07-28 22:16:38,822 - INFO - 方案 #2: 需要填充时长: 0.15秒
2025-07-28 22:16:38,822 - INFO - 方案 #2: 追加场景 scene_id=332 (裁剪至 0.15秒)
2025-07-28 22:16:38,822 - INFO - 方案 #2: 成功填充至目标时长
2025-07-28 22:16:38,822 - INFO - 方案 #2 调整/填充后最终总时长: 3.99秒
2025-07-28 22:16:38,822 - INFO - 方案 #2 添加到方案列表
2025-07-28 22:16:38,822 - INFO - 开始生成方案 #3
2025-07-28 22:16:38,822 - WARNING - 方案 #3: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:38,823 - INFO - 开始生成方案 #4
2025-07-28 22:16:38,823 - WARNING - 方案 #4: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:38,823 - INFO - 开始生成方案 #5
2025-07-28 22:16:38,823 - WARNING - 方案 #5: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:38,823 - INFO - 开始生成方案 #6
2025-07-28 22:16:38,823 - WARNING - 方案 #6: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:38,823 - INFO - ========== 字幕 #12 的 2 套有效场景方案生成完成 ==========
2025-07-28 22:16:38,823 - INFO - 
----- 处理字幕 #12 的方案 #1 -----
2025-07-28 22:16:38,823 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\12_1.mp4
2025-07-28 22:16:38,823 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp_4f9dob0
2025-07-28 22:16:38,824 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\327.mp4 (确认存在: True)
2025-07-28 22:16:38,824 - INFO - 添加场景ID=327，时长=1.48秒，累计时长=1.48秒
2025-07-28 22:16:38,824 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\329.mp4 (确认存在: True)
2025-07-28 22:16:38,824 - INFO - 添加场景ID=329，时长=1.68秒，累计时长=3.16秒
2025-07-28 22:16:38,824 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\328.mp4 (确认存在: True)
2025-07-28 22:16:38,824 - INFO - 添加场景ID=328，时长=1.36秒，累计时长=4.52秒
2025-07-28 22:16:38,824 - INFO - 准备合并 3 个场景文件，总时长约 4.52秒
2025-07-28 22:16:38,824 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/327.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/329.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/328.mp4'

2025-07-28 22:16:38,824 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp_4f9dob0\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp_4f9dob0\temp_combined.mp4
2025-07-28 22:16:39,012 - INFO - 合并后的视频时长: 4.59秒，目标音频时长: 3.99秒
2025-07-28 22:16:39,012 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp_4f9dob0\temp_combined.mp4 -ss 0 -to 3.989 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\12_1.mp4
2025-07-28 22:16:39,336 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 22:16:39,337 - INFO - 目标音频时长: 3.99秒
2025-07-28 22:16:39,337 - INFO - 实际视频时长: 4.02秒
2025-07-28 22:16:39,337 - INFO - 时长差异: 0.03秒 (0.85%)
2025-07-28 22:16:39,337 - INFO - ==========================================
2025-07-28 22:16:39,337 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 22:16:39,337 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\12_1.mp4
2025-07-28 22:16:39,337 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp_4f9dob0
2025-07-28 22:16:39,391 - INFO - 方案 #1 处理完成:
2025-07-28 22:16:39,391 - INFO -   - 音频时长: 3.99秒
2025-07-28 22:16:39,391 - INFO -   - 视频时长: 4.02秒
2025-07-28 22:16:39,391 - INFO -   - 时长差异: 0.03秒 (0.85%)
2025-07-28 22:16:39,392 - INFO - 
----- 处理字幕 #12 的方案 #2 -----
2025-07-28 22:16:39,392 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\12_2.mp4
2025-07-28 22:16:39,392 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp5rl83ivv
2025-07-28 22:16:39,393 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\330.mp4 (确认存在: True)
2025-07-28 22:16:39,393 - INFO - 添加场景ID=330，时长=1.28秒，累计时长=1.28秒
2025-07-28 22:16:39,393 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\331.mp4 (确认存在: True)
2025-07-28 22:16:39,393 - INFO - 添加场景ID=331，时长=2.56秒，累计时长=3.84秒
2025-07-28 22:16:39,393 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\332.mp4 (确认存在: True)
2025-07-28 22:16:39,393 - INFO - 添加场景ID=332，时长=1.24秒，累计时长=5.08秒
2025-07-28 22:16:39,393 - INFO - 准备合并 3 个场景文件，总时长约 5.08秒
2025-07-28 22:16:39,393 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/330.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/331.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/332.mp4'

2025-07-28 22:16:39,393 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp5rl83ivv\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp5rl83ivv\temp_combined.mp4
2025-07-28 22:16:39,560 - INFO - 合并后的视频时长: 5.13秒，目标音频时长: 3.99秒
2025-07-28 22:16:39,561 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp5rl83ivv\temp_combined.mp4 -ss 0 -to 3.989 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\12_2.mp4
2025-07-28 22:16:39,843 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 22:16:39,844 - INFO - 目标音频时长: 3.99秒
2025-07-28 22:16:39,844 - INFO - 实际视频时长: 4.02秒
2025-07-28 22:16:39,844 - INFO - 时长差异: 0.03秒 (0.85%)
2025-07-28 22:16:39,844 - INFO - ==========================================
2025-07-28 22:16:39,844 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 22:16:39,844 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\12_2.mp4
2025-07-28 22:16:39,844 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp5rl83ivv
2025-07-28 22:16:39,888 - INFO - 方案 #2 处理完成:
2025-07-28 22:16:39,888 - INFO -   - 音频时长: 3.99秒
2025-07-28 22:16:39,888 - INFO -   - 视频时长: 4.02秒
2025-07-28 22:16:39,888 - INFO -   - 时长差异: 0.03秒 (0.85%)
2025-07-28 22:16:39,888 - INFO - 
字幕 #12 处理完成，成功生成 2/2 套方案
2025-07-28 22:16:39,888 - INFO - 生成的视频文件:
2025-07-28 22:16:39,888 - INFO -   1. F:/github/aicut_auto/newcut_ai\12_1.mp4
2025-07-28 22:16:39,888 - INFO -   2. F:/github/aicut_auto/newcut_ai\12_2.mp4
2025-07-28 22:16:39,888 - INFO - ========== 字幕 #12 处理结束 ==========

