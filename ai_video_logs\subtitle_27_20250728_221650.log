2025-07-28 22:16:50,182 - INFO - ========== 字幕 #27 处理开始 ==========
2025-07-28 22:16:50,182 - INFO - 字幕内容: 心机女找不到符咒，情急之下心声脱口而出：“怎么会没有呢？我明明亲手放的！”
2025-07-28 22:16:50,182 - INFO - 字幕序号: [908, 909]
2025-07-28 22:16:50,182 - INFO - 音频文件详情:
2025-07-28 22:16:50,182 - INFO -   - 路径: output\27.wav
2025-07-28 22:16:50,182 - INFO -   - 时长: 5.56秒
2025-07-28 22:16:50,182 - INFO -   - 验证音频时长: 5.56秒
2025-07-28 22:16:50,182 - INFO - 字幕时间戳信息:
2025-07-28 22:16:50,192 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-28 22:16:50,192 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-28 22:16:50,192 - INFO -   - 根据生成的音频时长(5.56秒)已调整字幕时间戳
2025-07-28 22:16:50,192 - INFO - ========== 开始为字幕 #27 生成 6 套场景方案 ==========
2025-07-28 22:16:50,192 - INFO - 开始查找字幕序号 [908, 909] 对应的场景，共有 2710 个场景可选
2025-07-28 22:16:50,193 - INFO - 找到related_overlap场景: scene_id=936, 字幕#908
2025-07-28 22:16:50,193 - INFO - 找到related_overlap场景: scene_id=937, 字幕#908
2025-07-28 22:16:50,193 - INFO - 找到related_overlap场景: scene_id=938, 字幕#909
2025-07-28 22:16:50,193 - INFO - 字幕 #908 找到 2 个overlap场景, 0 个between场景
2025-07-28 22:16:50,193 - INFO - 字幕 #909 找到 1 个overlap场景, 0 个between场景
2025-07-28 22:16:50,194 - INFO - 共收集 3 个未使用的overlap场景和 0 个未使用的between场景
2025-07-28 22:16:50,194 - INFO - 开始生成方案 #1
2025-07-28 22:16:50,194 - INFO - 方案 #1: 为字幕#908选择初始化overlap场景id=937
2025-07-28 22:16:50,194 - INFO - 方案 #1: 为字幕#909选择初始化overlap场景id=938
2025-07-28 22:16:50,194 - INFO - 方案 #1: 初始选择后，当前总时长=4.72秒
2025-07-28 22:16:50,194 - INFO - 方案 #1: 额外添加overlap场景id=936, 当前总时长=7.04秒
2025-07-28 22:16:50,194 - INFO - 方案 #1: 额外between选择后，当前总时长=7.04秒
2025-07-28 22:16:50,194 - INFO - 方案 #1: 场景总时长(7.04秒)大于音频时长(5.56秒)，需要裁剪
2025-07-28 22:16:50,194 - INFO - 调整前总时长: 7.04秒, 目标时长: 5.56秒
2025-07-28 22:16:50,194 - INFO - 需要裁剪 1.47秒
2025-07-28 22:16:50,194 - INFO - 裁剪最长场景ID=938：从2.52秒裁剪至1.04秒
2025-07-28 22:16:50,194 - INFO - 调整后总时长: 5.56秒，与目标时长差异: 0.00秒
2025-07-28 22:16:50,194 - INFO - 方案 #1 调整/填充后最终总时长: 5.56秒
2025-07-28 22:16:50,194 - INFO - 方案 #1 添加到方案列表
2025-07-28 22:16:50,194 - INFO - 开始生成方案 #2
2025-07-28 22:16:50,194 - WARNING - 方案 #2: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:50,194 - INFO - 开始生成方案 #3
2025-07-28 22:16:50,194 - WARNING - 方案 #3: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:50,194 - INFO - 开始生成方案 #4
2025-07-28 22:16:50,194 - WARNING - 方案 #4: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:50,194 - INFO - 开始生成方案 #5
2025-07-28 22:16:50,194 - WARNING - 方案 #5: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:50,194 - INFO - 开始生成方案 #6
2025-07-28 22:16:50,194 - WARNING - 方案 #6: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:50,194 - INFO - ========== 字幕 #27 的 1 套有效场景方案生成完成 ==========
2025-07-28 22:16:50,194 - INFO - 
----- 处理字幕 #27 的方案 #1 -----
2025-07-28 22:16:50,194 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\27_1.mp4
2025-07-28 22:16:50,194 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpgw6wgayr
2025-07-28 22:16:50,194 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\937.mp4 (确认存在: True)
2025-07-28 22:16:50,194 - INFO - 添加场景ID=937，时长=2.20秒，累计时长=2.20秒
2025-07-28 22:16:50,194 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\938.mp4 (确认存在: True)
2025-07-28 22:16:50,194 - INFO - 添加场景ID=938，时长=2.52秒，累计时长=4.72秒
2025-07-28 22:16:50,196 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\936.mp4 (确认存在: True)
2025-07-28 22:16:50,196 - INFO - 添加场景ID=936，时长=2.32秒，累计时长=7.04秒
2025-07-28 22:16:50,196 - INFO - 准备合并 3 个场景文件，总时长约 7.04秒
2025-07-28 22:16:50,196 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/937.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/938.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/936.mp4'

2025-07-28 22:16:50,196 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpgw6wgayr\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpgw6wgayr\temp_combined.mp4
2025-07-28 22:16:50,346 - INFO - 合并后的视频时长: 7.11秒，目标音频时长: 5.56秒
2025-07-28 22:16:50,346 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpgw6wgayr\temp_combined.mp4 -ss 0 -to 5.564 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\27_1.mp4
2025-07-28 22:16:50,708 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 22:16:50,708 - INFO - 目标音频时长: 5.56秒
2025-07-28 22:16:50,708 - INFO - 实际视频时长: 5.62秒
2025-07-28 22:16:50,708 - INFO - 时长差异: 0.06秒 (1.06%)
2025-07-28 22:16:50,708 - INFO - ==========================================
2025-07-28 22:16:50,708 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 22:16:50,708 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\27_1.mp4
2025-07-28 22:16:50,709 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpgw6wgayr
2025-07-28 22:16:50,753 - INFO - 方案 #1 处理完成:
2025-07-28 22:16:50,753 - INFO -   - 音频时长: 5.56秒
2025-07-28 22:16:50,753 - INFO -   - 视频时长: 5.62秒
2025-07-28 22:16:50,753 - INFO -   - 时长差异: 0.06秒 (1.06%)
2025-07-28 22:16:50,754 - INFO - 
字幕 #27 处理完成，成功生成 1/1 套方案
2025-07-28 22:16:50,754 - INFO - 生成的视频文件:
2025-07-28 22:16:50,754 - INFO -   1. F:/github/aicut_auto/newcut_ai\27_1.mp4
2025-07-28 22:16:50,754 - INFO - ========== 字幕 #27 处理结束 ==========

