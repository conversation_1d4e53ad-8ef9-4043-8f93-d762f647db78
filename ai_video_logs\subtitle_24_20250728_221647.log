2025-07-28 22:16:47,459 - INFO - ========== 字幕 #24 处理开始 ==========
2025-07-28 22:16:47,459 - INFO - 字幕内容: 一计不成又生一计，心机女拿出邪恶的“鸠夺符”，企图栽赃真千金要吸走父母的气运。
2025-07-28 22:16:47,459 - INFO - 字幕序号: [820, 825]
2025-07-28 22:16:47,460 - INFO - 音频文件详情:
2025-07-28 22:16:47,460 - INFO -   - 路径: output\24.wav
2025-07-28 22:16:47,460 - INFO -   - 时长: 6.70秒
2025-07-28 22:16:47,460 - INFO -   - 验证音频时长: 6.70秒
2025-07-28 22:16:47,460 - INFO - 字幕时间戳信息:
2025-07-28 22:16:47,469 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-28 22:16:47,469 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-28 22:16:47,469 - INFO -   - 根据生成的音频时长(6.70秒)已调整字幕时间戳
2025-07-28 22:16:47,469 - INFO - ========== 开始为字幕 #24 生成 6 套场景方案 ==========
2025-07-28 22:16:47,469 - INFO - 开始查找字幕序号 [820, 825] 对应的场景，共有 2710 个场景可选
2025-07-28 22:16:47,470 - INFO - 找到related_overlap场景: scene_id=853, 字幕#820
2025-07-28 22:16:47,470 - INFO - 找到related_overlap场景: scene_id=857, 字幕#825
2025-07-28 22:16:47,470 - INFO - 字幕 #820 找到 1 个overlap场景, 0 个between场景
2025-07-28 22:16:47,471 - INFO - 字幕 #825 找到 1 个overlap场景, 0 个between场景
2025-07-28 22:16:47,471 - INFO - 共收集 2 个未使用的overlap场景和 0 个未使用的between场景
2025-07-28 22:16:47,471 - INFO - 开始生成方案 #1
2025-07-28 22:16:47,471 - INFO - 方案 #1: 为字幕#820选择初始化overlap场景id=853
2025-07-28 22:16:47,471 - INFO - 方案 #1: 为字幕#825选择初始化overlap场景id=857
2025-07-28 22:16:47,471 - INFO - 方案 #1: 初始选择后，当前总时长=3.44秒
2025-07-28 22:16:47,471 - INFO - 方案 #1: 额外between选择后，当前总时长=3.44秒
2025-07-28 22:16:47,471 - INFO - 方案 #1: 场景总时长(3.44秒)小于音频时长(6.70秒)，需要延伸填充
2025-07-28 22:16:47,471 - INFO - 方案 #1: 最后一个场景ID: 857
2025-07-28 22:16:47,471 - INFO - 方案 #1: 找到最后一个场景在原始列表中的索引: 856
2025-07-28 22:16:47,471 - INFO - 方案 #1: 需要填充时长: 3.26秒
2025-07-28 22:16:47,471 - INFO - 方案 #1: 追加场景 scene_id=858 (完整时长 1.44秒)
2025-07-28 22:16:47,471 - INFO - 方案 #1: 追加场景 scene_id=859 (裁剪至 1.82秒)
2025-07-28 22:16:47,471 - INFO - 方案 #1: 成功填充至目标时长
2025-07-28 22:16:47,471 - INFO - 方案 #1 调整/填充后最终总时长: 6.70秒
2025-07-28 22:16:47,471 - INFO - 方案 #1 添加到方案列表
2025-07-28 22:16:47,471 - INFO - 开始生成方案 #2
2025-07-28 22:16:47,471 - WARNING - 方案 #2: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:47,471 - INFO - 开始生成方案 #3
2025-07-28 22:16:47,471 - WARNING - 方案 #3: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:47,471 - INFO - 开始生成方案 #4
2025-07-28 22:16:47,471 - WARNING - 方案 #4: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:47,471 - INFO - 开始生成方案 #5
2025-07-28 22:16:47,471 - WARNING - 方案 #5: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:47,471 - INFO - 开始生成方案 #6
2025-07-28 22:16:47,471 - WARNING - 方案 #6: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:47,471 - INFO - ========== 字幕 #24 的 1 套有效场景方案生成完成 ==========
2025-07-28 22:16:47,471 - INFO - 
----- 处理字幕 #24 的方案 #1 -----
2025-07-28 22:16:47,471 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\24_1.mp4
2025-07-28 22:16:47,471 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmppf5nnjs4
2025-07-28 22:16:47,472 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\853.mp4 (确认存在: True)
2025-07-28 22:16:47,472 - INFO - 添加场景ID=853，时长=1.08秒，累计时长=1.08秒
2025-07-28 22:16:47,472 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\857.mp4 (确认存在: True)
2025-07-28 22:16:47,472 - INFO - 添加场景ID=857，时长=2.36秒，累计时长=3.44秒
2025-07-28 22:16:47,472 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\858.mp4 (确认存在: True)
2025-07-28 22:16:47,472 - INFO - 添加场景ID=858，时长=1.44秒，累计时长=4.88秒
2025-07-28 22:16:47,472 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\859.mp4 (确认存在: True)
2025-07-28 22:16:47,472 - INFO - 添加场景ID=859，时长=2.36秒，累计时长=7.24秒
2025-07-28 22:16:47,472 - INFO - 准备合并 4 个场景文件，总时长约 7.24秒
2025-07-28 22:16:47,472 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/853.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/857.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/858.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/859.mp4'

2025-07-28 22:16:47,473 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmppf5nnjs4\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmppf5nnjs4\temp_combined.mp4
2025-07-28 22:16:47,639 - INFO - 合并后的视频时长: 7.33秒，目标音频时长: 6.70秒
2025-07-28 22:16:47,640 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmppf5nnjs4\temp_combined.mp4 -ss 0 -to 6.7 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\24_1.mp4
2025-07-28 22:16:48,018 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 22:16:48,018 - INFO - 目标音频时长: 6.70秒
2025-07-28 22:16:48,018 - INFO - 实际视频时长: 6.74秒
2025-07-28 22:16:48,018 - INFO - 时长差异: 0.04秒 (0.64%)
2025-07-28 22:16:48,018 - INFO - ==========================================
2025-07-28 22:16:48,018 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 22:16:48,018 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\24_1.mp4
2025-07-28 22:16:48,020 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmppf5nnjs4
2025-07-28 22:16:48,067 - INFO - 方案 #1 处理完成:
2025-07-28 22:16:48,067 - INFO -   - 音频时长: 6.70秒
2025-07-28 22:16:48,067 - INFO -   - 视频时长: 6.74秒
2025-07-28 22:16:48,067 - INFO -   - 时长差异: 0.04秒 (0.64%)
2025-07-28 22:16:48,067 - INFO - 
字幕 #24 处理完成，成功生成 1/1 套方案
2025-07-28 22:16:48,067 - INFO - 生成的视频文件:
2025-07-28 22:16:48,067 - INFO -   1. F:/github/aicut_auto/newcut_ai\24_1.mp4
2025-07-28 22:16:48,067 - INFO - ========== 字幕 #24 处理结束 ==========

