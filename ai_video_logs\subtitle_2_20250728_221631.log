2025-07-28 22:16:31,118 - INFO - ========== 字幕 #2 处理开始 ==========
2025-07-28 22:16:31,118 - INFO - 字幕内容: 开局就被全家厌弃，幸好她觉醒了金手指系统，能提前预知并篡改心机女的心声。
2025-07-28 22:16:31,118 - INFO - 字幕序号: [67, 69]
2025-07-28 22:16:31,118 - INFO - 音频文件详情:
2025-07-28 22:16:31,118 - INFO -   - 路径: output\2.wav
2025-07-28 22:16:31,118 - INFO -   - 时长: 5.62秒
2025-07-28 22:16:31,119 - INFO -   - 验证音频时长: 5.62秒
2025-07-28 22:16:31,119 - INFO - 字幕时间戳信息:
2025-07-28 22:16:31,119 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-28 22:16:31,119 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-28 22:16:31,119 - INFO -   - 根据生成的音频时长(5.62秒)已调整字幕时间戳
2025-07-28 22:16:31,119 - INFO - ========== 开始为字幕 #2 生成 6 套场景方案 ==========
2025-07-28 22:16:31,119 - INFO - 开始查找字幕序号 [67, 69] 对应的场景，共有 2710 个场景可选
2025-07-28 22:16:31,119 - INFO - 找到related_overlap场景: scene_id=73, 字幕#67
2025-07-28 22:16:31,119 - INFO - 找到related_overlap场景: scene_id=74, 字幕#67
2025-07-28 22:16:31,119 - INFO - 找到related_overlap场景: scene_id=75, 字幕#69
2025-07-28 22:16:31,120 - INFO - 找到related_between场景: scene_id=71, 字幕#67
2025-07-28 22:16:31,120 - INFO - 找到related_between场景: scene_id=72, 字幕#67
2025-07-28 22:16:31,120 - INFO - 字幕 #67 找到 2 个overlap场景, 2 个between场景
2025-07-28 22:16:31,120 - INFO - 字幕 #69 找到 1 个overlap场景, 0 个between场景
2025-07-28 22:16:31,120 - INFO - 共收集 3 个未使用的overlap场景和 2 个未使用的between场景
2025-07-28 22:16:31,120 - INFO - 开始生成方案 #1
2025-07-28 22:16:31,120 - INFO - 方案 #1: 为字幕#67选择初始化overlap场景id=74
2025-07-28 22:16:31,120 - INFO - 方案 #1: 为字幕#69选择初始化overlap场景id=75
2025-07-28 22:16:31,120 - INFO - 方案 #1: 初始选择后，当前总时长=4.72秒
2025-07-28 22:16:31,120 - INFO - 方案 #1: 额外添加overlap场景id=73, 当前总时长=9.28秒
2025-07-28 22:16:31,120 - INFO - 方案 #1: 额外between选择后，当前总时长=9.28秒
2025-07-28 22:16:31,120 - INFO - 方案 #1: 场景总时长(9.28秒)大于音频时长(5.62秒)，需要裁剪
2025-07-28 22:16:31,120 - INFO - 调整前总时长: 9.28秒, 目标时长: 5.62秒
2025-07-28 22:16:31,120 - INFO - 需要裁剪 3.66秒
2025-07-28 22:16:31,120 - INFO - 裁剪单个场景会导致时长过短，尝试裁剪多个场景
2025-07-28 22:16:31,120 - INFO - 裁剪场景ID=73：从4.56秒裁剪至1.37秒
2025-07-28 22:16:31,120 - INFO - 裁剪场景ID=74：从3.40秒裁剪至2.93秒
2025-07-28 22:16:31,120 - INFO - 调整后总时长: 5.62秒，与目标时长差异: 0.00秒
2025-07-28 22:16:31,120 - INFO - 方案 #1 调整/填充后最终总时长: 5.62秒
2025-07-28 22:16:31,120 - INFO - 方案 #1 添加到方案列表
2025-07-28 22:16:31,120 - INFO - 开始生成方案 #2
2025-07-28 22:16:31,121 - INFO - 方案 #2: 初始选择后，当前总时长=0.00秒
2025-07-28 22:16:31,121 - INFO - 方案 #2: 为字幕#67选择初始化between场景id=72
2025-07-28 22:16:31,121 - INFO - 方案 #2: 额外between选择后，当前总时长=0.88秒
2025-07-28 22:16:31,121 - INFO - 方案 #2: 额外添加between场景id=71, 当前总时长=2.96秒
2025-07-28 22:16:31,121 - INFO - 方案 #2: 场景总时长(2.96秒)小于音频时长(5.62秒)，需要延伸填充
2025-07-28 22:16:31,121 - INFO - 方案 #2: 最后一个场景ID: 71
2025-07-28 22:16:31,121 - INFO - 方案 #2: 找到最后一个场景在原始列表中的索引: 70
2025-07-28 22:16:31,121 - INFO - 方案 #2: 需要填充时长: 2.66秒
2025-07-28 22:16:31,121 - INFO - 方案 #2: 跳过已使用的场景: scene_id=72
2025-07-28 22:16:31,121 - INFO - 方案 #2: 跳过已使用的场景: scene_id=73
2025-07-28 22:16:31,121 - INFO - 方案 #2: 跳过已使用的场景: scene_id=74
2025-07-28 22:16:31,121 - INFO - 方案 #2: 跳过已使用的场景: scene_id=75
2025-07-28 22:16:31,121 - INFO - 方案 #2: 追加场景 scene_id=76 (裁剪至 2.66秒)
2025-07-28 22:16:31,121 - INFO - 方案 #2: 成功填充至目标时长
2025-07-28 22:16:31,121 - INFO - 方案 #2 调整/填充后最终总时长: 5.62秒
2025-07-28 22:16:31,121 - INFO - 方案 #2 添加到方案列表
2025-07-28 22:16:31,121 - INFO - 开始生成方案 #3
2025-07-28 22:16:31,121 - WARNING - 方案 #3: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:31,121 - INFO - 开始生成方案 #4
2025-07-28 22:16:31,121 - WARNING - 方案 #4: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:31,121 - INFO - 开始生成方案 #5
2025-07-28 22:16:31,121 - WARNING - 方案 #5: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:31,121 - INFO - 开始生成方案 #6
2025-07-28 22:16:31,121 - WARNING - 方案 #6: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:31,121 - INFO - ========== 字幕 #2 的 2 套有效场景方案生成完成 ==========
2025-07-28 22:16:31,121 - INFO - 
----- 处理字幕 #2 的方案 #1 -----
2025-07-28 22:16:31,121 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\2_1.mp4
2025-07-28 22:16:31,121 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp0abjiko0
2025-07-28 22:16:31,122 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\74.mp4 (确认存在: True)
2025-07-28 22:16:31,122 - INFO - 添加场景ID=74，时长=3.40秒，累计时长=3.40秒
2025-07-28 22:16:31,122 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\75.mp4 (确认存在: True)
2025-07-28 22:16:31,122 - INFO - 添加场景ID=75，时长=1.32秒，累计时长=4.72秒
2025-07-28 22:16:31,122 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\73.mp4 (确认存在: True)
2025-07-28 22:16:31,122 - INFO - 添加场景ID=73，时长=4.56秒，累计时长=9.28秒
2025-07-28 22:16:31,122 - INFO - 场景总时长(9.28秒)已达到音频时长(5.62秒)的1.5倍，停止添加场景
2025-07-28 22:16:31,122 - INFO - 准备合并 3 个场景文件，总时长约 9.28秒
2025-07-28 22:16:31,122 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/74.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/75.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/73.mp4'

2025-07-28 22:16:31,122 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp0abjiko0\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp0abjiko0\temp_combined.mp4
2025-07-28 22:16:31,268 - INFO - 合并后的视频时长: 9.35秒，目标音频时长: 5.62秒
2025-07-28 22:16:31,268 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp0abjiko0\temp_combined.mp4 -ss 0 -to 5.619 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\2_1.mp4
2025-07-28 22:16:31,573 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 22:16:31,574 - INFO - 目标音频时长: 5.62秒
2025-07-28 22:16:31,574 - INFO - 实际视频时长: 5.66秒
2025-07-28 22:16:31,574 - INFO - 时长差异: 0.04秒 (0.78%)
2025-07-28 22:16:31,574 - INFO - ==========================================
2025-07-28 22:16:31,574 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 22:16:31,574 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\2_1.mp4
2025-07-28 22:16:31,575 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp0abjiko0
2025-07-28 22:16:31,624 - INFO - 方案 #1 处理完成:
2025-07-28 22:16:31,624 - INFO -   - 音频时长: 5.62秒
2025-07-28 22:16:31,624 - INFO -   - 视频时长: 5.66秒
2025-07-28 22:16:31,624 - INFO -   - 时长差异: 0.04秒 (0.78%)
2025-07-28 22:16:31,624 - INFO - 
----- 处理字幕 #2 的方案 #2 -----
2025-07-28 22:16:31,624 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\2_2.mp4
2025-07-28 22:16:31,625 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpha0nzeoc
2025-07-28 22:16:31,625 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\72.mp4 (确认存在: True)
2025-07-28 22:16:31,625 - INFO - 添加场景ID=72，时长=0.88秒，累计时长=0.88秒
2025-07-28 22:16:31,625 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\71.mp4 (确认存在: True)
2025-07-28 22:16:31,625 - INFO - 添加场景ID=71，时长=2.08秒，累计时长=2.96秒
2025-07-28 22:16:31,625 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\76.mp4 (确认存在: True)
2025-07-28 22:16:31,625 - INFO - 添加场景ID=76，时长=4.40秒，累计时长=7.36秒
2025-07-28 22:16:31,625 - INFO - 准备合并 3 个场景文件，总时长约 7.36秒
2025-07-28 22:16:31,625 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/72.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/71.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/76.mp4'

2025-07-28 22:16:31,625 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpha0nzeoc\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpha0nzeoc\temp_combined.mp4
2025-07-28 22:16:31,803 - INFO - 合并后的视频时长: 7.43秒，目标音频时长: 5.62秒
2025-07-28 22:16:31,803 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpha0nzeoc\temp_combined.mp4 -ss 0 -to 5.619 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\2_2.mp4
2025-07-28 22:16:32,185 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 22:16:32,185 - INFO - 目标音频时长: 5.62秒
2025-07-28 22:16:32,185 - INFO - 实际视频时长: 5.66秒
2025-07-28 22:16:32,185 - INFO - 时长差异: 0.04秒 (0.78%)
2025-07-28 22:16:32,185 - INFO - ==========================================
2025-07-28 22:16:32,185 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 22:16:32,185 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\2_2.mp4
2025-07-28 22:16:32,186 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpha0nzeoc
2025-07-28 22:16:32,227 - INFO - 方案 #2 处理完成:
2025-07-28 22:16:32,227 - INFO -   - 音频时长: 5.62秒
2025-07-28 22:16:32,227 - INFO -   - 视频时长: 5.66秒
2025-07-28 22:16:32,227 - INFO -   - 时长差异: 0.04秒 (0.78%)
2025-07-28 22:16:32,227 - INFO - 
字幕 #2 处理完成，成功生成 2/2 套方案
2025-07-28 22:16:32,227 - INFO - 生成的视频文件:
2025-07-28 22:16:32,227 - INFO -   1. F:/github/aicut_auto/newcut_ai\2_1.mp4
2025-07-28 22:16:32,227 - INFO -   2. F:/github/aicut_auto/newcut_ai\2_2.mp4
2025-07-28 22:16:32,227 - INFO - ========== 字幕 #2 处理结束 ==========

