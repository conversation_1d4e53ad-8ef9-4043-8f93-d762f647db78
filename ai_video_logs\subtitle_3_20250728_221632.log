2025-07-28 22:16:32,228 - INFO - ========== 字幕 #3 处理开始 ==========
2025-07-28 22:16:32,228 - INFO - 字幕内容: 原来，这假千金能用伪造的心声蛊惑人心，让全家都误以为真千金恶毒无比。
2025-07-28 22:16:32,228 - INFO - 字幕序号: [33, 35]
2025-07-28 22:16:32,228 - INFO - 音频文件详情:
2025-07-28 22:16:32,228 - INFO -   - 路径: output\3.wav
2025-07-28 22:16:32,228 - INFO -   - 时长: 5.45秒
2025-07-28 22:16:32,229 - INFO -   - 验证音频时长: 5.45秒
2025-07-28 22:16:32,229 - INFO - 字幕时间戳信息:
2025-07-28 22:16:32,229 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-28 22:16:32,229 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-28 22:16:32,229 - INFO -   - 根据生成的音频时长(5.45秒)已调整字幕时间戳
2025-07-28 22:16:32,229 - INFO - ========== 开始为字幕 #3 生成 6 套场景方案 ==========
2025-07-28 22:16:32,229 - INFO - 开始查找字幕序号 [33, 35] 对应的场景，共有 2710 个场景可选
2025-07-28 22:16:32,229 - INFO - 找到related_overlap场景: scene_id=33, 字幕#33
2025-07-28 22:16:32,229 - INFO - 找到related_overlap场景: scene_id=35, 字幕#35
2025-07-28 22:16:32,229 - INFO - 找到related_between场景: scene_id=36, 字幕#35
2025-07-28 22:16:32,230 - INFO - 找到related_between场景: scene_id=37, 字幕#35
2025-07-28 22:16:32,230 - INFO - 找到related_between场景: scene_id=38, 字幕#35
2025-07-28 22:16:32,230 - INFO - 找到related_between场景: scene_id=39, 字幕#35
2025-07-28 22:16:32,230 - INFO - 找到related_between场景: scene_id=40, 字幕#35
2025-07-28 22:16:32,230 - INFO - 找到related_between场景: scene_id=41, 字幕#35
2025-07-28 22:16:32,230 - INFO - 找到related_between场景: scene_id=42, 字幕#35
2025-07-28 22:16:32,230 - INFO - 找到related_between场景: scene_id=43, 字幕#35
2025-07-28 22:16:32,230 - INFO - 字幕 #33 找到 1 个overlap场景, 0 个between场景
2025-07-28 22:16:32,230 - INFO - 字幕 #35 找到 1 个overlap场景, 8 个between场景
2025-07-28 22:16:32,230 - INFO - 共收集 2 个未使用的overlap场景和 8 个未使用的between场景
2025-07-28 22:16:32,230 - INFO - 开始生成方案 #1
2025-07-28 22:16:32,230 - INFO - 方案 #1: 为字幕#33选择初始化overlap场景id=33
2025-07-28 22:16:32,230 - INFO - 方案 #1: 为字幕#35选择初始化overlap场景id=35
2025-07-28 22:16:32,230 - INFO - 方案 #1: 初始选择后，当前总时长=5.12秒
2025-07-28 22:16:32,230 - INFO - 方案 #1: 额外between选择后，当前总时长=5.12秒
2025-07-28 22:16:32,230 - INFO - 方案 #1: 额外添加between场景id=40, 当前总时长=6.48秒
2025-07-28 22:16:32,230 - INFO - 方案 #1: 场景总时长(6.48秒)大于音频时长(5.45秒)，需要裁剪
2025-07-28 22:16:32,230 - INFO - 调整前总时长: 6.48秒, 目标时长: 5.45秒
2025-07-28 22:16:32,230 - INFO - 需要裁剪 1.03秒
2025-07-28 22:16:32,230 - INFO - 裁剪最长场景ID=33：从3.08秒裁剪至2.05秒
2025-07-28 22:16:32,230 - INFO - 调整后总时长: 5.45秒，与目标时长差异: 0.00秒
2025-07-28 22:16:32,230 - INFO - 方案 #1 调整/填充后最终总时长: 5.45秒
2025-07-28 22:16:32,230 - INFO - 方案 #1 添加到方案列表
2025-07-28 22:16:32,230 - INFO - 开始生成方案 #2
2025-07-28 22:16:32,230 - INFO - 方案 #2: 初始选择后，当前总时长=0.00秒
2025-07-28 22:16:32,230 - INFO - 方案 #2: 为字幕#35选择初始化between场景id=43
2025-07-28 22:16:32,230 - INFO - 方案 #2: 额外between选择后，当前总时长=1.32秒
2025-07-28 22:16:32,230 - INFO - 方案 #2: 额外添加between场景id=42, 当前总时长=2.40秒
2025-07-28 22:16:32,230 - INFO - 方案 #2: 额外添加between场景id=41, 当前总时长=3.48秒
2025-07-28 22:16:32,231 - INFO - 方案 #2: 额外添加between场景id=36, 当前总时长=5.20秒
2025-07-28 22:16:32,231 - INFO - 方案 #2: 额外添加between场景id=39, 当前总时长=7.28秒
2025-07-28 22:16:32,231 - INFO - 方案 #2: 场景总时长(7.28秒)大于音频时长(5.45秒)，需要裁剪
2025-07-28 22:16:32,231 - INFO - 调整前总时长: 7.28秒, 目标时长: 5.45秒
2025-07-28 22:16:32,231 - INFO - 需要裁剪 1.83秒
2025-07-28 22:16:32,231 - INFO - 裁剪单个场景会导致时长过短，尝试裁剪多个场景
2025-07-28 22:16:32,231 - INFO - 裁剪场景ID=39：从2.08秒裁剪至1.00秒
2025-07-28 22:16:32,231 - INFO - 裁剪场景ID=36：从1.72秒裁剪至1.00秒
2025-07-28 22:16:32,231 - INFO - 裁剪场景ID=43：从1.32秒裁剪至1.29秒
2025-07-28 22:16:32,231 - INFO - 调整后总时长: 5.45秒，与目标时长差异: 0.00秒
2025-07-28 22:16:32,231 - INFO - 方案 #2 调整/填充后最终总时长: 5.45秒
2025-07-28 22:16:32,231 - INFO - 方案 #2 添加到方案列表
2025-07-28 22:16:32,231 - INFO - 开始生成方案 #3
2025-07-28 22:16:32,231 - INFO - 方案 #3: 初始选择后，当前总时长=0.00秒
2025-07-28 22:16:32,231 - INFO - 方案 #3: 为字幕#35选择初始化between场景id=38
2025-07-28 22:16:32,231 - INFO - 方案 #3: 额外between选择后，当前总时长=1.08秒
2025-07-28 22:16:32,231 - INFO - 方案 #3: 额外添加between场景id=37, 当前总时长=4.28秒
2025-07-28 22:16:32,231 - INFO - 方案 #3: 场景总时长(4.28秒)小于音频时长(5.45秒)，需要延伸填充
2025-07-28 22:16:32,231 - INFO - 方案 #3: 最后一个场景ID: 37
2025-07-28 22:16:32,231 - INFO - 方案 #3: 找到最后一个场景在原始列表中的索引: 36
2025-07-28 22:16:32,231 - INFO - 方案 #3: 需要填充时长: 1.17秒
2025-07-28 22:16:32,231 - INFO - 方案 #3: 跳过已使用的场景: scene_id=38
2025-07-28 22:16:32,231 - INFO - 方案 #3: 跳过已使用的场景: scene_id=39
2025-07-28 22:16:32,231 - INFO - 方案 #3: 跳过已使用的场景: scene_id=40
2025-07-28 22:16:32,231 - INFO - 方案 #3: 跳过已使用的场景: scene_id=41
2025-07-28 22:16:32,231 - INFO - 方案 #3: 跳过已使用的场景: scene_id=42
2025-07-28 22:16:32,231 - INFO - 方案 #3: 跳过已使用的场景: scene_id=43
2025-07-28 22:16:32,231 - INFO - 方案 #3: 追加场景 scene_id=44 (裁剪至 1.17秒)
2025-07-28 22:16:32,231 - INFO - 方案 #3: 成功填充至目标时长
2025-07-28 22:16:32,231 - INFO - 方案 #3 调整/填充后最终总时长: 5.45秒
2025-07-28 22:16:32,231 - INFO - 方案 #3 添加到方案列表
2025-07-28 22:16:32,231 - INFO - 开始生成方案 #4
2025-07-28 22:16:32,231 - WARNING - 方案 #4: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:32,231 - INFO - 开始生成方案 #5
2025-07-28 22:16:32,231 - WARNING - 方案 #5: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:32,231 - INFO - 开始生成方案 #6
2025-07-28 22:16:32,231 - WARNING - 方案 #6: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:32,231 - INFO - ========== 字幕 #3 的 3 套有效场景方案生成完成 ==========
2025-07-28 22:16:32,231 - INFO - 
----- 处理字幕 #3 的方案 #1 -----
2025-07-28 22:16:32,231 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\3_1.mp4
2025-07-28 22:16:32,231 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpe4x7h2e0
2025-07-28 22:16:32,232 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\33.mp4 (确认存在: True)
2025-07-28 22:16:32,232 - INFO - 添加场景ID=33，时长=3.08秒，累计时长=3.08秒
2025-07-28 22:16:32,232 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\35.mp4 (确认存在: True)
2025-07-28 22:16:32,232 - INFO - 添加场景ID=35，时长=2.04秒，累计时长=5.12秒
2025-07-28 22:16:32,232 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\40.mp4 (确认存在: True)
2025-07-28 22:16:32,232 - INFO - 添加场景ID=40，时长=1.36秒，累计时长=6.48秒
2025-07-28 22:16:32,232 - INFO - 准备合并 3 个场景文件，总时长约 6.48秒
2025-07-28 22:16:32,232 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/33.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/35.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/40.mp4'

2025-07-28 22:16:32,232 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpe4x7h2e0\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpe4x7h2e0\temp_combined.mp4
2025-07-28 22:16:32,372 - INFO - 合并后的视频时长: 6.55秒，目标音频时长: 5.45秒
2025-07-28 22:16:32,372 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpe4x7h2e0\temp_combined.mp4 -ss 0 -to 5.447 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\3_1.mp4
2025-07-28 22:16:32,691 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 22:16:32,691 - INFO - 目标音频时长: 5.45秒
2025-07-28 22:16:32,691 - INFO - 实际视频时长: 5.50秒
2025-07-28 22:16:32,691 - INFO - 时长差异: 0.06秒 (1.03%)
2025-07-28 22:16:32,691 - INFO - ==========================================
2025-07-28 22:16:32,691 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 22:16:32,691 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\3_1.mp4
2025-07-28 22:16:32,691 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpe4x7h2e0
2025-07-28 22:16:32,733 - INFO - 方案 #1 处理完成:
2025-07-28 22:16:32,733 - INFO -   - 音频时长: 5.45秒
2025-07-28 22:16:32,733 - INFO -   - 视频时长: 5.50秒
2025-07-28 22:16:32,733 - INFO -   - 时长差异: 0.06秒 (1.03%)
2025-07-28 22:16:32,733 - INFO - 
----- 处理字幕 #3 的方案 #2 -----
2025-07-28 22:16:32,734 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\3_2.mp4
2025-07-28 22:16:32,734 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpr3blc25v
2025-07-28 22:16:32,734 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\43.mp4 (确认存在: True)
2025-07-28 22:16:32,734 - INFO - 添加场景ID=43，时长=1.32秒，累计时长=1.32秒
2025-07-28 22:16:32,735 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\42.mp4 (确认存在: True)
2025-07-28 22:16:32,735 - INFO - 添加场景ID=42，时长=1.08秒，累计时长=2.40秒
2025-07-28 22:16:32,735 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\41.mp4 (确认存在: True)
2025-07-28 22:16:32,735 - INFO - 添加场景ID=41，时长=1.08秒，累计时长=3.48秒
2025-07-28 22:16:32,735 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\36.mp4 (确认存在: True)
2025-07-28 22:16:32,735 - INFO - 添加场景ID=36，时长=1.72秒，累计时长=5.20秒
2025-07-28 22:16:32,735 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\39.mp4 (确认存在: True)
2025-07-28 22:16:32,735 - INFO - 添加场景ID=39，时长=2.08秒，累计时长=7.28秒
2025-07-28 22:16:32,735 - INFO - 准备合并 5 个场景文件，总时长约 7.28秒
2025-07-28 22:16:32,735 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/43.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/42.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/41.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/36.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/39.mp4'

2025-07-28 22:16:32,735 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpr3blc25v\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpr3blc25v\temp_combined.mp4
2025-07-28 22:16:32,914 - INFO - 合并后的视频时长: 7.40秒，目标音频时长: 5.45秒
2025-07-28 22:16:32,914 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpr3blc25v\temp_combined.mp4 -ss 0 -to 5.447 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\3_2.mp4
2025-07-28 22:16:33,298 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 22:16:33,298 - INFO - 目标音频时长: 5.45秒
2025-07-28 22:16:33,298 - INFO - 实际视频时长: 5.50秒
2025-07-28 22:16:33,298 - INFO - 时长差异: 0.06秒 (1.03%)
2025-07-28 22:16:33,298 - INFO - ==========================================
2025-07-28 22:16:33,298 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 22:16:33,298 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\3_2.mp4
2025-07-28 22:16:33,299 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpr3blc25v
2025-07-28 22:16:33,342 - INFO - 方案 #2 处理完成:
2025-07-28 22:16:33,342 - INFO -   - 音频时长: 5.45秒
2025-07-28 22:16:33,342 - INFO -   - 视频时长: 5.50秒
2025-07-28 22:16:33,342 - INFO -   - 时长差异: 0.06秒 (1.03%)
2025-07-28 22:16:33,342 - INFO - 
----- 处理字幕 #3 的方案 #3 -----
2025-07-28 22:16:33,342 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\3_3.mp4
2025-07-28 22:16:33,344 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpe4vaky6p
2025-07-28 22:16:33,344 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\38.mp4 (确认存在: True)
2025-07-28 22:16:33,344 - INFO - 添加场景ID=38，时长=1.08秒，累计时长=1.08秒
2025-07-28 22:16:33,344 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\37.mp4 (确认存在: True)
2025-07-28 22:16:33,344 - INFO - 添加场景ID=37，时长=3.20秒，累计时长=4.28秒
2025-07-28 22:16:33,344 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\44.mp4 (确认存在: True)
2025-07-28 22:16:33,344 - INFO - 添加场景ID=44，时长=4.48秒，累计时长=8.76秒
2025-07-28 22:16:33,344 - INFO - 场景总时长(8.76秒)已达到音频时长(5.45秒)的1.5倍，停止添加场景
2025-07-28 22:16:33,345 - INFO - 准备合并 3 个场景文件，总时长约 8.76秒
2025-07-28 22:16:33,345 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/38.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/37.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/44.mp4'

2025-07-28 22:16:33,345 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpe4vaky6p\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpe4vaky6p\temp_combined.mp4
2025-07-28 22:16:33,489 - INFO - 合并后的视频时长: 8.83秒，目标音频时长: 5.45秒
2025-07-28 22:16:33,489 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpe4vaky6p\temp_combined.mp4 -ss 0 -to 5.447 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\3_3.mp4
2025-07-28 22:16:33,852 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 22:16:33,852 - INFO - 目标音频时长: 5.45秒
2025-07-28 22:16:33,852 - INFO - 实际视频时长: 5.50秒
2025-07-28 22:16:33,852 - INFO - 时长差异: 0.06秒 (1.03%)
2025-07-28 22:16:33,852 - INFO - ==========================================
2025-07-28 22:16:33,852 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 22:16:33,852 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\3_3.mp4
2025-07-28 22:16:33,853 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpe4vaky6p
2025-07-28 22:16:33,896 - INFO - 方案 #3 处理完成:
2025-07-28 22:16:33,896 - INFO -   - 音频时长: 5.45秒
2025-07-28 22:16:33,896 - INFO -   - 视频时长: 5.50秒
2025-07-28 22:16:33,896 - INFO -   - 时长差异: 0.06秒 (1.03%)
2025-07-28 22:16:33,896 - INFO - 
字幕 #3 处理完成，成功生成 3/3 套方案
2025-07-28 22:16:33,896 - INFO - 生成的视频文件:
2025-07-28 22:16:33,896 - INFO -   1. F:/github/aicut_auto/newcut_ai\3_1.mp4
2025-07-28 22:16:33,896 - INFO -   2. F:/github/aicut_auto/newcut_ai\3_2.mp4
2025-07-28 22:16:33,896 - INFO -   3. F:/github/aicut_auto/newcut_ai\3_3.mp4
2025-07-28 22:16:33,896 - INFO - ========== 字幕 #3 处理结束 ==========

