2025-07-28 22:16:45,978 - INFO - ========== 字幕 #22 处理开始 ==========
2025-07-28 22:16:45,978 - INFO - 字幕内容: 她心中得意地想着，反正二哥这个大傻子会永远相信她，正好拿来当自己的替罪羊。
2025-07-28 22:16:45,978 - INFO - 字幕序号: [752, 757]
2025-07-28 22:16:45,978 - INFO - 音频文件详情:
2025-07-28 22:16:45,978 - INFO -   - 路径: output\22.wav
2025-07-28 22:16:45,978 - INFO -   - 时长: 4.25秒
2025-07-28 22:16:45,980 - INFO -   - 验证音频时长: 4.25秒
2025-07-28 22:16:45,980 - INFO - 字幕时间戳信息:
2025-07-28 22:16:45,989 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-28 22:16:45,989 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-28 22:16:45,989 - INFO -   - 根据生成的音频时长(4.25秒)已调整字幕时间戳
2025-07-28 22:16:45,989 - INFO - ========== 开始为字幕 #22 生成 6 套场景方案 ==========
2025-07-28 22:16:45,989 - INFO - 开始查找字幕序号 [752, 757] 对应的场景，共有 2710 个场景可选
2025-07-28 22:16:45,990 - INFO - 找到related_overlap场景: scene_id=773, 字幕#752
2025-07-28 22:16:45,990 - INFO - 找到related_overlap场景: scene_id=774, 字幕#752
2025-07-28 22:16:45,990 - INFO - 找到related_overlap场景: scene_id=777, 字幕#757
2025-07-28 22:16:45,990 - INFO - 字幕 #752 找到 2 个overlap场景, 0 个between场景
2025-07-28 22:16:45,990 - INFO - 字幕 #757 找到 1 个overlap场景, 0 个between场景
2025-07-28 22:16:45,990 - INFO - 共收集 3 个未使用的overlap场景和 0 个未使用的between场景
2025-07-28 22:16:45,990 - INFO - 开始生成方案 #1
2025-07-28 22:16:45,991 - INFO - 方案 #1: 为字幕#752选择初始化overlap场景id=773
2025-07-28 22:16:45,991 - INFO - 方案 #1: 为字幕#757选择初始化overlap场景id=777
2025-07-28 22:16:45,991 - INFO - 方案 #1: 初始选择后，当前总时长=4.60秒
2025-07-28 22:16:45,991 - INFO - 方案 #1: 额外between选择后，当前总时长=4.60秒
2025-07-28 22:16:45,991 - INFO - 方案 #1: 场景总时长(4.60秒)大于音频时长(4.25秒)，需要裁剪
2025-07-28 22:16:45,991 - INFO - 调整前总时长: 4.60秒, 目标时长: 4.25秒
2025-07-28 22:16:45,991 - INFO - 需要裁剪 0.35秒
2025-07-28 22:16:45,991 - INFO - 裁剪最长场景ID=777：从3.32秒裁剪至2.97秒
2025-07-28 22:16:45,991 - INFO - 调整后总时长: 4.25秒，与目标时长差异: 0.00秒
2025-07-28 22:16:45,991 - INFO - 方案 #1 调整/填充后最终总时长: 4.25秒
2025-07-28 22:16:45,991 - INFO - 方案 #1 添加到方案列表
2025-07-28 22:16:45,991 - INFO - 开始生成方案 #2
2025-07-28 22:16:45,991 - INFO - 方案 #2: 为字幕#752选择初始化overlap场景id=774
2025-07-28 22:16:45,991 - INFO - 方案 #2: 初始选择后，当前总时长=1.12秒
2025-07-28 22:16:45,991 - INFO - 方案 #2: 额外between选择后，当前总时长=1.12秒
2025-07-28 22:16:45,991 - INFO - 方案 #2: 场景总时长(1.12秒)小于音频时长(4.25秒)，需要延伸填充
2025-07-28 22:16:45,991 - INFO - 方案 #2: 最后一个场景ID: 774
2025-07-28 22:16:45,991 - INFO - 方案 #2: 找到最后一个场景在原始列表中的索引: 773
2025-07-28 22:16:45,991 - INFO - 方案 #2: 需要填充时长: 3.13秒
2025-07-28 22:16:45,991 - INFO - 方案 #2: 追加场景 scene_id=775 (裁剪至 3.13秒)
2025-07-28 22:16:45,991 - INFO - 方案 #2: 成功填充至目标时长
2025-07-28 22:16:45,991 - INFO - 方案 #2 调整/填充后最终总时长: 4.25秒
2025-07-28 22:16:45,991 - INFO - 方案 #2 添加到方案列表
2025-07-28 22:16:45,991 - INFO - 开始生成方案 #3
2025-07-28 22:16:45,991 - WARNING - 方案 #3: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:45,991 - INFO - 开始生成方案 #4
2025-07-28 22:16:45,991 - WARNING - 方案 #4: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:45,991 - INFO - 开始生成方案 #5
2025-07-28 22:16:45,991 - WARNING - 方案 #5: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:45,991 - INFO - 开始生成方案 #6
2025-07-28 22:16:45,991 - WARNING - 方案 #6: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:45,991 - INFO - ========== 字幕 #22 的 2 套有效场景方案生成完成 ==========
2025-07-28 22:16:45,991 - INFO - 
----- 处理字幕 #22 的方案 #1 -----
2025-07-28 22:16:45,991 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\22_1.mp4
2025-07-28 22:16:45,992 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpj6ymglwn
2025-07-28 22:16:45,992 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\773.mp4 (确认存在: True)
2025-07-28 22:16:45,992 - INFO - 添加场景ID=773，时长=1.28秒，累计时长=1.28秒
2025-07-28 22:16:45,992 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\777.mp4 (确认存在: True)
2025-07-28 22:16:45,992 - INFO - 添加场景ID=777，时长=3.32秒，累计时长=4.60秒
2025-07-28 22:16:45,992 - INFO - 准备合并 2 个场景文件，总时长约 4.60秒
2025-07-28 22:16:45,992 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/773.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/777.mp4'

2025-07-28 22:16:45,992 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpj6ymglwn\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpj6ymglwn\temp_combined.mp4
2025-07-28 22:16:46,122 - INFO - 合并后的视频时长: 4.65秒，目标音频时长: 4.25秒
2025-07-28 22:16:46,122 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpj6ymglwn\temp_combined.mp4 -ss 0 -to 4.249 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\22_1.mp4
2025-07-28 22:16:46,406 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 22:16:46,406 - INFO - 目标音频时长: 4.25秒
2025-07-28 22:16:46,406 - INFO - 实际视频时长: 4.30秒
2025-07-28 22:16:46,406 - INFO - 时长差异: 0.05秒 (1.27%)
2025-07-28 22:16:46,406 - INFO - ==========================================
2025-07-28 22:16:46,406 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 22:16:46,406 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\22_1.mp4
2025-07-28 22:16:46,407 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpj6ymglwn
2025-07-28 22:16:46,450 - INFO - 方案 #1 处理完成:
2025-07-28 22:16:46,450 - INFO -   - 音频时长: 4.25秒
2025-07-28 22:16:46,450 - INFO -   - 视频时长: 4.30秒
2025-07-28 22:16:46,450 - INFO -   - 时长差异: 0.05秒 (1.27%)
2025-07-28 22:16:46,450 - INFO - 
----- 处理字幕 #22 的方案 #2 -----
2025-07-28 22:16:46,450 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\22_2.mp4
2025-07-28 22:16:46,450 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpr92ecy1u
2025-07-28 22:16:46,451 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\774.mp4 (确认存在: True)
2025-07-28 22:16:46,451 - INFO - 添加场景ID=774，时长=1.12秒，累计时长=1.12秒
2025-07-28 22:16:46,451 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\775.mp4 (确认存在: True)
2025-07-28 22:16:46,451 - INFO - 添加场景ID=775，时长=3.20秒，累计时长=4.32秒
2025-07-28 22:16:46,451 - INFO - 准备合并 2 个场景文件，总时长约 4.32秒
2025-07-28 22:16:46,451 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/774.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/775.mp4'

2025-07-28 22:16:46,451 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpr92ecy1u\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpr92ecy1u\temp_combined.mp4
2025-07-28 22:16:46,588 - INFO - 合并后的视频时长: 4.37秒，目标音频时长: 4.25秒
2025-07-28 22:16:46,588 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpr92ecy1u\temp_combined.mp4 -ss 0 -to 4.249 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\22_2.mp4
2025-07-28 22:16:46,870 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 22:16:46,870 - INFO - 目标音频时长: 4.25秒
2025-07-28 22:16:46,870 - INFO - 实际视频时长: 4.30秒
2025-07-28 22:16:46,870 - INFO - 时长差异: 0.05秒 (1.27%)
2025-07-28 22:16:46,870 - INFO - ==========================================
2025-07-28 22:16:46,870 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 22:16:46,870 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\22_2.mp4
2025-07-28 22:16:46,871 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpr92ecy1u
2025-07-28 22:16:46,916 - INFO - 方案 #2 处理完成:
2025-07-28 22:16:46,916 - INFO -   - 音频时长: 4.25秒
2025-07-28 22:16:46,916 - INFO -   - 视频时长: 4.30秒
2025-07-28 22:16:46,916 - INFO -   - 时长差异: 0.05秒 (1.27%)
2025-07-28 22:16:46,916 - INFO - 
字幕 #22 处理完成，成功生成 2/2 套方案
2025-07-28 22:16:46,916 - INFO - 生成的视频文件:
2025-07-28 22:16:46,916 - INFO -   1. F:/github/aicut_auto/newcut_ai\22_1.mp4
2025-07-28 22:16:46,916 - INFO -   2. F:/github/aicut_auto/newcut_ai\22_2.mp4
2025-07-28 22:16:46,916 - INFO - ========== 字幕 #22 处理结束 ==========

