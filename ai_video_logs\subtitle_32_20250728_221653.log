2025-07-28 22:16:53,496 - INFO - ========== 字幕 #32 处理开始 ==========
2025-07-28 22:16:53,496 - INFO - 字幕内容: 更令人震惊的是，大家发现心机女早已伪造文书，企图私自转移侯府全部家产！
2025-07-28 22:16:53,496 - INFO - 字幕序号: [1315, 1317]
2025-07-28 22:16:53,496 - INFO - 音频文件详情:
2025-07-28 22:16:53,496 - INFO -   - 路径: output\32.wav
2025-07-28 22:16:53,496 - INFO -   - 时长: 7.04秒
2025-07-28 22:16:53,497 - INFO -   - 验证音频时长: 7.04秒
2025-07-28 22:16:53,497 - INFO - 字幕时间戳信息:
2025-07-28 22:16:53,497 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-28 22:16:53,497 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-28 22:16:53,497 - INFO -   - 根据生成的音频时长(7.04秒)已调整字幕时间戳
2025-07-28 22:16:53,497 - INFO - ========== 开始为字幕 #32 生成 6 套场景方案 ==========
2025-07-28 22:16:53,497 - INFO - 开始查找字幕序号 [1315, 1317] 对应的场景，共有 2710 个场景可选
2025-07-28 22:16:53,497 - INFO - 找到related_overlap场景: scene_id=1281, 字幕#1315
2025-07-28 22:16:53,497 - INFO - 找到related_overlap场景: scene_id=1282, 字幕#1315
2025-07-28 22:16:53,497 - INFO - 找到related_overlap场景: scene_id=1283, 字幕#1317
2025-07-28 22:16:53,498 - INFO - 字幕 #1315 找到 2 个overlap场景, 0 个between场景
2025-07-28 22:16:53,498 - INFO - 字幕 #1317 找到 1 个overlap场景, 0 个between场景
2025-07-28 22:16:53,498 - INFO - 共收集 3 个未使用的overlap场景和 0 个未使用的between场景
2025-07-28 22:16:53,498 - INFO - 开始生成方案 #1
2025-07-28 22:16:53,498 - INFO - 方案 #1: 为字幕#1315选择初始化overlap场景id=1282
2025-07-28 22:16:53,498 - INFO - 方案 #1: 为字幕#1317选择初始化overlap场景id=1283
2025-07-28 22:16:53,498 - INFO - 方案 #1: 初始选择后，当前总时长=8.48秒
2025-07-28 22:16:53,498 - INFO - 方案 #1: 额外between选择后，当前总时长=8.48秒
2025-07-28 22:16:53,498 - INFO - 方案 #1: 场景总时长(8.48秒)大于音频时长(7.04秒)，需要裁剪
2025-07-28 22:16:53,498 - INFO - 调整前总时长: 8.48秒, 目标时长: 7.04秒
2025-07-28 22:16:53,498 - INFO - 需要裁剪 1.44秒
2025-07-28 22:16:53,498 - INFO - 裁剪最长场景ID=1283：从5.84秒裁剪至4.40秒
2025-07-28 22:16:53,498 - INFO - 调整后总时长: 7.04秒，与目标时长差异: 0.00秒
2025-07-28 22:16:53,498 - INFO - 方案 #1 调整/填充后最终总时长: 7.04秒
2025-07-28 22:16:53,498 - INFO - 方案 #1 添加到方案列表
2025-07-28 22:16:53,498 - INFO - 开始生成方案 #2
2025-07-28 22:16:53,498 - INFO - 方案 #2: 为字幕#1315选择初始化overlap场景id=1281
2025-07-28 22:16:53,498 - INFO - 方案 #2: 初始选择后，当前总时长=3.68秒
2025-07-28 22:16:53,498 - INFO - 方案 #2: 额外between选择后，当前总时长=3.68秒
2025-07-28 22:16:53,498 - INFO - 方案 #2: 场景总时长(3.68秒)小于音频时长(7.04秒)，需要延伸填充
2025-07-28 22:16:53,499 - INFO - 方案 #2: 最后一个场景ID: 1281
2025-07-28 22:16:53,499 - INFO - 方案 #2: 找到最后一个场景在原始列表中的索引: 1280
2025-07-28 22:16:53,499 - INFO - 方案 #2: 需要填充时长: 3.36秒
2025-07-28 22:16:53,499 - INFO - 方案 #2: 跳过已使用的场景: scene_id=1282
2025-07-28 22:16:53,499 - INFO - 方案 #2: 跳过已使用的场景: scene_id=1283
2025-07-28 22:16:53,499 - INFO - 方案 #2: 追加场景 scene_id=1284 (裁剪至 3.36秒)
2025-07-28 22:16:53,499 - INFO - 方案 #2: 成功填充至目标时长
2025-07-28 22:16:53,499 - INFO - 方案 #2 调整/填充后最终总时长: 7.04秒
2025-07-28 22:16:53,499 - INFO - 方案 #2 添加到方案列表
2025-07-28 22:16:53,499 - INFO - 开始生成方案 #3
2025-07-28 22:16:53,499 - WARNING - 方案 #3: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:53,499 - INFO - 开始生成方案 #4
2025-07-28 22:16:53,499 - WARNING - 方案 #4: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:53,499 - INFO - 开始生成方案 #5
2025-07-28 22:16:53,499 - WARNING - 方案 #5: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:53,499 - INFO - 开始生成方案 #6
2025-07-28 22:16:53,499 - WARNING - 方案 #6: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:53,499 - INFO - ========== 字幕 #32 的 2 套有效场景方案生成完成 ==========
2025-07-28 22:16:53,499 - INFO - 
----- 处理字幕 #32 的方案 #1 -----
2025-07-28 22:16:53,499 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\32_1.mp4
2025-07-28 22:16:53,499 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp6yjzo8b4
2025-07-28 22:16:53,500 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1282.mp4 (确认存在: True)
2025-07-28 22:16:53,500 - INFO - 添加场景ID=1282，时长=2.64秒，累计时长=2.64秒
2025-07-28 22:16:53,500 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1283.mp4 (确认存在: True)
2025-07-28 22:16:53,500 - INFO - 添加场景ID=1283，时长=5.84秒，累计时长=8.48秒
2025-07-28 22:16:53,500 - INFO - 准备合并 2 个场景文件，总时长约 8.48秒
2025-07-28 22:16:53,500 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1282.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1283.mp4'

2025-07-28 22:16:53,500 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp6yjzo8b4\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp6yjzo8b4\temp_combined.mp4
2025-07-28 22:16:53,649 - INFO - 合并后的视频时长: 8.53秒，目标音频时长: 7.04秒
2025-07-28 22:16:53,649 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp6yjzo8b4\temp_combined.mp4 -ss 0 -to 7.04 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\32_1.mp4
2025-07-28 22:16:54,034 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 22:16:54,034 - INFO - 目标音频时长: 7.04秒
2025-07-28 22:16:54,034 - INFO - 实际视频时长: 7.06秒
2025-07-28 22:16:54,034 - INFO - 时长差异: 0.02秒 (0.33%)
2025-07-28 22:16:54,034 - INFO - ==========================================
2025-07-28 22:16:54,034 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 22:16:54,034 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\32_1.mp4
2025-07-28 22:16:54,036 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp6yjzo8b4
2025-07-28 22:16:54,082 - INFO - 方案 #1 处理完成:
2025-07-28 22:16:54,082 - INFO -   - 音频时长: 7.04秒
2025-07-28 22:16:54,082 - INFO -   - 视频时长: 7.06秒
2025-07-28 22:16:54,082 - INFO -   - 时长差异: 0.02秒 (0.33%)
2025-07-28 22:16:54,082 - INFO - 
----- 处理字幕 #32 的方案 #2 -----
2025-07-28 22:16:54,082 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\32_2.mp4
2025-07-28 22:16:54,082 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpnc3gpmxl
2025-07-28 22:16:54,083 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1281.mp4 (确认存在: True)
2025-07-28 22:16:54,083 - INFO - 添加场景ID=1281，时长=3.68秒，累计时长=3.68秒
2025-07-28 22:16:54,083 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1284.mp4 (确认存在: True)
2025-07-28 22:16:54,083 - INFO - 添加场景ID=1284，时长=3.64秒，累计时长=7.32秒
2025-07-28 22:16:54,083 - INFO - 准备合并 2 个场景文件，总时长约 7.32秒
2025-07-28 22:16:54,083 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1281.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1284.mp4'

2025-07-28 22:16:54,083 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpnc3gpmxl\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpnc3gpmxl\temp_combined.mp4
2025-07-28 22:16:54,234 - INFO - 合并后的视频时长: 7.37秒，目标音频时长: 7.04秒
2025-07-28 22:16:54,234 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpnc3gpmxl\temp_combined.mp4 -ss 0 -to 7.04 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\32_2.mp4
2025-07-28 22:16:54,612 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 22:16:54,612 - INFO - 目标音频时长: 7.04秒
2025-07-28 22:16:54,612 - INFO - 实际视频时长: 7.06秒
2025-07-28 22:16:54,612 - INFO - 时长差异: 0.02秒 (0.33%)
2025-07-28 22:16:54,612 - INFO - ==========================================
2025-07-28 22:16:54,612 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 22:16:54,612 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\32_2.mp4
2025-07-28 22:16:54,613 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpnc3gpmxl
2025-07-28 22:16:54,658 - INFO - 方案 #2 处理完成:
2025-07-28 22:16:54,658 - INFO -   - 音频时长: 7.04秒
2025-07-28 22:16:54,658 - INFO -   - 视频时长: 7.06秒
2025-07-28 22:16:54,658 - INFO -   - 时长差异: 0.02秒 (0.33%)
2025-07-28 22:16:54,658 - INFO - 
字幕 #32 处理完成，成功生成 2/2 套方案
2025-07-28 22:16:54,658 - INFO - 生成的视频文件:
2025-07-28 22:16:54,658 - INFO -   1. F:/github/aicut_auto/newcut_ai\32_1.mp4
2025-07-28 22:16:54,658 - INFO -   2. F:/github/aicut_auto/newcut_ai\32_2.mp4
2025-07-28 22:16:54,658 - INFO - ========== 字幕 #32 处理结束 ==========

