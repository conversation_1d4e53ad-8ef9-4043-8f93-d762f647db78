2025-07-28 22:17:08,767 - INFO - ========== 字幕 #53 处理开始 ==========
2025-07-28 22:17:08,767 - INFO - 字幕内容: 宫中恰逢为生了病的二皇子选妃冲喜，据说那二皇子还成了半个废人。
2025-07-28 22:17:08,767 - INFO - 字幕序号: [2583, 2595]
2025-07-28 22:17:08,767 - INFO - 音频文件详情:
2025-07-28 22:17:08,767 - INFO -   - 路径: output\53.wav
2025-07-28 22:17:08,767 - INFO -   - 时长: 6.01秒
2025-07-28 22:17:08,767 - INFO -   - 验证音频时长: 6.01秒
2025-07-28 22:17:08,767 - INFO - 字幕时间戳信息:
2025-07-28 22:17:08,767 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-28 22:17:08,767 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-28 22:17:08,767 - INFO -   - 根据生成的音频时长(6.01秒)已调整字幕时间戳
2025-07-28 22:17:08,767 - INFO - ========== 开始为字幕 #53 生成 6 套场景方案 ==========
2025-07-28 22:17:08,767 - INFO - 开始查找字幕序号 [2583, 2595] 对应的场景，共有 2710 个场景可选
2025-07-28 22:17:08,768 - INFO - 找到related_overlap场景: scene_id=2463, 字幕#2583
2025-07-28 22:17:08,768 - INFO - 找到related_overlap场景: scene_id=2473, 字幕#2595
2025-07-28 22:17:08,768 - INFO - 找到related_overlap场景: scene_id=2474, 字幕#2595
2025-07-28 22:17:08,769 - INFO - 字幕 #2583 找到 1 个overlap场景, 0 个between场景
2025-07-28 22:17:08,769 - INFO - 字幕 #2595 找到 2 个overlap场景, 0 个between场景
2025-07-28 22:17:08,769 - INFO - 共收集 3 个未使用的overlap场景和 0 个未使用的between场景
2025-07-28 22:17:08,769 - INFO - 开始生成方案 #1
2025-07-28 22:17:08,769 - INFO - 方案 #1: 为字幕#2583选择初始化overlap场景id=2463
2025-07-28 22:17:08,769 - INFO - 方案 #1: 为字幕#2595选择初始化overlap场景id=2474
2025-07-28 22:17:08,769 - INFO - 方案 #1: 初始选择后，当前总时长=5.64秒
2025-07-28 22:17:08,769 - INFO - 方案 #1: 额外添加overlap场景id=2473, 当前总时长=7.92秒
2025-07-28 22:17:08,769 - INFO - 方案 #1: 额外between选择后，当前总时长=7.92秒
2025-07-28 22:17:08,769 - INFO - 方案 #1: 场景总时长(7.92秒)大于音频时长(6.01秒)，需要裁剪
2025-07-28 22:17:08,769 - INFO - 调整前总时长: 7.92秒, 目标时长: 6.01秒
2025-07-28 22:17:08,769 - INFO - 需要裁剪 1.91秒
2025-07-28 22:17:08,769 - INFO - 裁剪最长场景ID=2463：从3.00秒裁剪至1.09秒
2025-07-28 22:17:08,769 - INFO - 调整后总时长: 6.01秒，与目标时长差异: 0.00秒
2025-07-28 22:17:08,769 - INFO - 方案 #1 调整/填充后最终总时长: 6.01秒
2025-07-28 22:17:08,769 - INFO - 方案 #1 添加到方案列表
2025-07-28 22:17:08,769 - INFO - 开始生成方案 #2
2025-07-28 22:17:08,769 - WARNING - 方案 #2: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:17:08,769 - INFO - 开始生成方案 #3
2025-07-28 22:17:08,769 - WARNING - 方案 #3: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:17:08,769 - INFO - 开始生成方案 #4
2025-07-28 22:17:08,769 - WARNING - 方案 #4: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:17:08,769 - INFO - 开始生成方案 #5
2025-07-28 22:17:08,769 - WARNING - 方案 #5: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:17:08,769 - INFO - 开始生成方案 #6
2025-07-28 22:17:08,769 - WARNING - 方案 #6: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:17:08,769 - INFO - ========== 字幕 #53 的 1 套有效场景方案生成完成 ==========
2025-07-28 22:17:08,769 - INFO - 
----- 处理字幕 #53 的方案 #1 -----
2025-07-28 22:17:08,769 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\53_1.mp4
2025-07-28 22:17:08,769 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp0mx6b4dr
2025-07-28 22:17:08,770 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2463.mp4 (确认存在: True)
2025-07-28 22:17:08,770 - INFO - 添加场景ID=2463，时长=3.00秒，累计时长=3.00秒
2025-07-28 22:17:08,770 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2474.mp4 (确认存在: True)
2025-07-28 22:17:08,770 - INFO - 添加场景ID=2474，时长=2.64秒，累计时长=5.64秒
2025-07-28 22:17:08,770 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2473.mp4 (确认存在: True)
2025-07-28 22:17:08,770 - INFO - 添加场景ID=2473，时长=2.28秒，累计时长=7.92秒
2025-07-28 22:17:08,770 - INFO - 准备合并 3 个场景文件，总时长约 7.92秒
2025-07-28 22:17:08,770 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2463.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2474.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2473.mp4'

2025-07-28 22:17:08,770 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp0mx6b4dr\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp0mx6b4dr\temp_combined.mp4
2025-07-28 22:17:08,940 - INFO - 合并后的视频时长: 7.99秒，目标音频时长: 6.01秒
2025-07-28 22:17:08,940 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp0mx6b4dr\temp_combined.mp4 -ss 0 -to 6.01 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\53_1.mp4
2025-07-28 22:17:09,288 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 22:17:09,288 - INFO - 目标音频时长: 6.01秒
2025-07-28 22:17:09,288 - INFO - 实际视频时长: 6.06秒
2025-07-28 22:17:09,288 - INFO - 时长差异: 0.05秒 (0.88%)
2025-07-28 22:17:09,288 - INFO - ==========================================
2025-07-28 22:17:09,288 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 22:17:09,288 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\53_1.mp4
2025-07-28 22:17:09,288 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp0mx6b4dr
2025-07-28 22:17:09,338 - INFO - 方案 #1 处理完成:
2025-07-28 22:17:09,338 - INFO -   - 音频时长: 6.01秒
2025-07-28 22:17:09,338 - INFO -   - 视频时长: 6.06秒
2025-07-28 22:17:09,338 - INFO -   - 时长差异: 0.05秒 (0.88%)
2025-07-28 22:17:09,338 - INFO - 
字幕 #53 处理完成，成功生成 1/1 套方案
2025-07-28 22:17:09,338 - INFO - 生成的视频文件:
2025-07-28 22:17:09,338 - INFO -   1. F:/github/aicut_auto/newcut_ai\53_1.mp4
2025-07-28 22:17:09,338 - INFO - ========== 字幕 #53 处理结束 ==========

