2025-07-28 22:17:06,343 - INFO - ========== 字幕 #49 处理开始 ==========
2025-07-28 22:17:06,343 - INFO - 字幕内容: 一计不成，心机女竟联合外人，拿着侯府印鉴的假婚书上门，强行要将真千金嫁给一个残废。
2025-07-28 22:17:06,343 - INFO - 字幕序号: [2352, 2354]
2025-07-28 22:17:06,343 - INFO - 音频文件详情:
2025-07-28 22:17:06,343 - INFO -   - 路径: output\49.wav
2025-07-28 22:17:06,343 - INFO -   - 时长: 8.39秒
2025-07-28 22:17:06,343 - INFO -   - 验证音频时长: 8.39秒
2025-07-28 22:17:06,343 - INFO - 字幕时间戳信息:
2025-07-28 22:17:06,343 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-28 22:17:06,344 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-28 22:17:06,344 - INFO -   - 根据生成的音频时长(8.39秒)已调整字幕时间戳
2025-07-28 22:17:06,344 - INFO - ========== 开始为字幕 #49 生成 6 套场景方案 ==========
2025-07-28 22:17:06,344 - INFO - 开始查找字幕序号 [2352, 2354] 对应的场景，共有 2710 个场景可选
2025-07-28 22:17:06,344 - INFO - 找到related_overlap场景: scene_id=2208, 字幕#2352
2025-07-28 22:17:06,345 - INFO - 找到related_between场景: scene_id=2209, 字幕#2354
2025-07-28 22:17:06,345 - INFO - 字幕 #2352 找到 1 个overlap场景, 0 个between场景
2025-07-28 22:17:06,345 - INFO - 字幕 #2354 找到 0 个overlap场景, 1 个between场景
2025-07-28 22:17:06,345 - INFO - 共收集 1 个未使用的overlap场景和 1 个未使用的between场景
2025-07-28 22:17:06,345 - INFO - 开始生成方案 #1
2025-07-28 22:17:06,345 - INFO - 方案 #1: 为字幕#2352选择初始化overlap场景id=2208
2025-07-28 22:17:06,345 - INFO - 方案 #1: 初始选择后，当前总时长=2.92秒
2025-07-28 22:17:06,345 - INFO - 方案 #1: 为字幕#2354选择初始化between场景id=2209
2025-07-28 22:17:06,345 - INFO - 方案 #1: 额外between选择后，当前总时长=3.76秒
2025-07-28 22:17:06,345 - INFO - 方案 #1: 场景总时长(3.76秒)小于音频时长(8.39秒)，需要延伸填充
2025-07-28 22:17:06,345 - INFO - 方案 #1: 最后一个场景ID: 2209
2025-07-28 22:17:06,345 - INFO - 方案 #1: 找到最后一个场景在原始列表中的索引: 2208
2025-07-28 22:17:06,345 - INFO - 方案 #1: 需要填充时长: 4.63秒
2025-07-28 22:17:06,345 - INFO - 方案 #1: 追加场景 scene_id=2210 (完整时长 1.24秒)
2025-07-28 22:17:06,345 - INFO - 方案 #1: 追加场景 scene_id=2211 (完整时长 1.24秒)
2025-07-28 22:17:06,345 - INFO - 方案 #1: 追加场景 scene_id=2212 (完整时长 1.12秒)
2025-07-28 22:17:06,345 - INFO - 方案 #1: 追加场景 scene_id=2213 (裁剪至 1.03秒)
2025-07-28 22:17:06,345 - INFO - 方案 #1: 成功填充至目标时长
2025-07-28 22:17:06,345 - INFO - 方案 #1 调整/填充后最终总时长: 8.39秒
2025-07-28 22:17:06,345 - INFO - 方案 #1 添加到方案列表
2025-07-28 22:17:06,345 - INFO - 开始生成方案 #2
2025-07-28 22:17:06,345 - WARNING - 方案 #2: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:17:06,345 - INFO - 开始生成方案 #3
2025-07-28 22:17:06,345 - WARNING - 方案 #3: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:17:06,345 - INFO - 开始生成方案 #4
2025-07-28 22:17:06,345 - WARNING - 方案 #4: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:17:06,345 - INFO - 开始生成方案 #5
2025-07-28 22:17:06,345 - WARNING - 方案 #5: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:17:06,345 - INFO - 开始生成方案 #6
2025-07-28 22:17:06,345 - WARNING - 方案 #6: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:17:06,345 - INFO - ========== 字幕 #49 的 1 套有效场景方案生成完成 ==========
2025-07-28 22:17:06,345 - INFO - 
----- 处理字幕 #49 的方案 #1 -----
2025-07-28 22:17:06,346 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\49_1.mp4
2025-07-28 22:17:06,346 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpkb7dj9rk
2025-07-28 22:17:06,346 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2208.mp4 (确认存在: True)
2025-07-28 22:17:06,346 - INFO - 添加场景ID=2208，时长=2.92秒，累计时长=2.92秒
2025-07-28 22:17:06,346 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2209.mp4 (确认存在: True)
2025-07-28 22:17:06,346 - INFO - 添加场景ID=2209，时长=0.84秒，累计时长=3.76秒
2025-07-28 22:17:06,346 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2210.mp4 (确认存在: True)
2025-07-28 22:17:06,346 - INFO - 添加场景ID=2210，时长=1.24秒，累计时长=5.00秒
2025-07-28 22:17:06,347 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2211.mp4 (确认存在: True)
2025-07-28 22:17:06,347 - INFO - 添加场景ID=2211，时长=1.24秒，累计时长=6.24秒
2025-07-28 22:17:06,347 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2212.mp4 (确认存在: True)
2025-07-28 22:17:06,347 - INFO - 添加场景ID=2212，时长=1.12秒，累计时长=7.36秒
2025-07-28 22:17:06,347 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2213.mp4 (确认存在: True)
2025-07-28 22:17:06,347 - INFO - 添加场景ID=2213，时长=1.12秒，累计时长=8.48秒
2025-07-28 22:17:06,347 - INFO - 准备合并 6 个场景文件，总时长约 8.48秒
2025-07-28 22:17:06,347 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2208.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2209.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2210.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2211.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2212.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2213.mp4'

2025-07-28 22:17:06,347 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpkb7dj9rk\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpkb7dj9rk\temp_combined.mp4
2025-07-28 22:17:06,544 - INFO - 合并后的视频时长: 8.62秒，目标音频时长: 8.39秒
2025-07-28 22:17:06,544 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpkb7dj9rk\temp_combined.mp4 -ss 0 -to 8.387 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\49_1.mp4
2025-07-28 22:17:06,974 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 22:17:06,975 - INFO - 目标音频时长: 8.39秒
2025-07-28 22:17:06,975 - INFO - 实际视频时长: 8.42秒
2025-07-28 22:17:06,975 - INFO - 时长差异: 0.04秒 (0.43%)
2025-07-28 22:17:06,975 - INFO - ==========================================
2025-07-28 22:17:06,975 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 22:17:06,975 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\49_1.mp4
2025-07-28 22:17:06,975 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpkb7dj9rk
2025-07-28 22:17:07,026 - INFO - 方案 #1 处理完成:
2025-07-28 22:17:07,026 - INFO -   - 音频时长: 8.39秒
2025-07-28 22:17:07,026 - INFO -   - 视频时长: 8.42秒
2025-07-28 22:17:07,026 - INFO -   - 时长差异: 0.04秒 (0.43%)
2025-07-28 22:17:07,026 - INFO - 
字幕 #49 处理完成，成功生成 1/1 套方案
2025-07-28 22:17:07,026 - INFO - 生成的视频文件:
2025-07-28 22:17:07,026 - INFO -   1. F:/github/aicut_auto/newcut_ai\49_1.mp4
2025-07-28 22:17:07,026 - INFO - ========== 字幕 #49 处理结束 ==========

