2025-07-28 22:16:59,098 - INFO - ========== 字幕 #39 处理开始 ==========
2025-07-28 22:16:59,098 - INFO - 字幕内容: 她心中暗自得意，以为这次陷害天衣无缝，真千金绝对拿不出证据。
2025-07-28 22:16:59,098 - INFO - 字幕序号: [1753, 1755]
2025-07-28 22:16:59,098 - INFO - 音频文件详情:
2025-07-28 22:16:59,098 - INFO -   - 路径: output\39.wav
2025-07-28 22:16:59,098 - INFO -   - 时长: 4.41秒
2025-07-28 22:16:59,098 - INFO -   - 验证音频时长: 4.41秒
2025-07-28 22:16:59,099 - INFO - 字幕时间戳信息:
2025-07-28 22:16:59,108 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-28 22:16:59,108 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-28 22:16:59,108 - INFO -   - 根据生成的音频时长(4.41秒)已调整字幕时间戳
2025-07-28 22:16:59,108 - INFO - ========== 开始为字幕 #39 生成 6 套场景方案 ==========
2025-07-28 22:16:59,108 - INFO - 开始查找字幕序号 [1753, 1755] 对应的场景，共有 2710 个场景可选
2025-07-28 22:16:59,108 - INFO - 找到related_overlap场景: scene_id=1642, 字幕#1753
2025-07-28 22:16:59,108 - INFO - 找到related_overlap场景: scene_id=1643, 字幕#1753
2025-07-28 22:16:59,108 - INFO - 找到related_overlap场景: scene_id=1644, 字幕#1755
2025-07-28 22:16:59,109 - INFO - 字幕 #1753 找到 2 个overlap场景, 0 个between场景
2025-07-28 22:16:59,109 - INFO - 字幕 #1755 找到 1 个overlap场景, 0 个between场景
2025-07-28 22:16:59,109 - INFO - 共收集 3 个未使用的overlap场景和 0 个未使用的between场景
2025-07-28 22:16:59,109 - INFO - 开始生成方案 #1
2025-07-28 22:16:59,109 - INFO - 方案 #1: 为字幕#1753选择初始化overlap场景id=1642
2025-07-28 22:16:59,109 - INFO - 方案 #1: 为字幕#1755选择初始化overlap场景id=1644
2025-07-28 22:16:59,109 - INFO - 方案 #1: 初始选择后，当前总时长=3.64秒
2025-07-28 22:16:59,109 - INFO - 方案 #1: 额外添加overlap场景id=1643, 当前总时长=4.92秒
2025-07-28 22:16:59,109 - INFO - 方案 #1: 额外between选择后，当前总时长=4.92秒
2025-07-28 22:16:59,109 - INFO - 方案 #1: 场景总时长(4.92秒)大于音频时长(4.41秒)，需要裁剪
2025-07-28 22:16:59,109 - INFO - 调整前总时长: 4.92秒, 目标时长: 4.41秒
2025-07-28 22:16:59,109 - INFO - 需要裁剪 0.51秒
2025-07-28 22:16:59,109 - INFO - 裁剪最长场景ID=1644：从2.40秒裁剪至1.89秒
2025-07-28 22:16:59,109 - INFO - 调整后总时长: 4.41秒，与目标时长差异: 0.00秒
2025-07-28 22:16:59,109 - INFO - 方案 #1 调整/填充后最终总时长: 4.41秒
2025-07-28 22:16:59,109 - INFO - 方案 #1 添加到方案列表
2025-07-28 22:16:59,109 - INFO - 开始生成方案 #2
2025-07-28 22:16:59,109 - WARNING - 方案 #2: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:59,109 - INFO - 开始生成方案 #3
2025-07-28 22:16:59,109 - WARNING - 方案 #3: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:59,109 - INFO - 开始生成方案 #4
2025-07-28 22:16:59,109 - WARNING - 方案 #4: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:59,109 - INFO - 开始生成方案 #5
2025-07-28 22:16:59,109 - WARNING - 方案 #5: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:59,109 - INFO - 开始生成方案 #6
2025-07-28 22:16:59,109 - WARNING - 方案 #6: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:59,109 - INFO - ========== 字幕 #39 的 1 套有效场景方案生成完成 ==========
2025-07-28 22:16:59,110 - INFO - 
----- 处理字幕 #39 的方案 #1 -----
2025-07-28 22:16:59,110 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\39_1.mp4
2025-07-28 22:16:59,110 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpqffnaolg
2025-07-28 22:16:59,110 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1642.mp4 (确认存在: True)
2025-07-28 22:16:59,110 - INFO - 添加场景ID=1642，时长=1.24秒，累计时长=1.24秒
2025-07-28 22:16:59,111 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1644.mp4 (确认存在: True)
2025-07-28 22:16:59,111 - INFO - 添加场景ID=1644，时长=2.40秒，累计时长=3.64秒
2025-07-28 22:16:59,111 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1643.mp4 (确认存在: True)
2025-07-28 22:16:59,111 - INFO - 添加场景ID=1643，时长=1.28秒，累计时长=4.92秒
2025-07-28 22:16:59,111 - INFO - 准备合并 3 个场景文件，总时长约 4.92秒
2025-07-28 22:16:59,111 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1642.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1644.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1643.mp4'

2025-07-28 22:16:59,111 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpqffnaolg\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpqffnaolg\temp_combined.mp4
2025-07-28 22:16:59,245 - INFO - 合并后的视频时长: 4.99秒，目标音频时长: 4.41秒
2025-07-28 22:16:59,246 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpqffnaolg\temp_combined.mp4 -ss 0 -to 4.407 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\39_1.mp4
2025-07-28 22:16:59,557 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 22:16:59,557 - INFO - 目标音频时长: 4.41秒
2025-07-28 22:16:59,557 - INFO - 实际视频时长: 4.46秒
2025-07-28 22:16:59,557 - INFO - 时长差异: 0.06秒 (1.27%)
2025-07-28 22:16:59,557 - INFO - ==========================================
2025-07-28 22:16:59,557 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 22:16:59,557 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\39_1.mp4
2025-07-28 22:16:59,558 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpqffnaolg
2025-07-28 22:16:59,611 - INFO - 方案 #1 处理完成:
2025-07-28 22:16:59,611 - INFO -   - 音频时长: 4.41秒
2025-07-28 22:16:59,611 - INFO -   - 视频时长: 4.46秒
2025-07-28 22:16:59,611 - INFO -   - 时长差异: 0.06秒 (1.27%)
2025-07-28 22:16:59,611 - INFO - 
字幕 #39 处理完成，成功生成 1/1 套方案
2025-07-28 22:16:59,611 - INFO - 生成的视频文件:
2025-07-28 22:16:59,611 - INFO -   1. F:/github/aicut_auto/newcut_ai\39_1.mp4
2025-07-28 22:16:59,611 - INFO - ========== 字幕 #39 处理结束 ==========

