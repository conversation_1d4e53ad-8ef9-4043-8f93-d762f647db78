2025-07-28 22:16:30,285 - INFO - ========== 字幕 #1 处理开始 ==========
2025-07-28 22:16:30,285 - INFO - 字幕内容: 堂堂修真老祖，竟意外穿进自己笔下的话本，成了那个被抱错的倒霉真千金。
2025-07-28 22:16:30,285 - INFO - 字幕序号: [52, 63]
2025-07-28 22:16:30,285 - INFO - 音频文件详情:
2025-07-28 22:16:30,285 - INFO -   - 路径: output\1.wav
2025-07-28 22:16:30,285 - INFO -   - 时长: 6.62秒
2025-07-28 22:16:30,286 - INFO -   - 验证音频时长: 6.62秒
2025-07-28 22:16:30,286 - INFO - 字幕时间戳信息:
2025-07-28 22:16:30,286 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-28 22:16:30,286 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-28 22:16:30,286 - INFO -   - 根据生成的音频时长(6.62秒)已调整字幕时间戳
2025-07-28 22:16:30,286 - INFO - ========== 开始为字幕 #1 生成 6 套场景方案 ==========
2025-07-28 22:16:30,286 - INFO - 开始查找字幕序号 [52, 63] 对应的场景，共有 2710 个场景可选
2025-07-28 22:16:30,286 - INFO - 找到related_overlap场景: scene_id=60, 字幕#52
2025-07-28 22:16:30,286 - INFO - 找到related_overlap场景: scene_id=61, 字幕#52
2025-07-28 22:16:30,286 - INFO - 找到related_overlap场景: scene_id=67, 字幕#63
2025-07-28 22:16:30,287 - INFO - 字幕 #52 找到 2 个overlap场景, 0 个between场景
2025-07-28 22:16:30,287 - INFO - 字幕 #63 找到 1 个overlap场景, 0 个between场景
2025-07-28 22:16:30,287 - INFO - 共收集 3 个未使用的overlap场景和 0 个未使用的between场景
2025-07-28 22:16:30,287 - INFO - 开始生成方案 #1
2025-07-28 22:16:30,287 - INFO - 方案 #1: 为字幕#52选择初始化overlap场景id=60
2025-07-28 22:16:30,287 - INFO - 方案 #1: 为字幕#63选择初始化overlap场景id=67
2025-07-28 22:16:30,287 - INFO - 方案 #1: 初始选择后，当前总时长=5.96秒
2025-07-28 22:16:30,287 - INFO - 方案 #1: 额外添加overlap场景id=61, 当前总时长=9.40秒
2025-07-28 22:16:30,287 - INFO - 方案 #1: 额外between选择后，当前总时长=9.40秒
2025-07-28 22:16:30,287 - INFO - 方案 #1: 场景总时长(9.40秒)大于音频时长(6.62秒)，需要裁剪
2025-07-28 22:16:30,287 - INFO - 调整前总时长: 9.40秒, 目标时长: 6.62秒
2025-07-28 22:16:30,287 - INFO - 需要裁剪 2.78秒
2025-07-28 22:16:30,287 - INFO - 裁剪单个场景会导致时长过短，尝试裁剪多个场景
2025-07-28 22:16:30,287 - INFO - 裁剪场景ID=67：从3.68秒裁剪至1.10秒
2025-07-28 22:16:30,287 - INFO - 裁剪场景ID=61：从3.44秒裁剪至3.24秒
2025-07-28 22:16:30,287 - INFO - 调整后总时长: 6.62秒，与目标时长差异: 0.00秒
2025-07-28 22:16:30,287 - INFO - 方案 #1 调整/填充后最终总时长: 6.62秒
2025-07-28 22:16:30,287 - INFO - 方案 #1 添加到方案列表
2025-07-28 22:16:30,287 - INFO - 开始生成方案 #2
2025-07-28 22:16:30,287 - WARNING - 方案 #2: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:30,287 - INFO - 开始生成方案 #3
2025-07-28 22:16:30,287 - WARNING - 方案 #3: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:30,287 - INFO - 开始生成方案 #4
2025-07-28 22:16:30,287 - WARNING - 方案 #4: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:30,287 - INFO - 开始生成方案 #5
2025-07-28 22:16:30,287 - WARNING - 方案 #5: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:30,287 - INFO - 开始生成方案 #6
2025-07-28 22:16:30,287 - WARNING - 方案 #6: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:30,287 - INFO - ========== 字幕 #1 的 1 套有效场景方案生成完成 ==========
2025-07-28 22:16:30,287 - INFO - 
----- 处理字幕 #1 的方案 #1 -----
2025-07-28 22:16:30,287 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\1_1.mp4
2025-07-28 22:16:30,288 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp2pd00ouq
2025-07-28 22:16:30,289 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\60.mp4 (确认存在: True)
2025-07-28 22:16:30,289 - INFO - 添加场景ID=60，时长=2.28秒，累计时长=2.28秒
2025-07-28 22:16:30,289 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\67.mp4 (确认存在: True)
2025-07-28 22:16:30,289 - INFO - 添加场景ID=67，时长=3.68秒，累计时长=5.96秒
2025-07-28 22:16:30,289 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\61.mp4 (确认存在: True)
2025-07-28 22:16:30,289 - INFO - 添加场景ID=61，时长=3.44秒，累计时长=9.40秒
2025-07-28 22:16:30,289 - INFO - 准备合并 3 个场景文件，总时长约 9.40秒
2025-07-28 22:16:30,289 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/60.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/67.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/61.mp4'

2025-07-28 22:16:30,289 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp2pd00ouq\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp2pd00ouq\temp_combined.mp4
2025-07-28 22:16:30,685 - INFO - 合并后的视频时长: 9.47秒，目标音频时长: 6.62秒
2025-07-28 22:16:30,685 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp2pd00ouq\temp_combined.mp4 -ss 0 -to 6.62 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\1_1.mp4
2025-07-28 22:16:31,061 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 22:16:31,061 - INFO - 目标音频时长: 6.62秒
2025-07-28 22:16:31,061 - INFO - 实际视频时长: 6.66秒
2025-07-28 22:16:31,061 - INFO - 时长差异: 0.04秒 (0.65%)
2025-07-28 22:16:31,061 - INFO - ==========================================
2025-07-28 22:16:31,061 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 22:16:31,061 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\1_1.mp4
2025-07-28 22:16:31,062 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp2pd00ouq
2025-07-28 22:16:31,106 - INFO - 方案 #1 处理完成:
2025-07-28 22:16:31,106 - INFO -   - 音频时长: 6.62秒
2025-07-28 22:16:31,106 - INFO -   - 视频时长: 6.66秒
2025-07-28 22:16:31,106 - INFO -   - 时长差异: 0.04秒 (0.65%)
2025-07-28 22:16:31,106 - INFO - 
字幕 #1 处理完成，成功生成 1/1 套方案
2025-07-28 22:16:31,106 - INFO - 生成的视频文件:
2025-07-28 22:16:31,106 - INFO -   1. F:/github/aicut_auto/newcut_ai\1_1.mp4
2025-07-28 22:16:31,106 - INFO - ========== 字幕 #1 处理结束 ==========

