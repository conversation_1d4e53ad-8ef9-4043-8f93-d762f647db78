2025-07-28 22:16:52,444 - INFO - ========== 字幕 #31 处理开始 ==========
2025-07-28 22:16:52,444 - INFO - 字幕内容: 她被罚禁足反省，想通道歉了才能出来。
2025-07-28 22:16:52,444 - INFO - 字幕序号: [1078, 1082]
2025-07-28 22:16:52,444 - INFO - 音频文件详情:
2025-07-28 22:16:52,444 - INFO -   - 路径: output\31.wav
2025-07-28 22:16:52,444 - INFO -   - 时长: 3.62秒
2025-07-28 22:16:52,444 - INFO -   - 验证音频时长: 3.62秒
2025-07-28 22:16:52,444 - INFO - 字幕时间戳信息:
2025-07-28 22:16:52,444 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-28 22:16:52,454 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-28 22:16:52,454 - INFO -   - 根据生成的音频时长(3.62秒)已调整字幕时间戳
2025-07-28 22:16:52,454 - INFO - ========== 开始为字幕 #31 生成 6 套场景方案 ==========
2025-07-28 22:16:52,454 - INFO - 开始查找字幕序号 [1078, 1082] 对应的场景，共有 2710 个场景可选
2025-07-28 22:16:52,454 - INFO - 找到related_overlap场景: scene_id=1097, 字幕#1078
2025-07-28 22:16:52,454 - INFO - 找到related_overlap场景: scene_id=1098, 字幕#1078
2025-07-28 22:16:52,454 - INFO - 找到related_overlap场景: scene_id=1099, 字幕#1082
2025-07-28 22:16:52,454 - INFO - 找到related_overlap场景: scene_id=1100, 字幕#1082
2025-07-28 22:16:52,455 - INFO - 字幕 #1078 找到 2 个overlap场景, 0 个between场景
2025-07-28 22:16:52,455 - INFO - 字幕 #1082 找到 2 个overlap场景, 0 个between场景
2025-07-28 22:16:52,455 - INFO - 共收集 4 个未使用的overlap场景和 0 个未使用的between场景
2025-07-28 22:16:52,455 - INFO - 开始生成方案 #1
2025-07-28 22:16:52,456 - INFO - 方案 #1: 为字幕#1078选择初始化overlap场景id=1098
2025-07-28 22:16:52,456 - INFO - 方案 #1: 为字幕#1082选择初始化overlap场景id=1099
2025-07-28 22:16:52,456 - INFO - 方案 #1: 初始选择后，当前总时长=4.64秒
2025-07-28 22:16:52,456 - INFO - 方案 #1: 额外between选择后，当前总时长=4.64秒
2025-07-28 22:16:52,456 - INFO - 方案 #1: 场景总时长(4.64秒)大于音频时长(3.62秒)，需要裁剪
2025-07-28 22:16:52,456 - INFO - 调整前总时长: 4.64秒, 目标时长: 3.62秒
2025-07-28 22:16:52,456 - INFO - 需要裁剪 1.02秒
2025-07-28 22:16:52,456 - INFO - 裁剪最长场景ID=1098：从2.48秒裁剪至1.46秒
2025-07-28 22:16:52,456 - INFO - 调整后总时长: 3.62秒，与目标时长差异: 0.00秒
2025-07-28 22:16:52,456 - INFO - 方案 #1 调整/填充后最终总时长: 3.62秒
2025-07-28 22:16:52,456 - INFO - 方案 #1 添加到方案列表
2025-07-28 22:16:52,456 - INFO - 开始生成方案 #2
2025-07-28 22:16:52,456 - INFO - 方案 #2: 为字幕#1078选择初始化overlap场景id=1097
2025-07-28 22:16:52,456 - INFO - 方案 #2: 为字幕#1082选择初始化overlap场景id=1100
2025-07-28 22:16:52,456 - INFO - 方案 #2: 初始选择后，当前总时长=3.44秒
2025-07-28 22:16:52,456 - INFO - 方案 #2: 额外between选择后，当前总时长=3.44秒
2025-07-28 22:16:52,456 - INFO - 方案 #2: 场景总时长(3.44秒)小于音频时长(3.62秒)，需要延伸填充
2025-07-28 22:16:52,456 - INFO - 方案 #2: 最后一个场景ID: 1100
2025-07-28 22:16:52,456 - INFO - 方案 #2: 找到最后一个场景在原始列表中的索引: 1099
2025-07-28 22:16:52,456 - INFO - 方案 #2: 需要填充时长: 0.18秒
2025-07-28 22:16:52,456 - INFO - 方案 #2: 追加场景 scene_id=1101 (裁剪至 0.18秒)
2025-07-28 22:16:52,456 - INFO - 方案 #2: 成功填充至目标时长
2025-07-28 22:16:52,456 - INFO - 方案 #2 调整/填充后最终总时长: 3.62秒
2025-07-28 22:16:52,456 - INFO - 方案 #2 添加到方案列表
2025-07-28 22:16:52,456 - INFO - 开始生成方案 #3
2025-07-28 22:16:52,456 - WARNING - 方案 #3: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:52,456 - INFO - 开始生成方案 #4
2025-07-28 22:16:52,456 - WARNING - 方案 #4: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:52,456 - INFO - 开始生成方案 #5
2025-07-28 22:16:52,456 - WARNING - 方案 #5: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:52,456 - INFO - 开始生成方案 #6
2025-07-28 22:16:52,456 - WARNING - 方案 #6: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:52,456 - INFO - ========== 字幕 #31 的 2 套有效场景方案生成完成 ==========
2025-07-28 22:16:52,456 - INFO - 
----- 处理字幕 #31 的方案 #1 -----
2025-07-28 22:16:52,456 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\31_1.mp4
2025-07-28 22:16:52,456 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpjlq5c25r
2025-07-28 22:16:52,457 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1098.mp4 (确认存在: True)
2025-07-28 22:16:52,457 - INFO - 添加场景ID=1098，时长=2.48秒，累计时长=2.48秒
2025-07-28 22:16:52,457 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1099.mp4 (确认存在: True)
2025-07-28 22:16:52,457 - INFO - 添加场景ID=1099，时长=2.16秒，累计时长=4.64秒
2025-07-28 22:16:52,457 - INFO - 准备合并 2 个场景文件，总时长约 4.64秒
2025-07-28 22:16:52,457 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1098.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1099.mp4'

2025-07-28 22:16:52,457 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpjlq5c25r\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpjlq5c25r\temp_combined.mp4
2025-07-28 22:16:52,591 - INFO - 合并后的视频时长: 4.69秒，目标音频时长: 3.62秒
2025-07-28 22:16:52,591 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpjlq5c25r\temp_combined.mp4 -ss 0 -to 3.615 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\31_1.mp4
2025-07-28 22:16:52,888 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 22:16:52,888 - INFO - 目标音频时长: 3.62秒
2025-07-28 22:16:52,889 - INFO - 实际视频时长: 3.66秒
2025-07-28 22:16:52,889 - INFO - 时长差异: 0.05秒 (1.33%)
2025-07-28 22:16:52,889 - INFO - ==========================================
2025-07-28 22:16:52,889 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 22:16:52,889 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\31_1.mp4
2025-07-28 22:16:52,889 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpjlq5c25r
2025-07-28 22:16:52,933 - INFO - 方案 #1 处理完成:
2025-07-28 22:16:52,933 - INFO -   - 音频时长: 3.62秒
2025-07-28 22:16:52,933 - INFO -   - 视频时长: 3.66秒
2025-07-28 22:16:52,933 - INFO -   - 时长差异: 0.05秒 (1.33%)
2025-07-28 22:16:52,933 - INFO - 
----- 处理字幕 #31 的方案 #2 -----
2025-07-28 22:16:52,933 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\31_2.mp4
2025-07-28 22:16:52,933 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp1lnbghg0
2025-07-28 22:16:52,934 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1097.mp4 (确认存在: True)
2025-07-28 22:16:52,934 - INFO - 添加场景ID=1097，时长=2.08秒，累计时长=2.08秒
2025-07-28 22:16:52,934 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1100.mp4 (确认存在: True)
2025-07-28 22:16:52,934 - INFO - 添加场景ID=1100，时长=1.36秒，累计时长=3.44秒
2025-07-28 22:16:52,934 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1101.mp4 (确认存在: True)
2025-07-28 22:16:52,934 - INFO - 添加场景ID=1101，时长=1.56秒，累计时长=5.00秒
2025-07-28 22:16:52,934 - INFO - 准备合并 3 个场景文件，总时长约 5.00秒
2025-07-28 22:16:52,934 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1097.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1100.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1101.mp4'

2025-07-28 22:16:52,934 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp1lnbghg0\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp1lnbghg0\temp_combined.mp4
2025-07-28 22:16:53,130 - INFO - 合并后的视频时长: 5.07秒，目标音频时长: 3.62秒
2025-07-28 22:16:53,130 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp1lnbghg0\temp_combined.mp4 -ss 0 -to 3.615 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\31_2.mp4
2025-07-28 22:16:53,439 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 22:16:53,439 - INFO - 目标音频时长: 3.62秒
2025-07-28 22:16:53,439 - INFO - 实际视频时长: 3.66秒
2025-07-28 22:16:53,439 - INFO - 时长差异: 0.05秒 (1.33%)
2025-07-28 22:16:53,439 - INFO - ==========================================
2025-07-28 22:16:53,439 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 22:16:53,439 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\31_2.mp4
2025-07-28 22:16:53,440 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp1lnbghg0
2025-07-28 22:16:53,496 - INFO - 方案 #2 处理完成:
2025-07-28 22:16:53,496 - INFO -   - 音频时长: 3.62秒
2025-07-28 22:16:53,496 - INFO -   - 视频时长: 3.66秒
2025-07-28 22:16:53,496 - INFO -   - 时长差异: 0.05秒 (1.33%)
2025-07-28 22:16:53,496 - INFO - 
字幕 #31 处理完成，成功生成 2/2 套方案
2025-07-28 22:16:53,496 - INFO - 生成的视频文件:
2025-07-28 22:16:53,496 - INFO -   1. F:/github/aicut_auto/newcut_ai\31_1.mp4
2025-07-28 22:16:53,496 - INFO -   2. F:/github/aicut_auto/newcut_ai\31_2.mp4
2025-07-28 22:16:53,496 - INFO - ========== 字幕 #31 处理结束 ==========

