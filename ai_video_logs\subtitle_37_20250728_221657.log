2025-07-28 22:16:57,964 - INFO - ========== 字幕 #37 处理开始 ==========
2025-07-28 22:16:57,964 - INFO - 字幕内容: 认亲宴上，心机女买通小人，当众污蔑真千金为过上好日子而对他投怀送抱。
2025-07-28 22:16:57,964 - INFO - 字幕序号: [1693, 1694]
2025-07-28 22:16:57,964 - INFO - 音频文件详情:
2025-07-28 22:16:57,964 - INFO -   - 路径: output\37.wav
2025-07-28 22:16:57,964 - INFO -   - 时长: 6.18秒
2025-07-28 22:16:57,965 - INFO -   - 验证音频时长: 6.18秒
2025-07-28 22:16:57,965 - INFO - 字幕时间戳信息:
2025-07-28 22:16:57,965 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-28 22:16:57,965 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-28 22:16:57,965 - INFO -   - 根据生成的音频时长(6.18秒)已调整字幕时间戳
2025-07-28 22:16:57,965 - INFO - ========== 开始为字幕 #37 生成 6 套场景方案 ==========
2025-07-28 22:16:57,965 - INFO - 开始查找字幕序号 [1693, 1694] 对应的场景，共有 2710 个场景可选
2025-07-28 22:16:57,965 - INFO - 找到related_overlap场景: scene_id=1590, 字幕#1693
2025-07-28 22:16:57,965 - INFO - 找到related_overlap场景: scene_id=1591, 字幕#1693
2025-07-28 22:16:57,966 - INFO - 字幕 #1693 找到 2 个overlap场景, 0 个between场景
2025-07-28 22:16:57,966 - INFO - 字幕 #1694 找到 0 个overlap场景, 0 个between场景
2025-07-28 22:16:57,966 - WARNING - 字幕 #1694 没有找到任何匹配场景!
2025-07-28 22:16:57,966 - INFO - 共收集 2 个未使用的overlap场景和 0 个未使用的between场景
2025-07-28 22:16:57,966 - INFO - 开始生成方案 #1
2025-07-28 22:16:57,966 - INFO - 方案 #1: 为字幕#1693选择初始化overlap场景id=1590
2025-07-28 22:16:57,966 - INFO - 方案 #1: 初始选择后，当前总时长=1.56秒
2025-07-28 22:16:57,966 - INFO - 方案 #1: 额外添加overlap场景id=1591, 当前总时长=3.36秒
2025-07-28 22:16:57,966 - INFO - 方案 #1: 额外between选择后，当前总时长=3.36秒
2025-07-28 22:16:57,966 - INFO - 方案 #1: 场景总时长(3.36秒)小于音频时长(6.18秒)，需要延伸填充
2025-07-28 22:16:57,966 - INFO - 方案 #1: 最后一个场景ID: 1591
2025-07-28 22:16:57,966 - INFO - 方案 #1: 找到最后一个场景在原始列表中的索引: 1590
2025-07-28 22:16:57,966 - INFO - 方案 #1: 需要填充时长: 2.83秒
2025-07-28 22:16:57,966 - INFO - 方案 #1: 追加场景 scene_id=1592 (完整时长 1.36秒)
2025-07-28 22:16:57,967 - INFO - 方案 #1: 追加场景 scene_id=1593 (裁剪至 1.47秒)
2025-07-28 22:16:57,967 - INFO - 方案 #1: 成功填充至目标时长
2025-07-28 22:16:57,967 - INFO - 方案 #1 调整/填充后最终总时长: 6.18秒
2025-07-28 22:16:57,967 - INFO - 方案 #1 添加到方案列表
2025-07-28 22:16:57,967 - INFO - 开始生成方案 #2
2025-07-28 22:16:57,967 - WARNING - 方案 #2: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:57,967 - INFO - 开始生成方案 #3
2025-07-28 22:16:57,967 - WARNING - 方案 #3: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:57,967 - INFO - 开始生成方案 #4
2025-07-28 22:16:57,967 - WARNING - 方案 #4: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:57,967 - INFO - 开始生成方案 #5
2025-07-28 22:16:57,967 - WARNING - 方案 #5: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:57,967 - INFO - 开始生成方案 #6
2025-07-28 22:16:57,967 - WARNING - 方案 #6: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:57,967 - INFO - ========== 字幕 #37 的 1 套有效场景方案生成完成 ==========
2025-07-28 22:16:57,967 - INFO - 
----- 处理字幕 #37 的方案 #1 -----
2025-07-28 22:16:57,967 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\37_1.mp4
2025-07-28 22:16:57,967 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp63fvjer1
2025-07-28 22:16:57,968 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1590.mp4 (确认存在: True)
2025-07-28 22:16:57,968 - INFO - 添加场景ID=1590，时长=1.56秒，累计时长=1.56秒
2025-07-28 22:16:57,968 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1591.mp4 (确认存在: True)
2025-07-28 22:16:57,968 - INFO - 添加场景ID=1591，时长=1.80秒，累计时长=3.36秒
2025-07-28 22:16:57,968 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1592.mp4 (确认存在: True)
2025-07-28 22:16:57,968 - INFO - 添加场景ID=1592，时长=1.36秒，累计时长=4.72秒
2025-07-28 22:16:57,968 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1593.mp4 (确认存在: True)
2025-07-28 22:16:57,968 - INFO - 添加场景ID=1593，时长=2.56秒，累计时长=7.28秒
2025-07-28 22:16:57,968 - INFO - 准备合并 4 个场景文件，总时长约 7.28秒
2025-07-28 22:16:57,968 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1590.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1591.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1592.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1593.mp4'

2025-07-28 22:16:57,968 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp63fvjer1\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp63fvjer1\temp_combined.mp4
2025-07-28 22:16:58,154 - INFO - 合并后的视频时长: 7.37秒，目标音频时长: 6.18秒
2025-07-28 22:16:58,154 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp63fvjer1\temp_combined.mp4 -ss 0 -to 6.185 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\37_1.mp4
2025-07-28 22:16:58,514 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 22:16:58,515 - INFO - 目标音频时长: 6.18秒
2025-07-28 22:16:58,515 - INFO - 实际视频时长: 6.22秒
2025-07-28 22:16:58,515 - INFO - 时长差异: 0.04秒 (0.61%)
2025-07-28 22:16:58,515 - INFO - ==========================================
2025-07-28 22:16:58,515 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 22:16:58,515 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\37_1.mp4
2025-07-28 22:16:58,516 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp63fvjer1
2025-07-28 22:16:58,560 - INFO - 方案 #1 处理完成:
2025-07-28 22:16:58,560 - INFO -   - 音频时长: 6.18秒
2025-07-28 22:16:58,560 - INFO -   - 视频时长: 6.22秒
2025-07-28 22:16:58,560 - INFO -   - 时长差异: 0.04秒 (0.61%)
2025-07-28 22:16:58,561 - INFO - 
字幕 #37 处理完成，成功生成 1/1 套方案
2025-07-28 22:16:58,561 - INFO - 生成的视频文件:
2025-07-28 22:16:58,561 - INFO -   1. F:/github/aicut_auto/newcut_ai\37_1.mp4
2025-07-28 22:16:58,561 - INFO - ========== 字幕 #37 处理结束 ==========

