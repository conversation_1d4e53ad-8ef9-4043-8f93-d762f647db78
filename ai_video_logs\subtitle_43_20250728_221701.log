2025-07-28 22:17:01,435 - INFO - ========== 字幕 #43 处理开始 ==========
2025-07-28 22:17:01,435 - INFO - 字幕内容: 谁知推开房门，里面端坐的竟是镇国公世子！
2025-07-28 22:17:01,435 - INFO - 字幕序号: [2034, 2035]
2025-07-28 22:17:01,435 - INFO - 音频文件详情:
2025-07-28 22:17:01,435 - INFO -   - 路径: output\43.wav
2025-07-28 22:17:01,435 - INFO -   - 时长: 3.94秒
2025-07-28 22:17:01,435 - INFO -   - 验证音频时长: 3.94秒
2025-07-28 22:17:01,435 - INFO - 字幕时间戳信息:
2025-07-28 22:17:01,437 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-28 22:17:01,445 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-28 22:17:01,445 - INFO -   - 根据生成的音频时长(3.94秒)已调整字幕时间戳
2025-07-28 22:17:01,445 - INFO - ========== 开始为字幕 #43 生成 6 套场景方案 ==========
2025-07-28 22:17:01,445 - INFO - 开始查找字幕序号 [2034, 2035] 对应的场景，共有 2710 个场景可选
2025-07-28 22:17:01,446 - INFO - 找到related_overlap场景: scene_id=1885, 字幕#2034
2025-07-28 22:17:01,446 - INFO - 找到related_between场景: scene_id=1880, 字幕#2034
2025-07-28 22:17:01,446 - INFO - 找到related_between场景: scene_id=1881, 字幕#2034
2025-07-28 22:17:01,446 - INFO - 找到related_between场景: scene_id=1882, 字幕#2034
2025-07-28 22:17:01,446 - INFO - 找到related_between场景: scene_id=1883, 字幕#2034
2025-07-28 22:17:01,446 - INFO - 找到related_between场景: scene_id=1884, 字幕#2034
2025-07-28 22:17:01,447 - INFO - 字幕 #2034 找到 1 个overlap场景, 5 个between场景
2025-07-28 22:17:01,447 - INFO - 字幕 #2035 找到 0 个overlap场景, 0 个between场景
2025-07-28 22:17:01,447 - WARNING - 字幕 #2035 没有找到任何匹配场景!
2025-07-28 22:17:01,447 - INFO - 共收集 1 个未使用的overlap场景和 5 个未使用的between场景
2025-07-28 22:17:01,447 - INFO - 开始生成方案 #1
2025-07-28 22:17:01,447 - INFO - 方案 #1: 为字幕#2034选择初始化overlap场景id=1885
2025-07-28 22:17:01,447 - INFO - 方案 #1: 初始选择后，当前总时长=2.72秒
2025-07-28 22:17:01,447 - INFO - 方案 #1: 额外between选择后，当前总时长=2.72秒
2025-07-28 22:17:01,447 - INFO - 方案 #1: 额外添加between场景id=1882, 当前总时长=4.12秒
2025-07-28 22:17:01,447 - INFO - 方案 #1: 场景总时长(4.12秒)大于音频时长(3.94秒)，需要裁剪
2025-07-28 22:17:01,447 - INFO - 调整前总时长: 4.12秒, 目标时长: 3.94秒
2025-07-28 22:17:01,447 - INFO - 需要裁剪 0.18秒
2025-07-28 22:17:01,447 - INFO - 裁剪最长场景ID=1885：从2.72秒裁剪至2.54秒
2025-07-28 22:17:01,447 - INFO - 调整后总时长: 3.94秒，与目标时长差异: 0.00秒
2025-07-28 22:17:01,447 - INFO - 方案 #1 调整/填充后最终总时长: 3.94秒
2025-07-28 22:17:01,447 - INFO - 方案 #1 添加到方案列表
2025-07-28 22:17:01,447 - INFO - 开始生成方案 #2
2025-07-28 22:17:01,447 - INFO - 方案 #2: 初始选择后，当前总时长=0.00秒
2025-07-28 22:17:01,447 - INFO - 方案 #2: 为字幕#2034选择初始化between场景id=1880
2025-07-28 22:17:01,447 - INFO - 方案 #2: 额外between选择后，当前总时长=4.68秒
2025-07-28 22:17:01,447 - INFO - 方案 #2: 场景总时长(4.68秒)大于音频时长(3.94秒)，需要裁剪
2025-07-28 22:17:01,447 - INFO - 调整前总时长: 4.68秒, 目标时长: 3.94秒
2025-07-28 22:17:01,447 - INFO - 需要裁剪 0.74秒
2025-07-28 22:17:01,447 - INFO - 裁剪最长场景ID=1880：从4.68秒裁剪至3.94秒
2025-07-28 22:17:01,447 - INFO - 调整后总时长: 3.94秒，与目标时长差异: 0.00秒
2025-07-28 22:17:01,447 - INFO - 方案 #2 调整/填充后最终总时长: 3.94秒
2025-07-28 22:17:01,447 - INFO - 方案 #2 添加到方案列表
2025-07-28 22:17:01,447 - INFO - 开始生成方案 #3
2025-07-28 22:17:01,447 - INFO - 方案 #3: 初始选择后，当前总时长=0.00秒
2025-07-28 22:17:01,447 - INFO - 方案 #3: 为字幕#2034选择初始化between场景id=1884
2025-07-28 22:17:01,447 - INFO - 方案 #3: 额外between选择后，当前总时长=1.32秒
2025-07-28 22:17:01,447 - INFO - 方案 #3: 额外添加between场景id=1881, 当前总时长=2.52秒
2025-07-28 22:17:01,447 - INFO - 方案 #3: 额外添加between场景id=1883, 当前总时长=4.44秒
2025-07-28 22:17:01,447 - INFO - 方案 #3: 场景总时长(4.44秒)大于音频时长(3.94秒)，需要裁剪
2025-07-28 22:17:01,447 - INFO - 调整前总时长: 4.44秒, 目标时长: 3.94秒
2025-07-28 22:17:01,447 - INFO - 需要裁剪 0.50秒
2025-07-28 22:17:01,447 - INFO - 裁剪最长场景ID=1883：从1.92秒裁剪至1.42秒
2025-07-28 22:17:01,447 - INFO - 调整后总时长: 3.94秒，与目标时长差异: 0.00秒
2025-07-28 22:17:01,447 - INFO - 方案 #3 调整/填充后最终总时长: 3.94秒
2025-07-28 22:17:01,447 - INFO - 方案 #3 添加到方案列表
2025-07-28 22:17:01,447 - INFO - 开始生成方案 #4
2025-07-28 22:17:01,447 - WARNING - 方案 #4: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:17:01,447 - INFO - 开始生成方案 #5
2025-07-28 22:17:01,447 - WARNING - 方案 #5: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:17:01,447 - INFO - 开始生成方案 #6
2025-07-28 22:17:01,447 - WARNING - 方案 #6: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:17:01,447 - INFO - ========== 字幕 #43 的 3 套有效场景方案生成完成 ==========
2025-07-28 22:17:01,447 - INFO - 
----- 处理字幕 #43 的方案 #1 -----
2025-07-28 22:17:01,447 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\43_1.mp4
2025-07-28 22:17:01,448 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp8o9dlyvh
2025-07-28 22:17:01,448 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1885.mp4 (确认存在: True)
2025-07-28 22:17:01,448 - INFO - 添加场景ID=1885，时长=2.72秒，累计时长=2.72秒
2025-07-28 22:17:01,448 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1882.mp4 (确认存在: True)
2025-07-28 22:17:01,448 - INFO - 添加场景ID=1882，时长=1.40秒，累计时长=4.12秒
2025-07-28 22:17:01,449 - INFO - 准备合并 2 个场景文件，总时长约 4.12秒
2025-07-28 22:17:01,449 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1885.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1882.mp4'

2025-07-28 22:17:01,449 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp8o9dlyvh\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp8o9dlyvh\temp_combined.mp4
2025-07-28 22:17:01,586 - INFO - 合并后的视频时长: 4.17秒，目标音频时长: 3.94秒
2025-07-28 22:17:01,586 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp8o9dlyvh\temp_combined.mp4 -ss 0 -to 3.936 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\43_1.mp4
2025-07-28 22:17:01,894 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 22:17:01,894 - INFO - 目标音频时长: 3.94秒
2025-07-28 22:17:01,894 - INFO - 实际视频时长: 3.98秒
2025-07-28 22:17:01,894 - INFO - 时长差异: 0.05秒 (1.19%)
2025-07-28 22:17:01,894 - INFO - ==========================================
2025-07-28 22:17:01,894 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 22:17:01,894 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\43_1.mp4
2025-07-28 22:17:01,895 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp8o9dlyvh
2025-07-28 22:17:01,942 - INFO - 方案 #1 处理完成:
2025-07-28 22:17:01,942 - INFO -   - 音频时长: 3.94秒
2025-07-28 22:17:01,942 - INFO -   - 视频时长: 3.98秒
2025-07-28 22:17:01,942 - INFO -   - 时长差异: 0.05秒 (1.19%)
2025-07-28 22:17:01,942 - INFO - 
----- 处理字幕 #43 的方案 #2 -----
2025-07-28 22:17:01,942 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\43_2.mp4
2025-07-28 22:17:01,942 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp471xivw0
2025-07-28 22:17:01,943 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1880.mp4 (确认存在: True)
2025-07-28 22:17:01,943 - INFO - 添加场景ID=1880，时长=4.68秒，累计时长=4.68秒
2025-07-28 22:17:01,943 - INFO - 准备合并 1 个场景文件，总时长约 4.68秒
2025-07-28 22:17:01,943 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1880.mp4'

2025-07-28 22:17:01,943 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp471xivw0\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp471xivw0\temp_combined.mp4
2025-07-28 22:17:02,080 - INFO - 合并后的视频时长: 4.70秒，目标音频时长: 3.94秒
2025-07-28 22:17:02,080 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp471xivw0\temp_combined.mp4 -ss 0 -to 3.936 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\43_2.mp4
2025-07-28 22:17:02,380 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 22:17:02,380 - INFO - 目标音频时长: 3.94秒
2025-07-28 22:17:02,380 - INFO - 实际视频时长: 3.98秒
2025-07-28 22:17:02,380 - INFO - 时长差异: 0.05秒 (1.19%)
2025-07-28 22:17:02,380 - INFO - ==========================================
2025-07-28 22:17:02,380 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 22:17:02,380 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\43_2.mp4
2025-07-28 22:17:02,381 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp471xivw0
2025-07-28 22:17:02,425 - INFO - 方案 #2 处理完成:
2025-07-28 22:17:02,425 - INFO -   - 音频时长: 3.94秒
2025-07-28 22:17:02,425 - INFO -   - 视频时长: 3.98秒
2025-07-28 22:17:02,425 - INFO -   - 时长差异: 0.05秒 (1.19%)
2025-07-28 22:17:02,425 - INFO - 
----- 处理字幕 #43 的方案 #3 -----
2025-07-28 22:17:02,425 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\43_3.mp4
2025-07-28 22:17:02,425 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpyzsmgq51
2025-07-28 22:17:02,425 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1884.mp4 (确认存在: True)
2025-07-28 22:17:02,425 - INFO - 添加场景ID=1884，时长=1.32秒，累计时长=1.32秒
2025-07-28 22:17:02,427 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1881.mp4 (确认存在: True)
2025-07-28 22:17:02,427 - INFO - 添加场景ID=1881，时长=1.20秒，累计时长=2.52秒
2025-07-28 22:17:02,427 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1883.mp4 (确认存在: True)
2025-07-28 22:17:02,427 - INFO - 添加场景ID=1883，时长=1.92秒，累计时长=4.44秒
2025-07-28 22:17:02,427 - INFO - 准备合并 3 个场景文件，总时长约 4.44秒
2025-07-28 22:17:02,427 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1884.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1881.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1883.mp4'

2025-07-28 22:17:02,427 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpyzsmgq51\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpyzsmgq51\temp_combined.mp4
2025-07-28 22:17:02,596 - INFO - 合并后的视频时长: 4.51秒，目标音频时长: 3.94秒
2025-07-28 22:17:02,596 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpyzsmgq51\temp_combined.mp4 -ss 0 -to 3.936 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\43_3.mp4
2025-07-28 22:17:02,912 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 22:17:02,912 - INFO - 目标音频时长: 3.94秒
2025-07-28 22:17:02,912 - INFO - 实际视频时长: 3.98秒
2025-07-28 22:17:02,913 - INFO - 时长差异: 0.05秒 (1.19%)
2025-07-28 22:17:02,913 - INFO - ==========================================
2025-07-28 22:17:02,913 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 22:17:02,913 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\43_3.mp4
2025-07-28 22:17:02,913 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpyzsmgq51
2025-07-28 22:17:02,966 - INFO - 方案 #3 处理完成:
2025-07-28 22:17:02,966 - INFO -   - 音频时长: 3.94秒
2025-07-28 22:17:02,966 - INFO -   - 视频时长: 3.98秒
2025-07-28 22:17:02,966 - INFO -   - 时长差异: 0.05秒 (1.19%)
2025-07-28 22:17:02,966 - INFO - 
字幕 #43 处理完成，成功生成 3/3 套方案
2025-07-28 22:17:02,966 - INFO - 生成的视频文件:
2025-07-28 22:17:02,966 - INFO -   1. F:/github/aicut_auto/newcut_ai\43_1.mp4
2025-07-28 22:17:02,966 - INFO -   2. F:/github/aicut_auto/newcut_ai\43_2.mp4
2025-07-28 22:17:02,966 - INFO -   3. F:/github/aicut_auto/newcut_ai\43_3.mp4
2025-07-28 22:17:02,966 - INFO - ========== 字幕 #43 处理结束 ==========

