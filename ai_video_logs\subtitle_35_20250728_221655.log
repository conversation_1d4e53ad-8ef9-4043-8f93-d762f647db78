2025-07-28 22:16:55,807 - INFO - ========== 字幕 #35 处理开始 ==========
2025-07-28 22:16:55,807 - INFO - 字幕内容: 她得意地交出所有财产抵押书和私房钱，却不知拿到手的是一份毫无价值的分红书。
2025-07-28 22:16:55,807 - INFO - 字幕序号: [1516, 1521]
2025-07-28 22:16:55,807 - INFO - 音频文件详情:
2025-07-28 22:16:55,807 - INFO -   - 路径: output\35.wav
2025-07-28 22:16:55,807 - INFO -   - 时长: 5.31秒
2025-07-28 22:16:55,808 - INFO -   - 验证音频时长: 5.31秒
2025-07-28 22:16:55,808 - INFO - 字幕时间戳信息:
2025-07-28 22:16:55,817 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-28 22:16:55,818 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-28 22:16:55,818 - INFO -   - 根据生成的音频时长(5.31秒)已调整字幕时间戳
2025-07-28 22:16:55,818 - INFO - ========== 开始为字幕 #35 生成 6 套场景方案 ==========
2025-07-28 22:16:55,818 - INFO - 开始查找字幕序号 [1516, 1521] 对应的场景，共有 2710 个场景可选
2025-07-28 22:16:55,818 - INFO - 找到related_overlap场景: scene_id=1418, 字幕#1516
2025-07-28 22:16:55,818 - INFO - 找到related_overlap场景: scene_id=1419, 字幕#1516
2025-07-28 22:16:55,818 - INFO - 找到related_overlap场景: scene_id=1424, 字幕#1521
2025-07-28 22:16:55,819 - INFO - 找到related_between场景: scene_id=1420, 字幕#1516
2025-07-28 22:16:55,819 - INFO - 找到related_between场景: scene_id=1421, 字幕#1516
2025-07-28 22:16:55,819 - INFO - 找到related_between场景: scene_id=1422, 字幕#1516
2025-07-28 22:16:55,819 - INFO - 找到related_between场景: scene_id=1425, 字幕#1521
2025-07-28 22:16:55,819 - INFO - 找到related_between场景: scene_id=1426, 字幕#1521
2025-07-28 22:16:55,819 - INFO - 找到related_between场景: scene_id=1427, 字幕#1521
2025-07-28 22:16:55,819 - INFO - 找到related_between场景: scene_id=1428, 字幕#1521
2025-07-28 22:16:55,819 - INFO - 字幕 #1516 找到 2 个overlap场景, 3 个between场景
2025-07-28 22:16:55,819 - INFO - 字幕 #1521 找到 1 个overlap场景, 4 个between场景
2025-07-28 22:16:55,819 - INFO - 共收集 3 个未使用的overlap场景和 7 个未使用的between场景
2025-07-28 22:16:55,819 - INFO - 开始生成方案 #1
2025-07-28 22:16:55,819 - INFO - 方案 #1: 为字幕#1516选择初始化overlap场景id=1419
2025-07-28 22:16:55,819 - INFO - 方案 #1: 为字幕#1521选择初始化overlap场景id=1424
2025-07-28 22:16:55,819 - INFO - 方案 #1: 初始选择后，当前总时长=4.92秒
2025-07-28 22:16:55,819 - INFO - 方案 #1: 额外添加overlap场景id=1418, 当前总时长=6.80秒
2025-07-28 22:16:55,819 - INFO - 方案 #1: 额外between选择后，当前总时长=6.80秒
2025-07-28 22:16:55,819 - INFO - 方案 #1: 场景总时长(6.80秒)大于音频时长(5.31秒)，需要裁剪
2025-07-28 22:16:55,819 - INFO - 调整前总时长: 6.80秒, 目标时长: 5.31秒
2025-07-28 22:16:55,819 - INFO - 需要裁剪 1.49秒
2025-07-28 22:16:55,819 - INFO - 裁剪最长场景ID=1424：从3.08秒裁剪至1.59秒
2025-07-28 22:16:55,819 - INFO - 调整后总时长: 5.31秒，与目标时长差异: 0.00秒
2025-07-28 22:16:55,819 - INFO - 方案 #1 调整/填充后最终总时长: 5.31秒
2025-07-28 22:16:55,819 - INFO - 方案 #1 添加到方案列表
2025-07-28 22:16:55,819 - INFO - 开始生成方案 #2
2025-07-28 22:16:55,819 - INFO - 方案 #2: 初始选择后，当前总时长=0.00秒
2025-07-28 22:16:55,819 - INFO - 方案 #2: 为字幕#1516选择初始化between场景id=1420
2025-07-28 22:16:55,819 - INFO - 方案 #2: 为字幕#1521选择初始化between场景id=1427
2025-07-28 22:16:55,819 - INFO - 方案 #2: 额外between选择后，当前总时长=3.00秒
2025-07-28 22:16:55,819 - INFO - 方案 #2: 额外添加between场景id=1421, 当前总时长=4.60秒
2025-07-28 22:16:55,819 - INFO - 方案 #2: 额外添加between场景id=1422, 当前总时长=5.92秒
2025-07-28 22:16:55,820 - INFO - 方案 #2: 场景总时长(5.92秒)大于音频时长(5.31秒)，需要裁剪
2025-07-28 22:16:55,820 - INFO - 调整前总时长: 5.92秒, 目标时长: 5.31秒
2025-07-28 22:16:55,820 - INFO - 需要裁剪 0.61秒
2025-07-28 22:16:55,820 - INFO - 裁剪最长场景ID=1427：从1.72秒裁剪至1.11秒
2025-07-28 22:16:55,820 - INFO - 调整后总时长: 5.31秒，与目标时长差异: 0.00秒
2025-07-28 22:16:55,820 - INFO - 方案 #2 调整/填充后最终总时长: 5.31秒
2025-07-28 22:16:55,820 - INFO - 方案 #2 添加到方案列表
2025-07-28 22:16:55,820 - INFO - 开始生成方案 #3
2025-07-28 22:16:55,820 - INFO - 方案 #3: 初始选择后，当前总时长=0.00秒
2025-07-28 22:16:55,820 - INFO - 方案 #3: 为字幕#1521选择初始化between场景id=1426
2025-07-28 22:16:55,820 - INFO - 方案 #3: 额外between选择后，当前总时长=2.68秒
2025-07-28 22:16:55,820 - INFO - 方案 #3: 额外添加between场景id=1425, 当前总时长=3.92秒
2025-07-28 22:16:55,820 - INFO - 方案 #3: 额外添加between场景id=1428, 当前总时长=5.16秒
2025-07-28 22:16:55,820 - INFO - 方案 #3: 场景总时长(5.16秒)小于音频时长(5.31秒)，需要延伸填充
2025-07-28 22:16:55,820 - INFO - 方案 #3: 最后一个场景ID: 1428
2025-07-28 22:16:55,820 - INFO - 方案 #3: 找到最后一个场景在原始列表中的索引: 1427
2025-07-28 22:16:55,820 - INFO - 方案 #3: 需要填充时长: 0.15秒
2025-07-28 22:16:55,820 - INFO - 方案 #3: 追加场景 scene_id=1429 (裁剪至 0.15秒)
2025-07-28 22:16:55,820 - INFO - 方案 #3: 成功填充至目标时长
2025-07-28 22:16:55,820 - INFO - 方案 #3 调整/填充后最终总时长: 5.31秒
2025-07-28 22:16:55,820 - INFO - 方案 #3 添加到方案列表
2025-07-28 22:16:55,820 - INFO - 开始生成方案 #4
2025-07-28 22:16:55,820 - WARNING - 方案 #4: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:55,820 - INFO - 开始生成方案 #5
2025-07-28 22:16:55,820 - WARNING - 方案 #5: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:55,820 - INFO - 开始生成方案 #6
2025-07-28 22:16:55,820 - WARNING - 方案 #6: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:55,820 - INFO - ========== 字幕 #35 的 3 套有效场景方案生成完成 ==========
2025-07-28 22:16:55,820 - INFO - 
----- 处理字幕 #35 的方案 #1 -----
2025-07-28 22:16:55,821 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\35_1.mp4
2025-07-28 22:16:55,821 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpm_wbn4sq
2025-07-28 22:16:55,821 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1419.mp4 (确认存在: True)
2025-07-28 22:16:55,821 - INFO - 添加场景ID=1419，时长=1.84秒，累计时长=1.84秒
2025-07-28 22:16:55,821 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1424.mp4 (确认存在: True)
2025-07-28 22:16:55,821 - INFO - 添加场景ID=1424，时长=3.08秒，累计时长=4.92秒
2025-07-28 22:16:55,822 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1418.mp4 (确认存在: True)
2025-07-28 22:16:55,822 - INFO - 添加场景ID=1418，时长=1.88秒，累计时长=6.80秒
2025-07-28 22:16:55,822 - INFO - 准备合并 3 个场景文件，总时长约 6.80秒
2025-07-28 22:16:55,822 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1419.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1424.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1418.mp4'

2025-07-28 22:16:55,822 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpm_wbn4sq\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpm_wbn4sq\temp_combined.mp4
2025-07-28 22:16:55,965 - INFO - 合并后的视频时长: 6.87秒，目标音频时长: 5.31秒
2025-07-28 22:16:55,965 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpm_wbn4sq\temp_combined.mp4 -ss 0 -to 5.313 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\35_1.mp4
2025-07-28 22:16:56,309 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 22:16:56,309 - INFO - 目标音频时长: 5.31秒
2025-07-28 22:16:56,309 - INFO - 实际视频时长: 5.34秒
2025-07-28 22:16:56,309 - INFO - 时长差异: 0.03秒 (0.56%)
2025-07-28 22:16:56,309 - INFO - ==========================================
2025-07-28 22:16:56,309 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 22:16:56,309 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\35_1.mp4
2025-07-28 22:16:56,310 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpm_wbn4sq
2025-07-28 22:16:56,353 - INFO - 方案 #1 处理完成:
2025-07-28 22:16:56,353 - INFO -   - 音频时长: 5.31秒
2025-07-28 22:16:56,353 - INFO -   - 视频时长: 5.34秒
2025-07-28 22:16:56,353 - INFO -   - 时长差异: 0.03秒 (0.56%)
2025-07-28 22:16:56,353 - INFO - 
----- 处理字幕 #35 的方案 #2 -----
2025-07-28 22:16:56,353 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\35_2.mp4
2025-07-28 22:16:56,354 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpsinwz_bg
2025-07-28 22:16:56,354 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1420.mp4 (确认存在: True)
2025-07-28 22:16:56,354 - INFO - 添加场景ID=1420，时长=1.28秒，累计时长=1.28秒
2025-07-28 22:16:56,354 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1427.mp4 (确认存在: True)
2025-07-28 22:16:56,354 - INFO - 添加场景ID=1427，时长=1.72秒，累计时长=3.00秒
2025-07-28 22:16:56,354 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1421.mp4 (确认存在: True)
2025-07-28 22:16:56,354 - INFO - 添加场景ID=1421，时长=1.60秒，累计时长=4.60秒
2025-07-28 22:16:56,354 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1422.mp4 (确认存在: True)
2025-07-28 22:16:56,354 - INFO - 添加场景ID=1422，时长=1.32秒，累计时长=5.92秒
2025-07-28 22:16:56,354 - INFO - 准备合并 4 个场景文件，总时长约 5.92秒
2025-07-28 22:16:56,355 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1420.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1427.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1421.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1422.mp4'

2025-07-28 22:16:56,355 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpsinwz_bg\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpsinwz_bg\temp_combined.mp4
2025-07-28 22:16:56,528 - INFO - 合并后的视频时长: 6.01秒，目标音频时长: 5.31秒
2025-07-28 22:16:56,528 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpsinwz_bg\temp_combined.mp4 -ss 0 -to 5.313 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\35_2.mp4
2025-07-28 22:16:56,872 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 22:16:56,872 - INFO - 目标音频时长: 5.31秒
2025-07-28 22:16:56,872 - INFO - 实际视频时长: 5.34秒
2025-07-28 22:16:56,872 - INFO - 时长差异: 0.03秒 (0.56%)
2025-07-28 22:16:56,872 - INFO - ==========================================
2025-07-28 22:16:56,872 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 22:16:56,872 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\35_2.mp4
2025-07-28 22:16:56,873 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpsinwz_bg
2025-07-28 22:16:56,918 - INFO - 方案 #2 处理完成:
2025-07-28 22:16:56,918 - INFO -   - 音频时长: 5.31秒
2025-07-28 22:16:56,918 - INFO -   - 视频时长: 5.34秒
2025-07-28 22:16:56,918 - INFO -   - 时长差异: 0.03秒 (0.56%)
2025-07-28 22:16:56,918 - INFO - 
----- 处理字幕 #35 的方案 #3 -----
2025-07-28 22:16:56,918 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\35_3.mp4
2025-07-28 22:16:56,918 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpt7vswn_1
2025-07-28 22:16:56,919 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1426.mp4 (确认存在: True)
2025-07-28 22:16:56,919 - INFO - 添加场景ID=1426，时长=2.68秒，累计时长=2.68秒
2025-07-28 22:16:56,919 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1425.mp4 (确认存在: True)
2025-07-28 22:16:56,919 - INFO - 添加场景ID=1425，时长=1.24秒，累计时长=3.92秒
2025-07-28 22:16:56,919 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1428.mp4 (确认存在: True)
2025-07-28 22:16:56,919 - INFO - 添加场景ID=1428，时长=1.24秒，累计时长=5.16秒
2025-07-28 22:16:56,919 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1429.mp4 (确认存在: True)
2025-07-28 22:16:56,919 - INFO - 添加场景ID=1429，时长=1.92秒，累计时长=7.08秒
2025-07-28 22:16:56,919 - INFO - 准备合并 4 个场景文件，总时长约 7.08秒
2025-07-28 22:16:56,919 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1426.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1425.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1428.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1429.mp4'

2025-07-28 22:16:56,919 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpt7vswn_1\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpt7vswn_1\temp_combined.mp4
2025-07-28 22:16:57,077 - INFO - 合并后的视频时长: 7.17秒，目标音频时长: 5.31秒
2025-07-28 22:16:57,077 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpt7vswn_1\temp_combined.mp4 -ss 0 -to 5.313 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\35_3.mp4
2025-07-28 22:16:57,430 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 22:16:57,430 - INFO - 目标音频时长: 5.31秒
2025-07-28 22:16:57,430 - INFO - 实际视频时长: 5.34秒
2025-07-28 22:16:57,430 - INFO - 时长差异: 0.03秒 (0.56%)
2025-07-28 22:16:57,430 - INFO - ==========================================
2025-07-28 22:16:57,430 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 22:16:57,431 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\35_3.mp4
2025-07-28 22:16:57,431 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpt7vswn_1
2025-07-28 22:16:57,476 - INFO - 方案 #3 处理完成:
2025-07-28 22:16:57,476 - INFO -   - 音频时长: 5.31秒
2025-07-28 22:16:57,476 - INFO -   - 视频时长: 5.34秒
2025-07-28 22:16:57,476 - INFO -   - 时长差异: 0.03秒 (0.56%)
2025-07-28 22:16:57,476 - INFO - 
字幕 #35 处理完成，成功生成 3/3 套方案
2025-07-28 22:16:57,476 - INFO - 生成的视频文件:
2025-07-28 22:16:57,476 - INFO -   1. F:/github/aicut_auto/newcut_ai\35_1.mp4
2025-07-28 22:16:57,476 - INFO -   2. F:/github/aicut_auto/newcut_ai\35_2.mp4
2025-07-28 22:16:57,476 - INFO -   3. F:/github/aicut_auto/newcut_ai\35_3.mp4
2025-07-28 22:16:57,476 - INFO - ========== 字幕 #35 处理结束 ==========

