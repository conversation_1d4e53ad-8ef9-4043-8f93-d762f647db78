2025-07-28 22:17:11,483 - INFO - ========== 字幕 #56 处理开始 ==========
2025-07-28 22:17:11,483 - INFO - 字幕内容: 二皇子当众表示，自己心悦之人正是这位养女，恳求父皇为他们赐婚。
2025-07-28 22:17:11,483 - INFO - 字幕序号: [2653, 2654]
2025-07-28 22:17:11,485 - INFO - 音频文件详情:
2025-07-28 22:17:11,485 - INFO -   - 路径: output\56.wav
2025-07-28 22:17:11,485 - INFO -   - 时长: 5.68秒
2025-07-28 22:17:11,485 - INFO -   - 验证音频时长: 5.68秒
2025-07-28 22:17:11,485 - INFO - 字幕时间戳信息:
2025-07-28 22:17:11,493 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-28 22:17:11,493 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-28 22:17:11,493 - INFO -   - 根据生成的音频时长(5.68秒)已调整字幕时间戳
2025-07-28 22:17:11,493 - INFO - ========== 开始为字幕 #56 生成 6 套场景方案 ==========
2025-07-28 22:17:11,495 - INFO - 开始查找字幕序号 [2653, 2654] 对应的场景，共有 2710 个场景可选
2025-07-28 22:17:11,495 - INFO - 找到related_overlap场景: scene_id=2526, 字幕#2653
2025-07-28 22:17:11,495 - INFO - 找到related_overlap场景: scene_id=2527, 字幕#2653
2025-07-28 22:17:11,495 - INFO - 找到related_overlap场景: scene_id=2528, 字幕#2654
2025-07-28 22:17:11,496 - INFO - 找到related_between场景: scene_id=2529, 字幕#2654
2025-07-28 22:17:11,496 - INFO - 找到related_between场景: scene_id=2530, 字幕#2654
2025-07-28 22:17:11,496 - INFO - 找到related_between场景: scene_id=2531, 字幕#2654
2025-07-28 22:17:11,496 - INFO - 找到related_between场景: scene_id=2532, 字幕#2654
2025-07-28 22:17:11,496 - INFO - 字幕 #2653 找到 2 个overlap场景, 0 个between场景
2025-07-28 22:17:11,496 - INFO - 字幕 #2654 找到 1 个overlap场景, 4 个between场景
2025-07-28 22:17:11,496 - INFO - 共收集 3 个未使用的overlap场景和 4 个未使用的between场景
2025-07-28 22:17:11,496 - INFO - 开始生成方案 #1
2025-07-28 22:17:11,496 - INFO - 方案 #1: 为字幕#2653选择初始化overlap场景id=2526
2025-07-28 22:17:11,496 - INFO - 方案 #1: 为字幕#2654选择初始化overlap场景id=2528
2025-07-28 22:17:11,496 - INFO - 方案 #1: 初始选择后，当前总时长=4.04秒
2025-07-28 22:17:11,496 - INFO - 方案 #1: 额外添加overlap场景id=2527, 当前总时长=5.44秒
2025-07-28 22:17:11,496 - INFO - 方案 #1: 额外between选择后，当前总时长=5.44秒
2025-07-28 22:17:11,496 - INFO - 方案 #1: 额外添加between场景id=2532, 当前总时长=6.64秒
2025-07-28 22:17:11,496 - INFO - 方案 #1: 场景总时长(6.64秒)大于音频时长(5.68秒)，需要裁剪
2025-07-28 22:17:11,496 - INFO - 调整前总时长: 6.64秒, 目标时长: 5.68秒
2025-07-28 22:17:11,496 - INFO - 需要裁剪 0.96秒
2025-07-28 22:17:11,496 - INFO - 裁剪最长场景ID=2526：从2.20秒裁剪至1.24秒
2025-07-28 22:17:11,496 - INFO - 调整后总时长: 5.68秒，与目标时长差异: 0.00秒
2025-07-28 22:17:11,496 - INFO - 方案 #1 调整/填充后最终总时长: 5.68秒
2025-07-28 22:17:11,496 - INFO - 方案 #1 添加到方案列表
2025-07-28 22:17:11,496 - INFO - 开始生成方案 #2
2025-07-28 22:17:11,496 - INFO - 方案 #2: 初始选择后，当前总时长=0.00秒
2025-07-28 22:17:11,496 - INFO - 方案 #2: 为字幕#2654选择初始化between场景id=2531
2025-07-28 22:17:11,496 - INFO - 方案 #2: 额外between选择后，当前总时长=1.48秒
2025-07-28 22:17:11,496 - INFO - 方案 #2: 额外添加between场景id=2530, 当前总时长=2.56秒
2025-07-28 22:17:11,496 - INFO - 方案 #2: 额外添加between场景id=2529, 当前总时长=3.96秒
2025-07-28 22:17:11,496 - INFO - 方案 #2: 场景总时长(3.96秒)小于音频时长(5.68秒)，需要延伸填充
2025-07-28 22:17:11,496 - INFO - 方案 #2: 最后一个场景ID: 2529
2025-07-28 22:17:11,496 - INFO - 方案 #2: 找到最后一个场景在原始列表中的索引: 2528
2025-07-28 22:17:11,496 - INFO - 方案 #2: 需要填充时长: 1.72秒
2025-07-28 22:17:11,496 - INFO - 方案 #2: 跳过已使用的场景: scene_id=2530
2025-07-28 22:17:11,496 - INFO - 方案 #2: 跳过已使用的场景: scene_id=2531
2025-07-28 22:17:11,496 - INFO - 方案 #2: 跳过已使用的场景: scene_id=2532
2025-07-28 22:17:11,496 - INFO - 方案 #2: 追加场景 scene_id=2533 (裁剪至 1.72秒)
2025-07-28 22:17:11,496 - INFO - 方案 #2: 成功填充至目标时长
2025-07-28 22:17:11,496 - INFO - 方案 #2 调整/填充后最终总时长: 5.68秒
2025-07-28 22:17:11,496 - INFO - 方案 #2 添加到方案列表
2025-07-28 22:17:11,496 - INFO - 开始生成方案 #3
2025-07-28 22:17:11,497 - WARNING - 方案 #3: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:17:11,497 - INFO - 开始生成方案 #4
2025-07-28 22:17:11,497 - WARNING - 方案 #4: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:17:11,497 - INFO - 开始生成方案 #5
2025-07-28 22:17:11,497 - WARNING - 方案 #5: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:17:11,497 - INFO - 开始生成方案 #6
2025-07-28 22:17:11,497 - WARNING - 方案 #6: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:17:11,497 - INFO - ========== 字幕 #56 的 2 套有效场景方案生成完成 ==========
2025-07-28 22:17:11,497 - INFO - 
----- 处理字幕 #56 的方案 #1 -----
2025-07-28 22:17:11,497 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\56_1.mp4
2025-07-28 22:17:11,497 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpiiv_4ndf
2025-07-28 22:17:11,497 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2526.mp4 (确认存在: True)
2025-07-28 22:17:11,497 - INFO - 添加场景ID=2526，时长=2.20秒，累计时长=2.20秒
2025-07-28 22:17:11,498 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2528.mp4 (确认存在: True)
2025-07-28 22:17:11,498 - INFO - 添加场景ID=2528，时长=1.84秒，累计时长=4.04秒
2025-07-28 22:17:11,498 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2527.mp4 (确认存在: True)
2025-07-28 22:17:11,498 - INFO - 添加场景ID=2527，时长=1.40秒，累计时长=5.44秒
2025-07-28 22:17:11,498 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2532.mp4 (确认存在: True)
2025-07-28 22:17:11,498 - INFO - 添加场景ID=2532，时长=1.20秒，累计时长=6.64秒
2025-07-28 22:17:11,498 - INFO - 准备合并 4 个场景文件，总时长约 6.64秒
2025-07-28 22:17:11,498 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2526.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2528.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2527.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2532.mp4'

2025-07-28 22:17:11,498 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpiiv_4ndf\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpiiv_4ndf\temp_combined.mp4
2025-07-28 22:17:11,657 - INFO - 合并后的视频时长: 6.73秒，目标音频时长: 5.68秒
2025-07-28 22:17:11,658 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpiiv_4ndf\temp_combined.mp4 -ss 0 -to 5.676 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\56_1.mp4
2025-07-28 22:17:11,998 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 22:17:11,998 - INFO - 目标音频时长: 5.68秒
2025-07-28 22:17:11,998 - INFO - 实际视频时长: 5.70秒
2025-07-28 22:17:11,998 - INFO - 时长差异: 0.03秒 (0.48%)
2025-07-28 22:17:11,998 - INFO - ==========================================
2025-07-28 22:17:11,998 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 22:17:11,998 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\56_1.mp4
2025-07-28 22:17:11,999 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpiiv_4ndf
2025-07-28 22:17:12,042 - INFO - 方案 #1 处理完成:
2025-07-28 22:17:12,042 - INFO -   - 音频时长: 5.68秒
2025-07-28 22:17:12,042 - INFO -   - 视频时长: 5.70秒
2025-07-28 22:17:12,042 - INFO -   - 时长差异: 0.03秒 (0.48%)
2025-07-28 22:17:12,042 - INFO - 
----- 处理字幕 #56 的方案 #2 -----
2025-07-28 22:17:12,042 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\56_2.mp4
2025-07-28 22:17:12,042 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp6qsq679f
2025-07-28 22:17:12,043 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2531.mp4 (确认存在: True)
2025-07-28 22:17:12,043 - INFO - 添加场景ID=2531，时长=1.48秒，累计时长=1.48秒
2025-07-28 22:17:12,043 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2530.mp4 (确认存在: True)
2025-07-28 22:17:12,043 - INFO - 添加场景ID=2530，时长=1.08秒，累计时长=2.56秒
2025-07-28 22:17:12,043 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2529.mp4 (确认存在: True)
2025-07-28 22:17:12,043 - INFO - 添加场景ID=2529，时长=1.40秒，累计时长=3.96秒
2025-07-28 22:17:12,043 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2533.mp4 (确认存在: True)
2025-07-28 22:17:12,043 - INFO - 添加场景ID=2533，时长=2.72秒，累计时长=6.68秒
2025-07-28 22:17:12,043 - INFO - 准备合并 4 个场景文件，总时长约 6.68秒
2025-07-28 22:17:12,043 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2531.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2530.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2529.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2533.mp4'

2025-07-28 22:17:12,043 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp6qsq679f\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp6qsq679f\temp_combined.mp4
2025-07-28 22:17:12,221 - INFO - 合并后的视频时长: 6.77秒，目标音频时长: 5.68秒
2025-07-28 22:17:12,221 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp6qsq679f\temp_combined.mp4 -ss 0 -to 5.676 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\56_2.mp4
2025-07-28 22:17:12,600 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 22:17:12,600 - INFO - 目标音频时长: 5.68秒
2025-07-28 22:17:12,600 - INFO - 实际视频时长: 5.70秒
2025-07-28 22:17:12,600 - INFO - 时长差异: 0.03秒 (0.48%)
2025-07-28 22:17:12,600 - INFO - ==========================================
2025-07-28 22:17:12,600 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 22:17:12,600 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\56_2.mp4
2025-07-28 22:17:12,601 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp6qsq679f
2025-07-28 22:17:12,645 - INFO - 方案 #2 处理完成:
2025-07-28 22:17:12,645 - INFO -   - 音频时长: 5.68秒
2025-07-28 22:17:12,645 - INFO -   - 视频时长: 5.70秒
2025-07-28 22:17:12,645 - INFO -   - 时长差异: 0.03秒 (0.48%)
2025-07-28 22:17:12,645 - INFO - 
字幕 #56 处理完成，成功生成 2/2 套方案
2025-07-28 22:17:12,645 - INFO - 生成的视频文件:
2025-07-28 22:17:12,645 - INFO -   1. F:/github/aicut_auto/newcut_ai\56_1.mp4
2025-07-28 22:17:12,645 - INFO -   2. F:/github/aicut_auto/newcut_ai\56_2.mp4
2025-07-28 22:17:12,645 - INFO - ========== 字幕 #56 处理结束 ==========

