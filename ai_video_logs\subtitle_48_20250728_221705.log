2025-07-28 22:17:05,806 - INFO - ========== 字幕 #48 处理开始 ==========
2025-07-28 22:17:05,806 - INFO - 字幕内容: 二哥终于彻底醒悟，原来一直舍命救自己的，竟是从前最不信任的亲妹妹。
2025-07-28 22:17:05,806 - INFO - 字幕序号: [2202, 2204]
2025-07-28 22:17:05,806 - INFO - 音频文件详情:
2025-07-28 22:17:05,806 - INFO -   - 路径: output\48.wav
2025-07-28 22:17:05,806 - INFO -   - 时长: 6.94秒
2025-07-28 22:17:05,807 - INFO -   - 验证音频时长: 6.94秒
2025-07-28 22:17:05,807 - INFO - 字幕时间戳信息:
2025-07-28 22:17:05,807 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-28 22:17:05,807 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-28 22:17:05,807 - INFO -   - 根据生成的音频时长(6.94秒)已调整字幕时间戳
2025-07-28 22:17:05,807 - INFO - ========== 开始为字幕 #48 生成 6 套场景方案 ==========
2025-07-28 22:17:05,807 - INFO - 开始查找字幕序号 [2202, 2204] 对应的场景，共有 2710 个场景可选
2025-07-28 22:17:05,807 - INFO - 找到related_overlap场景: scene_id=2058, 字幕#2202
2025-07-28 22:17:05,807 - INFO - 找到related_overlap场景: scene_id=2059, 字幕#2204
2025-07-28 22:17:05,808 - INFO - 字幕 #2202 找到 1 个overlap场景, 0 个between场景
2025-07-28 22:17:05,808 - INFO - 字幕 #2204 找到 1 个overlap场景, 0 个between场景
2025-07-28 22:17:05,808 - INFO - 共收集 2 个未使用的overlap场景和 0 个未使用的between场景
2025-07-28 22:17:05,808 - INFO - 开始生成方案 #1
2025-07-28 22:17:05,808 - INFO - 方案 #1: 为字幕#2202选择初始化overlap场景id=2058
2025-07-28 22:17:05,808 - INFO - 方案 #1: 为字幕#2204选择初始化overlap场景id=2059
2025-07-28 22:17:05,808 - INFO - 方案 #1: 初始选择后，当前总时长=4.40秒
2025-07-28 22:17:05,808 - INFO - 方案 #1: 额外between选择后，当前总时长=4.40秒
2025-07-28 22:17:05,808 - INFO - 方案 #1: 场景总时长(4.40秒)小于音频时长(6.94秒)，需要延伸填充
2025-07-28 22:17:05,808 - INFO - 方案 #1: 最后一个场景ID: 2059
2025-07-28 22:17:05,808 - INFO - 方案 #1: 找到最后一个场景在原始列表中的索引: 2058
2025-07-28 22:17:05,808 - INFO - 方案 #1: 需要填充时长: 2.54秒
2025-07-28 22:17:05,808 - INFO - 方案 #1: 追加场景 scene_id=2060 (完整时长 1.84秒)
2025-07-28 22:17:05,808 - INFO - 方案 #1: 追加场景 scene_id=2061 (裁剪至 0.70秒)
2025-07-28 22:17:05,808 - INFO - 方案 #1: 成功填充至目标时长
2025-07-28 22:17:05,809 - INFO - 方案 #1 调整/填充后最终总时长: 6.94秒
2025-07-28 22:17:05,809 - INFO - 方案 #1 添加到方案列表
2025-07-28 22:17:05,809 - INFO - 开始生成方案 #2
2025-07-28 22:17:05,809 - WARNING - 方案 #2: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:17:05,809 - INFO - 开始生成方案 #3
2025-07-28 22:17:05,809 - WARNING - 方案 #3: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:17:05,809 - INFO - 开始生成方案 #4
2025-07-28 22:17:05,809 - WARNING - 方案 #4: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:17:05,809 - INFO - 开始生成方案 #5
2025-07-28 22:17:05,809 - WARNING - 方案 #5: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:17:05,809 - INFO - 开始生成方案 #6
2025-07-28 22:17:05,809 - WARNING - 方案 #6: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:17:05,809 - INFO - ========== 字幕 #48 的 1 套有效场景方案生成完成 ==========
2025-07-28 22:17:05,809 - INFO - 
----- 处理字幕 #48 的方案 #1 -----
2025-07-28 22:17:05,809 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\48_1.mp4
2025-07-28 22:17:05,809 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpm5tz0amw
2025-07-28 22:17:05,810 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2058.mp4 (确认存在: True)
2025-07-28 22:17:05,810 - INFO - 添加场景ID=2058，时长=2.32秒，累计时长=2.32秒
2025-07-28 22:17:05,810 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2059.mp4 (确认存在: True)
2025-07-28 22:17:05,810 - INFO - 添加场景ID=2059，时长=2.08秒，累计时长=4.40秒
2025-07-28 22:17:05,810 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2060.mp4 (确认存在: True)
2025-07-28 22:17:05,810 - INFO - 添加场景ID=2060，时长=1.84秒，累计时长=6.24秒
2025-07-28 22:17:05,810 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2061.mp4 (确认存在: True)
2025-07-28 22:17:05,810 - INFO - 添加场景ID=2061，时长=1.28秒，累计时长=7.52秒
2025-07-28 22:17:05,810 - INFO - 准备合并 4 个场景文件，总时长约 7.52秒
2025-07-28 22:17:05,810 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2058.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2059.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2060.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2061.mp4'

2025-07-28 22:17:05,810 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpm5tz0amw\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpm5tz0amw\temp_combined.mp4
2025-07-28 22:17:05,972 - INFO - 合并后的视频时长: 7.61秒，目标音频时长: 6.94秒
2025-07-28 22:17:05,972 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpm5tz0amw\temp_combined.mp4 -ss 0 -to 6.942 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\48_1.mp4
2025-07-28 22:17:06,298 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 22:17:06,298 - INFO - 目标音频时长: 6.94秒
2025-07-28 22:17:06,298 - INFO - 实际视频时长: 6.98秒
2025-07-28 22:17:06,298 - INFO - 时长差异: 0.04秒 (0.59%)
2025-07-28 22:17:06,298 - INFO - ==========================================
2025-07-28 22:17:06,298 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 22:17:06,298 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\48_1.mp4
2025-07-28 22:17:06,299 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpm5tz0amw
2025-07-28 22:17:06,342 - INFO - 方案 #1 处理完成:
2025-07-28 22:17:06,342 - INFO -   - 音频时长: 6.94秒
2025-07-28 22:17:06,342 - INFO -   - 视频时长: 6.98秒
2025-07-28 22:17:06,342 - INFO -   - 时长差异: 0.04秒 (0.59%)
2025-07-28 22:17:06,342 - INFO - 
字幕 #48 处理完成，成功生成 1/1 套方案
2025-07-28 22:17:06,342 - INFO - 生成的视频文件:
2025-07-28 22:17:06,342 - INFO -   1. F:/github/aicut_auto/newcut_ai\48_1.mp4
2025-07-28 22:17:06,342 - INFO - ========== 字幕 #48 处理结束 ==========

