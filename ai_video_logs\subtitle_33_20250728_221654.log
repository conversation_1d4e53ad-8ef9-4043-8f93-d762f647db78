2025-07-28 22:16:54,658 - INFO - ========== 字幕 #33 处理开始 ==========
2025-07-28 22:16:54,659 - INFO - 字幕内容: 侯爷与夫人设下惊天骗局，假借有九千万两的大生意，引诱贪婪的养女拿出所有资产投资。
2025-07-28 22:16:54,659 - INFO - 字幕序号: [1355, 1359]
2025-07-28 22:16:54,659 - INFO - 音频文件详情:
2025-07-28 22:16:54,659 - INFO -   - 路径: output\33.wav
2025-07-28 22:16:54,659 - INFO -   - 时长: 7.30秒
2025-07-28 22:16:54,659 - INFO -   - 验证音频时长: 7.30秒
2025-07-28 22:16:54,659 - INFO - 字幕时间戳信息:
2025-07-28 22:16:54,669 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-28 22:16:54,669 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-28 22:16:54,669 - INFO -   - 根据生成的音频时长(7.30秒)已调整字幕时间戳
2025-07-28 22:16:54,669 - INFO - ========== 开始为字幕 #33 生成 6 套场景方案 ==========
2025-07-28 22:16:54,669 - INFO - 开始查找字幕序号 [1355, 1359] 对应的场景，共有 2710 个场景可选
2025-07-28 22:16:54,669 - INFO - 找到related_overlap场景: scene_id=1317, 字幕#1355
2025-07-28 22:16:54,669 - INFO - 找到related_overlap场景: scene_id=1321, 字幕#1359
2025-07-28 22:16:54,670 - INFO - 找到related_between场景: scene_id=1322, 字幕#1359
2025-07-28 22:16:54,670 - INFO - 找到related_between场景: scene_id=1323, 字幕#1359
2025-07-28 22:16:54,670 - INFO - 找到related_between场景: scene_id=1324, 字幕#1359
2025-07-28 22:16:54,670 - INFO - 字幕 #1355 找到 1 个overlap场景, 0 个between场景
2025-07-28 22:16:54,670 - INFO - 字幕 #1359 找到 1 个overlap场景, 3 个between场景
2025-07-28 22:16:54,670 - INFO - 共收集 2 个未使用的overlap场景和 3 个未使用的between场景
2025-07-28 22:16:54,670 - INFO - 开始生成方案 #1
2025-07-28 22:16:54,670 - INFO - 方案 #1: 为字幕#1355选择初始化overlap场景id=1317
2025-07-28 22:16:54,670 - INFO - 方案 #1: 为字幕#1359选择初始化overlap场景id=1321
2025-07-28 22:16:54,670 - INFO - 方案 #1: 初始选择后，当前总时长=3.16秒
2025-07-28 22:16:54,670 - INFO - 方案 #1: 额外between选择后，当前总时长=3.16秒
2025-07-28 22:16:54,670 - INFO - 方案 #1: 额外添加between场景id=1324, 当前总时长=4.36秒
2025-07-28 22:16:54,670 - INFO - 方案 #1: 额外添加between场景id=1323, 当前总时长=5.36秒
2025-07-28 22:16:54,670 - INFO - 方案 #1: 额外添加between场景id=1322, 当前总时长=7.32秒
2025-07-28 22:16:54,670 - INFO - 方案 #1: 场景总时长(7.32秒)大于音频时长(7.30秒)，需要裁剪
2025-07-28 22:16:54,670 - INFO - 调整前总时长: 7.32秒, 目标时长: 7.30秒
2025-07-28 22:16:54,670 - INFO - 需要裁剪 0.01秒
2025-07-28 22:16:54,670 - INFO - 裁剪最长场景ID=1322：从1.96秒裁剪至1.95秒
2025-07-28 22:16:54,670 - INFO - 调整后总时长: 7.30秒，与目标时长差异: 0.00秒
2025-07-28 22:16:54,670 - INFO - 方案 #1 调整/填充后最终总时长: 7.30秒
2025-07-28 22:16:54,670 - INFO - 方案 #1 添加到方案列表
2025-07-28 22:16:54,670 - INFO - 开始生成方案 #2
2025-07-28 22:16:54,670 - WARNING - 方案 #2: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:54,670 - INFO - 开始生成方案 #3
2025-07-28 22:16:54,670 - WARNING - 方案 #3: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:54,670 - INFO - 开始生成方案 #4
2025-07-28 22:16:54,670 - WARNING - 方案 #4: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:54,670 - INFO - 开始生成方案 #5
2025-07-28 22:16:54,670 - WARNING - 方案 #5: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:54,670 - INFO - 开始生成方案 #6
2025-07-28 22:16:54,670 - WARNING - 方案 #6: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:54,671 - INFO - ========== 字幕 #33 的 1 套有效场景方案生成完成 ==========
2025-07-28 22:16:54,671 - INFO - 
----- 处理字幕 #33 的方案 #1 -----
2025-07-28 22:16:54,671 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\33_1.mp4
2025-07-28 22:16:54,671 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmppd4cna0f
2025-07-28 22:16:54,671 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1317.mp4 (确认存在: True)
2025-07-28 22:16:54,671 - INFO - 添加场景ID=1317，时长=1.84秒，累计时长=1.84秒
2025-07-28 22:16:54,671 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1321.mp4 (确认存在: True)
2025-07-28 22:16:54,672 - INFO - 添加场景ID=1321，时长=1.32秒，累计时长=3.16秒
2025-07-28 22:16:54,672 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1324.mp4 (确认存在: True)
2025-07-28 22:16:54,672 - INFO - 添加场景ID=1324，时长=1.20秒，累计时长=4.36秒
2025-07-28 22:16:54,672 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1323.mp4 (确认存在: True)
2025-07-28 22:16:54,672 - INFO - 添加场景ID=1323，时长=1.00秒，累计时长=5.36秒
2025-07-28 22:16:54,672 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1322.mp4 (确认存在: True)
2025-07-28 22:16:54,672 - INFO - 添加场景ID=1322，时长=1.96秒，累计时长=7.32秒
2025-07-28 22:16:54,672 - INFO - 准备合并 5 个场景文件，总时长约 7.32秒
2025-07-28 22:16:54,672 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1317.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1321.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1324.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1323.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1322.mp4'

2025-07-28 22:16:54,672 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmppd4cna0f\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmppd4cna0f\temp_combined.mp4
2025-07-28 22:16:54,877 - INFO - 合并后的视频时长: 7.44秒，目标音频时长: 7.30秒
2025-07-28 22:16:54,877 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmppd4cna0f\temp_combined.mp4 -ss 0 -to 7.305 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\33_1.mp4
2025-07-28 22:16:55,269 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 22:16:55,269 - INFO - 目标音频时长: 7.30秒
2025-07-28 22:16:55,269 - INFO - 实际视频时长: 7.34秒
2025-07-28 22:16:55,269 - INFO - 时长差异: 0.04秒 (0.52%)
2025-07-28 22:16:55,269 - INFO - ==========================================
2025-07-28 22:16:55,269 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 22:16:55,269 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\33_1.mp4
2025-07-28 22:16:55,270 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmppd4cna0f
2025-07-28 22:16:55,315 - INFO - 方案 #1 处理完成:
2025-07-28 22:16:55,315 - INFO -   - 音频时长: 7.30秒
2025-07-28 22:16:55,315 - INFO -   - 视频时长: 7.34秒
2025-07-28 22:16:55,315 - INFO -   - 时长差异: 0.04秒 (0.52%)
2025-07-28 22:16:55,315 - INFO - 
字幕 #33 处理完成，成功生成 1/1 套方案
2025-07-28 22:16:55,315 - INFO - 生成的视频文件:
2025-07-28 22:16:55,315 - INFO -   1. F:/github/aicut_auto/newcut_ai\33_1.mp4
2025-07-28 22:16:55,315 - INFO - ========== 字幕 #33 处理结束 ==========

