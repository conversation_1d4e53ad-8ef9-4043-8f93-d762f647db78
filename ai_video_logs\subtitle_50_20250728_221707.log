2025-07-28 22:17:07,037 - INFO - ========== 字幕 #50 处理开始 ==========
2025-07-28 22:17:07,038 - INFO - 字幕内容: 侯府夫人彻底爆发，以牙还牙，直接以主母身份将这养女许配给了城东的屠夫！
2025-07-28 22:17:07,038 - INFO - 字幕序号: [2412, 2414]
2025-07-28 22:17:07,038 - INFO - 音频文件详情:
2025-07-28 22:17:07,038 - INFO -   - 路径: output\50.wav
2025-07-28 22:17:07,038 - INFO -   - 时长: 6.73秒
2025-07-28 22:17:07,038 - INFO -   - 验证音频时长: 6.73秒
2025-07-28 22:17:07,038 - INFO - 字幕时间戳信息:
2025-07-28 22:17:07,038 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-28 22:17:07,038 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-28 22:17:07,038 - INFO -   - 根据生成的音频时长(6.73秒)已调整字幕时间戳
2025-07-28 22:17:07,038 - INFO - ========== 开始为字幕 #50 生成 6 套场景方案 ==========
2025-07-28 22:17:07,038 - INFO - 开始查找字幕序号 [2412, 2414] 对应的场景，共有 2710 个场景可选
2025-07-28 22:17:07,039 - INFO - 找到related_overlap场景: scene_id=2269, 字幕#2412
2025-07-28 22:17:07,039 - INFO - 找到related_overlap场景: scene_id=2271, 字幕#2414
2025-07-28 22:17:07,040 - INFO - 字幕 #2412 找到 1 个overlap场景, 0 个between场景
2025-07-28 22:17:07,040 - INFO - 字幕 #2414 找到 1 个overlap场景, 0 个between场景
2025-07-28 22:17:07,040 - INFO - 共收集 2 个未使用的overlap场景和 0 个未使用的between场景
2025-07-28 22:17:07,040 - INFO - 开始生成方案 #1
2025-07-28 22:17:07,040 - INFO - 方案 #1: 为字幕#2412选择初始化overlap场景id=2269
2025-07-28 22:17:07,040 - INFO - 方案 #1: 为字幕#2414选择初始化overlap场景id=2271
2025-07-28 22:17:07,040 - INFO - 方案 #1: 初始选择后，当前总时长=4.72秒
2025-07-28 22:17:07,040 - INFO - 方案 #1: 额外between选择后，当前总时长=4.72秒
2025-07-28 22:17:07,040 - INFO - 方案 #1: 场景总时长(4.72秒)小于音频时长(6.73秒)，需要延伸填充
2025-07-28 22:17:07,040 - INFO - 方案 #1: 最后一个场景ID: 2271
2025-07-28 22:17:07,040 - INFO - 方案 #1: 找到最后一个场景在原始列表中的索引: 2270
2025-07-28 22:17:07,040 - INFO - 方案 #1: 需要填充时长: 2.02秒
2025-07-28 22:17:07,040 - INFO - 方案 #1: 追加场景 scene_id=2272 (完整时长 1.80秒)
2025-07-28 22:17:07,040 - INFO - 方案 #1: 追加场景 scene_id=2273 (裁剪至 0.22秒)
2025-07-28 22:17:07,040 - INFO - 方案 #1: 成功填充至目标时长
2025-07-28 22:17:07,040 - INFO - 方案 #1 调整/填充后最终总时长: 6.73秒
2025-07-28 22:17:07,040 - INFO - 方案 #1 添加到方案列表
2025-07-28 22:17:07,040 - INFO - 开始生成方案 #2
2025-07-28 22:17:07,040 - WARNING - 方案 #2: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:17:07,040 - INFO - 开始生成方案 #3
2025-07-28 22:17:07,040 - WARNING - 方案 #3: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:17:07,040 - INFO - 开始生成方案 #4
2025-07-28 22:17:07,040 - WARNING - 方案 #4: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:17:07,040 - INFO - 开始生成方案 #5
2025-07-28 22:17:07,040 - WARNING - 方案 #5: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:17:07,040 - INFO - 开始生成方案 #6
2025-07-28 22:17:07,040 - WARNING - 方案 #6: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:17:07,040 - INFO - ========== 字幕 #50 的 1 套有效场景方案生成完成 ==========
2025-07-28 22:17:07,040 - INFO - 
----- 处理字幕 #50 的方案 #1 -----
2025-07-28 22:17:07,040 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\50_1.mp4
2025-07-28 22:17:07,041 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp8hc9fntv
2025-07-28 22:17:07,041 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2269.mp4 (确认存在: True)
2025-07-28 22:17:07,041 - INFO - 添加场景ID=2269，时长=1.96秒，累计时长=1.96秒
2025-07-28 22:17:07,041 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2271.mp4 (确认存在: True)
2025-07-28 22:17:07,041 - INFO - 添加场景ID=2271，时长=2.76秒，累计时长=4.72秒
2025-07-28 22:17:07,041 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2272.mp4 (确认存在: True)
2025-07-28 22:17:07,041 - INFO - 添加场景ID=2272，时长=1.80秒，累计时长=6.52秒
2025-07-28 22:17:07,041 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2273.mp4 (确认存在: True)
2025-07-28 22:17:07,041 - INFO - 添加场景ID=2273，时长=1.40秒，累计时长=7.92秒
2025-07-28 22:17:07,041 - INFO - 准备合并 4 个场景文件，总时长约 7.92秒
2025-07-28 22:17:07,041 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2269.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2271.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2272.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2273.mp4'

2025-07-28 22:17:07,042 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp8hc9fntv\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp8hc9fntv\temp_combined.mp4
2025-07-28 22:17:07,216 - INFO - 合并后的视频时长: 8.01秒，目标音频时长: 6.73秒
2025-07-28 22:17:07,216 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp8hc9fntv\temp_combined.mp4 -ss 0 -to 6.734 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\50_1.mp4
2025-07-28 22:17:07,580 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 22:17:07,580 - INFO - 目标音频时长: 6.73秒
2025-07-28 22:17:07,580 - INFO - 实际视频时长: 6.78秒
2025-07-28 22:17:07,580 - INFO - 时长差异: 0.05秒 (0.73%)
2025-07-28 22:17:07,580 - INFO - ==========================================
2025-07-28 22:17:07,580 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 22:17:07,580 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\50_1.mp4
2025-07-28 22:17:07,581 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp8hc9fntv
2025-07-28 22:17:07,632 - INFO - 方案 #1 处理完成:
2025-07-28 22:17:07,632 - INFO -   - 音频时长: 6.73秒
2025-07-28 22:17:07,632 - INFO -   - 视频时长: 6.78秒
2025-07-28 22:17:07,632 - INFO -   - 时长差异: 0.05秒 (0.73%)
2025-07-28 22:17:07,633 - INFO - 
字幕 #50 处理完成，成功生成 1/1 套方案
2025-07-28 22:17:07,633 - INFO - 生成的视频文件:
2025-07-28 22:17:07,633 - INFO -   1. F:/github/aicut_auto/newcut_ai\50_1.mp4
2025-07-28 22:17:07,633 - INFO - ========== 字幕 #50 处理结束 ==========

