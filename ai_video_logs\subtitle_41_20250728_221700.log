2025-07-28 22:17:00,202 - INFO - ========== 字幕 #41 处理开始 ==========
2025-07-28 22:17:00,202 - INFO - 字幕内容: 紧接着，老祖又找来第二个证人，当场指认与小人私通的正是侯府二小姐！
2025-07-28 22:17:00,202 - INFO - 字幕序号: [1871, 1878]
2025-07-28 22:17:00,202 - INFO - 音频文件详情:
2025-07-28 22:17:00,202 - INFO -   - 路径: output\41.wav
2025-07-28 22:17:00,202 - INFO -   - 时长: 6.97秒
2025-07-28 22:17:00,202 - INFO -   - 验证音频时长: 6.97秒
2025-07-28 22:17:00,202 - INFO - 字幕时间戳信息:
2025-07-28 22:17:00,203 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-28 22:17:00,203 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-28 22:17:00,203 - INFO -   - 根据生成的音频时长(6.97秒)已调整字幕时间戳
2025-07-28 22:17:00,203 - INFO - ========== 开始为字幕 #41 生成 6 套场景方案 ==========
2025-07-28 22:17:00,203 - INFO - 开始查找字幕序号 [1871, 1878] 对应的场景，共有 2710 个场景可选
2025-07-28 22:17:00,203 - INFO - 找到related_overlap场景: scene_id=1719, 字幕#1871
2025-07-28 22:17:00,203 - INFO - 找到related_overlap场景: scene_id=1720, 字幕#1871
2025-07-28 22:17:00,203 - INFO - 找到related_overlap场景: scene_id=1730, 字幕#1878
2025-07-28 22:17:00,204 - INFO - 找到related_between场景: scene_id=1731, 字幕#1878
2025-07-28 22:17:00,204 - INFO - 找到related_between场景: scene_id=1732, 字幕#1878
2025-07-28 22:17:00,204 - INFO - 找到related_between场景: scene_id=1733, 字幕#1878
2025-07-28 22:17:00,204 - INFO - 字幕 #1871 找到 2 个overlap场景, 0 个between场景
2025-07-28 22:17:00,204 - INFO - 字幕 #1878 找到 1 个overlap场景, 3 个between场景
2025-07-28 22:17:00,204 - INFO - 共收集 3 个未使用的overlap场景和 3 个未使用的between场景
2025-07-28 22:17:00,204 - INFO - 开始生成方案 #1
2025-07-28 22:17:00,204 - INFO - 方案 #1: 为字幕#1871选择初始化overlap场景id=1719
2025-07-28 22:17:00,204 - INFO - 方案 #1: 为字幕#1878选择初始化overlap场景id=1730
2025-07-28 22:17:00,204 - INFO - 方案 #1: 初始选择后，当前总时长=3.20秒
2025-07-28 22:17:00,204 - INFO - 方案 #1: 额外添加overlap场景id=1720, 当前总时长=4.56秒
2025-07-28 22:17:00,204 - INFO - 方案 #1: 额外between选择后，当前总时长=4.56秒
2025-07-28 22:17:00,204 - INFO - 方案 #1: 额外添加between场景id=1732, 当前总时长=5.68秒
2025-07-28 22:17:00,204 - INFO - 方案 #1: 额外添加between场景id=1731, 当前总时长=6.40秒
2025-07-28 22:17:00,204 - INFO - 方案 #1: 额外添加between场景id=1733, 当前总时长=7.16秒
2025-07-28 22:17:00,204 - INFO - 方案 #1: 场景总时长(7.16秒)大于音频时长(6.97秒)，需要裁剪
2025-07-28 22:17:00,204 - INFO - 调整前总时长: 7.16秒, 目标时长: 6.97秒
2025-07-28 22:17:00,204 - INFO - 需要裁剪 0.18秒
2025-07-28 22:17:00,204 - INFO - 裁剪最长场景ID=1730：从1.72秒裁剪至1.54秒
2025-07-28 22:17:00,204 - INFO - 调整后总时长: 6.98秒，与目标时长差异: 0.00秒
2025-07-28 22:17:00,204 - INFO - 方案 #1 调整/填充后最终总时长: 6.97秒
2025-07-28 22:17:00,204 - INFO - 方案 #1 添加到方案列表
2025-07-28 22:17:00,204 - INFO - 开始生成方案 #2
2025-07-28 22:17:00,204 - WARNING - 方案 #2: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:17:00,204 - INFO - 开始生成方案 #3
2025-07-28 22:17:00,204 - WARNING - 方案 #3: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:17:00,204 - INFO - 开始生成方案 #4
2025-07-28 22:17:00,204 - WARNING - 方案 #4: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:17:00,204 - INFO - 开始生成方案 #5
2025-07-28 22:17:00,204 - WARNING - 方案 #5: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:17:00,204 - INFO - 开始生成方案 #6
2025-07-28 22:17:00,204 - WARNING - 方案 #6: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:17:00,204 - INFO - ========== 字幕 #41 的 1 套有效场景方案生成完成 ==========
2025-07-28 22:17:00,204 - INFO - 
----- 处理字幕 #41 的方案 #1 -----
2025-07-28 22:17:00,204 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\41_1.mp4
2025-07-28 22:17:00,204 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpazoa10vi
2025-07-28 22:17:00,205 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1719.mp4 (确认存在: True)
2025-07-28 22:17:00,205 - INFO - 添加场景ID=1719，时长=1.48秒，累计时长=1.48秒
2025-07-28 22:17:00,205 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1730.mp4 (确认存在: True)
2025-07-28 22:17:00,205 - INFO - 添加场景ID=1730，时长=1.72秒，累计时长=3.20秒
2025-07-28 22:17:00,205 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1720.mp4 (确认存在: True)
2025-07-28 22:17:00,205 - INFO - 添加场景ID=1720，时长=1.36秒，累计时长=4.56秒
2025-07-28 22:17:00,205 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1732.mp4 (确认存在: True)
2025-07-28 22:17:00,205 - INFO - 添加场景ID=1732，时长=1.12秒，累计时长=5.68秒
2025-07-28 22:17:00,205 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1731.mp4 (确认存在: True)
2025-07-28 22:17:00,205 - INFO - 添加场景ID=1731，时长=0.72秒，累计时长=6.40秒
2025-07-28 22:17:00,205 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1733.mp4 (确认存在: True)
2025-07-28 22:17:00,205 - INFO - 添加场景ID=1733，时长=0.76秒，累计时长=7.16秒
2025-07-28 22:17:00,205 - INFO - 准备合并 6 个场景文件，总时长约 7.16秒
2025-07-28 22:17:00,205 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1719.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1730.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1720.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1732.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1731.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1733.mp4'

2025-07-28 22:17:00,205 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpazoa10vi\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpazoa10vi\temp_combined.mp4
2025-07-28 22:17:00,411 - INFO - 合并后的视频时长: 7.30秒，目标音频时长: 6.97秒
2025-07-28 22:17:00,411 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpazoa10vi\temp_combined.mp4 -ss 0 -to 6.975 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\41_1.mp4
2025-07-28 22:17:00,816 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 22:17:00,816 - INFO - 目标音频时长: 6.97秒
2025-07-28 22:17:00,816 - INFO - 实际视频时长: 7.02秒
2025-07-28 22:17:00,816 - INFO - 时长差异: 0.05秒 (0.69%)
2025-07-28 22:17:00,816 - INFO - ==========================================
2025-07-28 22:17:00,816 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 22:17:00,816 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\41_1.mp4
2025-07-28 22:17:00,817 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpazoa10vi
2025-07-28 22:17:00,868 - INFO - 方案 #1 处理完成:
2025-07-28 22:17:00,868 - INFO -   - 音频时长: 6.97秒
2025-07-28 22:17:00,868 - INFO -   - 视频时长: 7.02秒
2025-07-28 22:17:00,868 - INFO -   - 时长差异: 0.05秒 (0.69%)
2025-07-28 22:17:00,868 - INFO - 
字幕 #41 处理完成，成功生成 1/1 套方案
2025-07-28 22:17:00,868 - INFO - 生成的视频文件:
2025-07-28 22:17:00,868 - INFO -   1. F:/github/aicut_auto/newcut_ai\41_1.mp4
2025-07-28 22:17:00,868 - INFO - ========== 字幕 #41 处理结束 ==========

