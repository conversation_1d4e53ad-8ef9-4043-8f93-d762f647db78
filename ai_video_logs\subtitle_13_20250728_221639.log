2025-07-28 22:16:39,889 - INFO - ========== 字幕 #13 处理开始 ==========
2025-07-28 22:16:39,889 - INFO - 字幕内容: 经此一役，全家终于开始相信，他们听到的心声，似乎并不完全是真的。
2025-07-28 22:16:39,889 - INFO - 字幕序号: [327, 328]
2025-07-28 22:16:39,889 - INFO - 音频文件详情:
2025-07-28 22:16:39,889 - INFO -   - 路径: output\13.wav
2025-07-28 22:16:39,889 - INFO -   - 时长: 4.18秒
2025-07-28 22:16:39,889 - INFO -   - 验证音频时长: 4.18秒
2025-07-28 22:16:39,889 - INFO - 字幕时间戳信息:
2025-07-28 22:16:39,889 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-28 22:16:39,889 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-28 22:16:39,889 - INFO -   - 根据生成的音频时长(4.18秒)已调整字幕时间戳
2025-07-28 22:16:39,889 - INFO - ========== 开始为字幕 #13 生成 6 套场景方案 ==========
2025-07-28 22:16:39,889 - INFO - 开始查找字幕序号 [327, 328] 对应的场景，共有 2710 个场景可选
2025-07-28 22:16:39,889 - INFO - 找到related_overlap场景: scene_id=363, 字幕#327
2025-07-28 22:16:39,891 - INFO - 字幕 #327 找到 1 个overlap场景, 0 个between场景
2025-07-28 22:16:39,891 - INFO - 字幕 #328 找到 0 个overlap场景, 0 个between场景
2025-07-28 22:16:39,891 - WARNING - 字幕 #328 没有找到任何匹配场景!
2025-07-28 22:16:39,891 - INFO - 共收集 1 个未使用的overlap场景和 0 个未使用的between场景
2025-07-28 22:16:39,891 - INFO - 开始生成方案 #1
2025-07-28 22:16:39,891 - INFO - 方案 #1: 为字幕#327选择初始化overlap场景id=363
2025-07-28 22:16:39,891 - INFO - 方案 #1: 初始选择后，当前总时长=9.72秒
2025-07-28 22:16:39,891 - INFO - 方案 #1: 额外between选择后，当前总时长=9.72秒
2025-07-28 22:16:39,891 - INFO - 方案 #1: 场景总时长(9.72秒)大于音频时长(4.18秒)，需要裁剪
2025-07-28 22:16:39,891 - INFO - 调整前总时长: 9.72秒, 目标时长: 4.18秒
2025-07-28 22:16:39,891 - INFO - 需要裁剪 5.54秒
2025-07-28 22:16:39,891 - INFO - 裁剪最长场景ID=363：从9.72秒裁剪至4.18秒
2025-07-28 22:16:39,891 - INFO - 调整后总时长: 4.18秒，与目标时长差异: 0.00秒
2025-07-28 22:16:39,891 - INFO - 方案 #1 调整/填充后最终总时长: 4.18秒
2025-07-28 22:16:39,891 - INFO - 方案 #1 添加到方案列表
2025-07-28 22:16:39,891 - INFO - 开始生成方案 #2
2025-07-28 22:16:39,891 - WARNING - 方案 #2: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:39,891 - INFO - 开始生成方案 #3
2025-07-28 22:16:39,891 - WARNING - 方案 #3: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:39,891 - INFO - 开始生成方案 #4
2025-07-28 22:16:39,891 - WARNING - 方案 #4: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:39,891 - INFO - 开始生成方案 #5
2025-07-28 22:16:39,891 - WARNING - 方案 #5: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:39,891 - INFO - 开始生成方案 #6
2025-07-28 22:16:39,891 - WARNING - 方案 #6: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:39,891 - INFO - ========== 字幕 #13 的 1 套有效场景方案生成完成 ==========
2025-07-28 22:16:39,891 - INFO - 
----- 处理字幕 #13 的方案 #1 -----
2025-07-28 22:16:39,891 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\13_1.mp4
2025-07-28 22:16:39,892 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpjt2d0ioj
2025-07-28 22:16:39,892 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\363.mp4 (确认存在: True)
2025-07-28 22:16:39,892 - INFO - 添加场景ID=363，时长=9.72秒，累计时长=9.72秒
2025-07-28 22:16:39,892 - INFO - 场景总时长(9.72秒)已达到音频时长(4.18秒)的1.5倍，停止添加场景
2025-07-28 22:16:39,892 - INFO - 准备合并 1 个场景文件，总时长约 9.72秒
2025-07-28 22:16:39,892 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/363.mp4'

2025-07-28 22:16:39,893 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpjt2d0ioj\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpjt2d0ioj\temp_combined.mp4
2025-07-28 22:16:40,008 - INFO - 合并后的视频时长: 9.74秒，目标音频时长: 4.18秒
2025-07-28 22:16:40,008 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpjt2d0ioj\temp_combined.mp4 -ss 0 -to 4.184 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\13_1.mp4
2025-07-28 22:16:40,286 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 22:16:40,286 - INFO - 目标音频时长: 4.18秒
2025-07-28 22:16:40,286 - INFO - 实际视频时长: 4.22秒
2025-07-28 22:16:40,286 - INFO - 时长差异: 0.04秒 (0.93%)
2025-07-28 22:16:40,286 - INFO - ==========================================
2025-07-28 22:16:40,286 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 22:16:40,286 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\13_1.mp4
2025-07-28 22:16:40,287 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpjt2d0ioj
2025-07-28 22:16:40,335 - INFO - 方案 #1 处理完成:
2025-07-28 22:16:40,335 - INFO -   - 音频时长: 4.18秒
2025-07-28 22:16:40,335 - INFO -   - 视频时长: 4.22秒
2025-07-28 22:16:40,335 - INFO -   - 时长差异: 0.04秒 (0.93%)
2025-07-28 22:16:40,335 - INFO - 
字幕 #13 处理完成，成功生成 1/1 套方案
2025-07-28 22:16:40,335 - INFO - 生成的视频文件:
2025-07-28 22:16:40,335 - INFO -   1. F:/github/aicut_auto/newcut_ai\13_1.mp4
2025-07-28 22:16:40,335 - INFO - ========== 字幕 #13 处理结束 ==========

