2025-07-28 22:16:40,926 - INFO - ========== 字幕 #15 处理开始 ==========
2025-07-28 22:16:40,926 - INFO - 字幕内容: 心机女听闻此事，生怕自己养女身份暴露，暗中发誓绝不允许宴会顺利举行。
2025-07-28 22:16:40,926 - INFO - 字幕序号: [376, 377]
2025-07-28 22:16:40,926 - INFO - 音频文件详情:
2025-07-28 22:16:40,926 - INFO -   - 路径: output\15.wav
2025-07-28 22:16:40,926 - INFO -   - 时长: 5.90秒
2025-07-28 22:16:40,926 - INFO -   - 验证音频时长: 5.90秒
2025-07-28 22:16:40,926 - INFO - 字幕时间戳信息:
2025-07-28 22:16:40,936 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-28 22:16:40,936 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-28 22:16:40,936 - INFO -   - 根据生成的音频时长(5.90秒)已调整字幕时间戳
2025-07-28 22:16:40,936 - INFO - ========== 开始为字幕 #15 生成 6 套场景方案 ==========
2025-07-28 22:16:40,936 - INFO - 开始查找字幕序号 [376, 377] 对应的场景，共有 2710 个场景可选
2025-07-28 22:16:40,937 - INFO - 找到related_overlap场景: scene_id=405, 字幕#376
2025-07-28 22:16:40,937 - INFO - 找到related_overlap场景: scene_id=406, 字幕#376
2025-07-28 22:16:40,937 - INFO - 找到related_between场景: scene_id=407, 字幕#377
2025-07-28 22:16:40,938 - INFO - 字幕 #376 找到 2 个overlap场景, 0 个between场景
2025-07-28 22:16:40,938 - INFO - 字幕 #377 找到 0 个overlap场景, 1 个between场景
2025-07-28 22:16:40,938 - INFO - 共收集 2 个未使用的overlap场景和 1 个未使用的between场景
2025-07-28 22:16:40,938 - INFO - 开始生成方案 #1
2025-07-28 22:16:40,938 - INFO - 方案 #1: 为字幕#376选择初始化overlap场景id=406
2025-07-28 22:16:40,938 - INFO - 方案 #1: 初始选择后，当前总时长=5.80秒
2025-07-28 22:16:40,938 - INFO - 方案 #1: 额外添加overlap场景id=405, 当前总时长=7.88秒
2025-07-28 22:16:40,938 - INFO - 方案 #1: 为字幕#377选择初始化between场景id=407
2025-07-28 22:16:40,938 - INFO - 方案 #1: 额外between选择后，当前总时长=9.20秒
2025-07-28 22:16:40,938 - INFO - 方案 #1: 场景总时长(9.20秒)大于音频时长(5.90秒)，需要裁剪
2025-07-28 22:16:40,938 - INFO - 调整前总时长: 9.20秒, 目标时长: 5.90秒
2025-07-28 22:16:40,938 - INFO - 需要裁剪 3.30秒
2025-07-28 22:16:40,938 - INFO - 裁剪最长场景ID=406：从5.80秒裁剪至2.50秒
2025-07-28 22:16:40,938 - INFO - 调整后总时长: 5.90秒，与目标时长差异: 0.00秒
2025-07-28 22:16:40,938 - INFO - 方案 #1 调整/填充后最终总时长: 5.90秒
2025-07-28 22:16:40,938 - INFO - 方案 #1 添加到方案列表
2025-07-28 22:16:40,938 - INFO - 开始生成方案 #2
2025-07-28 22:16:40,938 - WARNING - 方案 #2: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:40,938 - INFO - 开始生成方案 #3
2025-07-28 22:16:40,938 - WARNING - 方案 #3: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:40,938 - INFO - 开始生成方案 #4
2025-07-28 22:16:40,938 - WARNING - 方案 #4: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:40,938 - INFO - 开始生成方案 #5
2025-07-28 22:16:40,938 - WARNING - 方案 #5: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:40,938 - INFO - 开始生成方案 #6
2025-07-28 22:16:40,938 - WARNING - 方案 #6: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:40,938 - INFO - ========== 字幕 #15 的 1 套有效场景方案生成完成 ==========
2025-07-28 22:16:40,938 - INFO - 
----- 处理字幕 #15 的方案 #1 -----
2025-07-28 22:16:40,938 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\15_1.mp4
2025-07-28 22:16:40,938 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpe6wb2r0p
2025-07-28 22:16:40,939 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\406.mp4 (确认存在: True)
2025-07-28 22:16:40,939 - INFO - 添加场景ID=406，时长=5.80秒，累计时长=5.80秒
2025-07-28 22:16:40,939 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\405.mp4 (确认存在: True)
2025-07-28 22:16:40,939 - INFO - 添加场景ID=405，时长=2.08秒，累计时长=7.88秒
2025-07-28 22:16:40,939 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\407.mp4 (确认存在: True)
2025-07-28 22:16:40,939 - INFO - 添加场景ID=407，时长=1.32秒，累计时长=9.20秒
2025-07-28 22:16:40,939 - INFO - 场景总时长(9.20秒)已达到音频时长(5.90秒)的1.5倍，停止添加场景
2025-07-28 22:16:40,939 - INFO - 准备合并 3 个场景文件，总时长约 9.20秒
2025-07-28 22:16:40,939 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/406.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/405.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/407.mp4'

2025-07-28 22:16:40,939 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpe6wb2r0p\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpe6wb2r0p\temp_combined.mp4
2025-07-28 22:16:41,094 - INFO - 合并后的视频时长: 9.27秒，目标音频时长: 5.90秒
2025-07-28 22:16:41,094 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpe6wb2r0p\temp_combined.mp4 -ss 0 -to 5.901 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\15_1.mp4
2025-07-28 22:16:41,454 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 22:16:41,454 - INFO - 目标音频时长: 5.90秒
2025-07-28 22:16:41,454 - INFO - 实际视频时长: 5.94秒
2025-07-28 22:16:41,454 - INFO - 时长差异: 0.04秒 (0.71%)
2025-07-28 22:16:41,454 - INFO - ==========================================
2025-07-28 22:16:41,454 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 22:16:41,454 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\15_1.mp4
2025-07-28 22:16:41,456 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpe6wb2r0p
2025-07-28 22:16:41,500 - INFO - 方案 #1 处理完成:
2025-07-28 22:16:41,500 - INFO -   - 音频时长: 5.90秒
2025-07-28 22:16:41,500 - INFO -   - 视频时长: 5.94秒
2025-07-28 22:16:41,500 - INFO -   - 时长差异: 0.04秒 (0.71%)
2025-07-28 22:16:41,500 - INFO - 
字幕 #15 处理完成，成功生成 1/1 套方案
2025-07-28 22:16:41,500 - INFO - 生成的视频文件:
2025-07-28 22:16:41,500 - INFO -   1. F:/github/aicut_auto/newcut_ai\15_1.mp4
2025-07-28 22:16:41,500 - INFO - ========== 字幕 #15 处理结束 ==========

