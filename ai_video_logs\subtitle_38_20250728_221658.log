2025-07-28 22:16:58,561 - INFO - ========== 字幕 #38 处理开始 ==========
2025-07-28 22:16:58,561 - INFO - 字幕内容: 这正是原著中，导致原主名声尽毁、悲惨收场的致命一击。
2025-07-28 22:16:58,561 - INFO - 字幕序号: [1712, 1714]
2025-07-28 22:16:58,562 - INFO - 音频文件详情:
2025-07-28 22:16:58,562 - INFO -   - 路径: output\38.wav
2025-07-28 22:16:58,562 - INFO -   - 时长: 6.02秒
2025-07-28 22:16:58,562 - INFO -   - 验证音频时长: 6.02秒
2025-07-28 22:16:58,562 - INFO - 字幕时间戳信息:
2025-07-28 22:16:58,562 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-28 22:16:58,572 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-28 22:16:58,572 - INFO -   - 根据生成的音频时长(6.02秒)已调整字幕时间戳
2025-07-28 22:16:58,572 - INFO - ========== 开始为字幕 #38 生成 6 套场景方案 ==========
2025-07-28 22:16:58,572 - INFO - 开始查找字幕序号 [1712, 1714] 对应的场景，共有 2710 个场景可选
2025-07-28 22:16:58,573 - INFO - 找到related_overlap场景: scene_id=1602, 字幕#1712
2025-07-28 22:16:58,573 - INFO - 找到related_overlap场景: scene_id=1603, 字幕#1712
2025-07-28 22:16:58,574 - INFO - 字幕 #1712 找到 2 个overlap场景, 0 个between场景
2025-07-28 22:16:58,574 - INFO - 字幕 #1714 找到 0 个overlap场景, 0 个between场景
2025-07-28 22:16:58,574 - WARNING - 字幕 #1714 没有找到任何匹配场景!
2025-07-28 22:16:58,574 - INFO - 共收集 2 个未使用的overlap场景和 0 个未使用的between场景
2025-07-28 22:16:58,574 - INFO - 开始生成方案 #1
2025-07-28 22:16:58,574 - INFO - 方案 #1: 为字幕#1712选择初始化overlap场景id=1602
2025-07-28 22:16:58,574 - INFO - 方案 #1: 初始选择后，当前总时长=1.56秒
2025-07-28 22:16:58,574 - INFO - 方案 #1: 额外添加overlap场景id=1603, 当前总时长=6.92秒
2025-07-28 22:16:58,574 - INFO - 方案 #1: 额外between选择后，当前总时长=6.92秒
2025-07-28 22:16:58,574 - INFO - 方案 #1: 场景总时长(6.92秒)大于音频时长(6.02秒)，需要裁剪
2025-07-28 22:16:58,574 - INFO - 调整前总时长: 6.92秒, 目标时长: 6.02秒
2025-07-28 22:16:58,574 - INFO - 需要裁剪 0.90秒
2025-07-28 22:16:58,574 - INFO - 裁剪最长场景ID=1603：从5.36秒裁剪至4.46秒
2025-07-28 22:16:58,574 - INFO - 调整后总时长: 6.02秒，与目标时长差异: 0.00秒
2025-07-28 22:16:58,574 - INFO - 方案 #1 调整/填充后最终总时长: 6.02秒
2025-07-28 22:16:58,574 - INFO - 方案 #1 添加到方案列表
2025-07-28 22:16:58,574 - INFO - 开始生成方案 #2
2025-07-28 22:16:58,574 - WARNING - 方案 #2: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:58,574 - INFO - 开始生成方案 #3
2025-07-28 22:16:58,574 - WARNING - 方案 #3: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:58,574 - INFO - 开始生成方案 #4
2025-07-28 22:16:58,574 - WARNING - 方案 #4: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:58,574 - INFO - 开始生成方案 #5
2025-07-28 22:16:58,574 - WARNING - 方案 #5: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:58,574 - INFO - 开始生成方案 #6
2025-07-28 22:16:58,574 - WARNING - 方案 #6: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:58,574 - INFO - ========== 字幕 #38 的 1 套有效场景方案生成完成 ==========
2025-07-28 22:16:58,574 - INFO - 
----- 处理字幕 #38 的方案 #1 -----
2025-07-28 22:16:58,574 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\38_1.mp4
2025-07-28 22:16:58,574 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpbqgv27n6
2025-07-28 22:16:58,575 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1602.mp4 (确认存在: True)
2025-07-28 22:16:58,575 - INFO - 添加场景ID=1602，时长=1.56秒，累计时长=1.56秒
2025-07-28 22:16:58,575 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1603.mp4 (确认存在: True)
2025-07-28 22:16:58,575 - INFO - 添加场景ID=1603，时长=5.36秒，累计时长=6.92秒
2025-07-28 22:16:58,575 - INFO - 准备合并 2 个场景文件，总时长约 6.92秒
2025-07-28 22:16:58,575 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1602.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1603.mp4'

2025-07-28 22:16:58,575 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpbqgv27n6\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpbqgv27n6\temp_combined.mp4
2025-07-28 22:16:58,714 - INFO - 合并后的视频时长: 6.97秒，目标音频时长: 6.02秒
2025-07-28 22:16:58,714 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpbqgv27n6\temp_combined.mp4 -ss 0 -to 6.019 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\38_1.mp4
2025-07-28 22:16:59,053 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 22:16:59,053 - INFO - 目标音频时长: 6.02秒
2025-07-28 22:16:59,053 - INFO - 实际视频时长: 6.06秒
2025-07-28 22:16:59,053 - INFO - 时长差异: 0.04秒 (0.73%)
2025-07-28 22:16:59,053 - INFO - ==========================================
2025-07-28 22:16:59,053 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 22:16:59,053 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\38_1.mp4
2025-07-28 22:16:59,054 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpbqgv27n6
2025-07-28 22:16:59,097 - INFO - 方案 #1 处理完成:
2025-07-28 22:16:59,097 - INFO -   - 音频时长: 6.02秒
2025-07-28 22:16:59,097 - INFO -   - 视频时长: 6.06秒
2025-07-28 22:16:59,097 - INFO -   - 时长差异: 0.04秒 (0.73%)
2025-07-28 22:16:59,097 - INFO - 
字幕 #38 处理完成，成功生成 1/1 套方案
2025-07-28 22:16:59,097 - INFO - 生成的视频文件:
2025-07-28 22:16:59,097 - INFO -   1. F:/github/aicut_auto/newcut_ai\38_1.mp4
2025-07-28 22:16:59,097 - INFO - ========== 字幕 #38 处理结束 ==========

