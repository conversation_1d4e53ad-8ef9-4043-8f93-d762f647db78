2025-07-28 22:17:16,510 - INFO - ========== 字幕 #62 处理开始 ==========
2025-07-28 22:17:16,510 - INFO - 字幕内容: 而我们的修真老祖，则因为那些大胆奔放的心声，意外吸引了能听到她心声的国公世子。
2025-07-28 22:17:16,510 - INFO - 字幕序号: [2134, 2138]
2025-07-28 22:17:16,510 - INFO - 音频文件详情:
2025-07-28 22:17:16,510 - INFO -   - 路径: output\62.wav
2025-07-28 22:17:16,510 - INFO -   - 时长: 5.47秒
2025-07-28 22:17:16,510 - INFO -   - 验证音频时长: 5.47秒
2025-07-28 22:17:16,511 - INFO - 字幕时间戳信息:
2025-07-28 22:17:16,511 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-28 22:17:16,511 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-28 22:17:16,511 - INFO -   - 根据生成的音频时长(5.47秒)已调整字幕时间戳
2025-07-28 22:17:16,511 - INFO - ========== 开始为字幕 #62 生成 6 套场景方案 ==========
2025-07-28 22:17:16,511 - INFO - 开始查找字幕序号 [2134, 2138] 对应的场景，共有 2710 个场景可选
2025-07-28 22:17:16,511 - INFO - 找到related_overlap场景: scene_id=2003, 字幕#2134
2025-07-28 22:17:16,511 - INFO - 找到related_overlap场景: scene_id=2006, 字幕#2138
2025-07-28 22:17:16,511 - INFO - 找到related_overlap场景: scene_id=2007, 字幕#2138
2025-07-28 22:17:16,512 - INFO - 找到related_between场景: scene_id=2002, 字幕#2134
2025-07-28 22:17:16,512 - INFO - 字幕 #2134 找到 1 个overlap场景, 1 个between场景
2025-07-28 22:17:16,512 - INFO - 字幕 #2138 找到 2 个overlap场景, 0 个between场景
2025-07-28 22:17:16,512 - INFO - 共收集 3 个未使用的overlap场景和 1 个未使用的between场景
2025-07-28 22:17:16,512 - INFO - 开始生成方案 #1
2025-07-28 22:17:16,512 - INFO - 方案 #1: 为字幕#2134选择初始化overlap场景id=2003
2025-07-28 22:17:16,512 - INFO - 方案 #1: 为字幕#2138选择初始化overlap场景id=2006
2025-07-28 22:17:16,512 - INFO - 方案 #1: 初始选择后，当前总时长=4.92秒
2025-07-28 22:17:16,512 - INFO - 方案 #1: 额外添加overlap场景id=2007, 当前总时长=6.72秒
2025-07-28 22:17:16,512 - INFO - 方案 #1: 额外between选择后，当前总时长=6.72秒
2025-07-28 22:17:16,512 - INFO - 方案 #1: 场景总时长(6.72秒)大于音频时长(5.47秒)，需要裁剪
2025-07-28 22:17:16,512 - INFO - 调整前总时长: 6.72秒, 目标时长: 5.47秒
2025-07-28 22:17:16,512 - INFO - 需要裁剪 1.25秒
2025-07-28 22:17:16,512 - INFO - 裁剪最长场景ID=2003：从2.52秒裁剪至1.27秒
2025-07-28 22:17:16,512 - INFO - 调整后总时长: 5.47秒，与目标时长差异: 0.00秒
2025-07-28 22:17:16,512 - INFO - 方案 #1 调整/填充后最终总时长: 5.47秒
2025-07-28 22:17:16,512 - INFO - 方案 #1 添加到方案列表
2025-07-28 22:17:16,512 - INFO - 开始生成方案 #2
2025-07-28 22:17:16,512 - INFO - 方案 #2: 初始选择后，当前总时长=0.00秒
2025-07-28 22:17:16,512 - INFO - 方案 #2: 为字幕#2134选择初始化between场景id=2002
2025-07-28 22:17:16,512 - INFO - 方案 #2: 额外between选择后，当前总时长=1.48秒
2025-07-28 22:17:16,512 - INFO - 方案 #2: 场景总时长(1.48秒)小于音频时长(5.47秒)，需要延伸填充
2025-07-28 22:17:16,512 - INFO - 方案 #2: 最后一个场景ID: 2002
2025-07-28 22:17:16,513 - INFO - 方案 #2: 找到最后一个场景在原始列表中的索引: 2001
2025-07-28 22:17:16,513 - INFO - 方案 #2: 需要填充时长: 3.99秒
2025-07-28 22:17:16,513 - INFO - 方案 #2: 跳过已使用的场景: scene_id=2003
2025-07-28 22:17:16,513 - INFO - 方案 #2: 追加场景 scene_id=2004 (完整时长 1.20秒)
2025-07-28 22:17:16,513 - INFO - 方案 #2: 追加场景 scene_id=2005 (裁剪至 2.79秒)
2025-07-28 22:17:16,513 - INFO - 方案 #2: 成功填充至目标时长
2025-07-28 22:17:16,513 - INFO - 方案 #2 调整/填充后最终总时长: 5.47秒
2025-07-28 22:17:16,513 - INFO - 方案 #2 添加到方案列表
2025-07-28 22:17:16,513 - INFO - 开始生成方案 #3
2025-07-28 22:17:16,513 - WARNING - 方案 #3: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:17:16,513 - INFO - 开始生成方案 #4
2025-07-28 22:17:16,513 - WARNING - 方案 #4: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:17:16,513 - INFO - 开始生成方案 #5
2025-07-28 22:17:16,513 - WARNING - 方案 #5: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:17:16,513 - INFO - 开始生成方案 #6
2025-07-28 22:17:16,513 - WARNING - 方案 #6: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:17:16,513 - INFO - ========== 字幕 #62 的 2 套有效场景方案生成完成 ==========
2025-07-28 22:17:16,513 - INFO - 
----- 处理字幕 #62 的方案 #1 -----
2025-07-28 22:17:16,513 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\62_1.mp4
2025-07-28 22:17:16,513 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpmnupmhr2
2025-07-28 22:17:16,513 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2003.mp4 (确认存在: True)
2025-07-28 22:17:16,513 - INFO - 添加场景ID=2003，时长=2.52秒，累计时长=2.52秒
2025-07-28 22:17:16,513 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2006.mp4 (确认存在: True)
2025-07-28 22:17:16,513 - INFO - 添加场景ID=2006，时长=2.40秒，累计时长=4.92秒
2025-07-28 22:17:16,513 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2007.mp4 (确认存在: True)
2025-07-28 22:17:16,513 - INFO - 添加场景ID=2007，时长=1.80秒，累计时长=6.72秒
2025-07-28 22:17:16,513 - INFO - 准备合并 3 个场景文件，总时长约 6.72秒
2025-07-28 22:17:16,513 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2003.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2006.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2007.mp4'

2025-07-28 22:17:16,515 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpmnupmhr2\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpmnupmhr2\temp_combined.mp4
2025-07-28 22:17:16,667 - INFO - 合并后的视频时长: 6.79秒，目标音频时长: 5.47秒
2025-07-28 22:17:16,667 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpmnupmhr2\temp_combined.mp4 -ss 0 -to 5.468 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\62_1.mp4
2025-07-28 22:17:17,005 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 22:17:17,005 - INFO - 目标音频时长: 5.47秒
2025-07-28 22:17:17,005 - INFO - 实际视频时长: 5.50秒
2025-07-28 22:17:17,005 - INFO - 时长差异: 0.04秒 (0.64%)
2025-07-28 22:17:17,005 - INFO - ==========================================
2025-07-28 22:17:17,005 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 22:17:17,005 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\62_1.mp4
2025-07-28 22:17:17,005 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpmnupmhr2
2025-07-28 22:17:17,053 - INFO - 方案 #1 处理完成:
2025-07-28 22:17:17,053 - INFO -   - 音频时长: 5.47秒
2025-07-28 22:17:17,053 - INFO -   - 视频时长: 5.50秒
2025-07-28 22:17:17,055 - INFO -   - 时长差异: 0.04秒 (0.64%)
2025-07-28 22:17:17,055 - INFO - 
----- 处理字幕 #62 的方案 #2 -----
2025-07-28 22:17:17,055 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\62_2.mp4
2025-07-28 22:17:17,055 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp9k67sroh
2025-07-28 22:17:17,055 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2002.mp4 (确认存在: True)
2025-07-28 22:17:17,055 - INFO - 添加场景ID=2002，时长=1.48秒，累计时长=1.48秒
2025-07-28 22:17:17,056 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2004.mp4 (确认存在: True)
2025-07-28 22:17:17,056 - INFO - 添加场景ID=2004，时长=1.20秒，累计时长=2.68秒
2025-07-28 22:17:17,056 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2005.mp4 (确认存在: True)
2025-07-28 22:17:17,056 - INFO - 添加场景ID=2005，时长=2.96秒，累计时长=5.64秒
2025-07-28 22:17:17,056 - INFO - 准备合并 3 个场景文件，总时长约 5.64秒
2025-07-28 22:17:17,056 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2002.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2004.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2005.mp4'

2025-07-28 22:17:17,056 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp9k67sroh\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp9k67sroh\temp_combined.mp4
2025-07-28 22:17:17,228 - INFO - 合并后的视频时长: 5.71秒，目标音频时长: 5.47秒
2025-07-28 22:17:17,228 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp9k67sroh\temp_combined.mp4 -ss 0 -to 5.468 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\62_2.mp4
2025-07-28 22:17:17,610 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 22:17:17,610 - INFO - 目标音频时长: 5.47秒
2025-07-28 22:17:17,610 - INFO - 实际视频时长: 5.50秒
2025-07-28 22:17:17,610 - INFO - 时长差异: 0.04秒 (0.64%)
2025-07-28 22:17:17,610 - INFO - ==========================================
2025-07-28 22:17:17,610 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 22:17:17,610 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\62_2.mp4
2025-07-28 22:17:17,611 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp9k67sroh
2025-07-28 22:17:17,664 - INFO - 方案 #2 处理完成:
2025-07-28 22:17:17,664 - INFO -   - 音频时长: 5.47秒
2025-07-28 22:17:17,664 - INFO -   - 视频时长: 5.50秒
2025-07-28 22:17:17,664 - INFO -   - 时长差异: 0.04秒 (0.64%)
2025-07-28 22:17:17,664 - INFO - 
字幕 #62 处理完成，成功生成 2/2 套方案
2025-07-28 22:17:17,664 - INFO - 生成的视频文件:
2025-07-28 22:17:17,664 - INFO -   1. F:/github/aicut_auto/newcut_ai\62_1.mp4
2025-07-28 22:17:17,664 - INFO -   2. F:/github/aicut_auto/newcut_ai\62_2.mp4
2025-07-28 22:17:17,664 - INFO - ========== 字幕 #62 处理结束 ==========

