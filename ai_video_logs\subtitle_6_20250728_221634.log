2025-07-28 22:16:34,961 - INFO - ========== 字幕 #6 处理开始 ==========
2025-07-28 22:16:34,962 - INFO - 字幕内容: 老祖嘴角一扬，立刻发动金手指，心声中的“毒药”瞬间变成了“泻药”！
2025-07-28 22:16:34,962 - INFO - 字幕序号: [129, 127]
2025-07-28 22:16:34,962 - INFO - 音频文件详情:
2025-07-28 22:16:34,962 - INFO -   - 路径: output\6.wav
2025-07-28 22:16:34,962 - INFO -   - 时长: 5.78秒
2025-07-28 22:16:34,962 - INFO -   - 验证音频时长: 5.78秒
2025-07-28 22:16:34,962 - INFO - 字幕时间戳信息:
2025-07-28 22:16:34,962 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-28 22:16:34,962 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-28 22:16:34,962 - INFO -   - 根据生成的音频时长(5.78秒)已调整字幕时间戳
2025-07-28 22:16:34,962 - INFO - ========== 开始为字幕 #6 生成 6 套场景方案 ==========
2025-07-28 22:16:34,962 - INFO - 开始查找字幕序号 [129, 127] 对应的场景，共有 2710 个场景可选
2025-07-28 22:16:34,962 - INFO - 找到related_overlap场景: scene_id=134, 字幕#127
2025-07-28 22:16:34,962 - INFO - 找到related_overlap场景: scene_id=136, 字幕#129
2025-07-28 22:16:34,963 - INFO - 找到related_between场景: scene_id=135, 字幕#129
2025-07-28 22:16:34,963 - INFO - 找到related_between场景: scene_id=137, 字幕#129
2025-07-28 22:16:34,963 - INFO - 找到related_between场景: scene_id=138, 字幕#129
2025-07-28 22:16:34,963 - INFO - 找到related_between场景: scene_id=139, 字幕#129
2025-07-28 22:16:34,963 - INFO - 字幕 #129 找到 1 个overlap场景, 4 个between场景
2025-07-28 22:16:34,964 - INFO - 字幕 #127 找到 1 个overlap场景, 0 个between场景
2025-07-28 22:16:34,964 - INFO - 共收集 1 个未使用的overlap场景和 4 个未使用的between场景
2025-07-28 22:16:34,964 - INFO - 开始生成方案 #1
2025-07-28 22:16:34,964 - INFO - 方案 #1: 为字幕#129选择初始化overlap场景id=136
2025-07-28 22:16:34,964 - INFO - 方案 #1: 初始选择后，当前总时长=2.32秒
2025-07-28 22:16:34,964 - INFO - 方案 #1: 额外between选择后，当前总时长=2.32秒
2025-07-28 22:16:34,964 - INFO - 方案 #1: 额外添加between场景id=139, 当前总时长=4.00秒
2025-07-28 22:16:34,964 - INFO - 方案 #1: 额外添加between场景id=135, 当前总时长=5.12秒
2025-07-28 22:16:34,964 - INFO - 方案 #1: 额外添加between场景id=138, 当前总时长=5.68秒
2025-07-28 22:16:34,964 - INFO - 方案 #1: 额外添加between场景id=137, 当前总时长=6.48秒
2025-07-28 22:16:34,964 - INFO - 方案 #1: 场景总时长(6.48秒)大于音频时长(5.78秒)，需要裁剪
2025-07-28 22:16:34,964 - INFO - 调整前总时长: 6.48秒, 目标时长: 5.78秒
2025-07-28 22:16:34,964 - INFO - 需要裁剪 0.70秒
2025-07-28 22:16:34,964 - INFO - 裁剪最长场景ID=136：从2.32秒裁剪至1.62秒
2025-07-28 22:16:34,964 - INFO - 调整后总时长: 5.78秒，与目标时长差异: 0.00秒
2025-07-28 22:16:34,964 - INFO - 方案 #1 调整/填充后最终总时长: 5.78秒
2025-07-28 22:16:34,964 - INFO - 方案 #1 添加到方案列表
2025-07-28 22:16:34,964 - INFO - 开始生成方案 #2
2025-07-28 22:16:34,964 - WARNING - 方案 #2: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:34,964 - INFO - 开始生成方案 #3
2025-07-28 22:16:34,964 - WARNING - 方案 #3: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:34,964 - INFO - 开始生成方案 #4
2025-07-28 22:16:34,964 - WARNING - 方案 #4: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:34,964 - INFO - 开始生成方案 #5
2025-07-28 22:16:34,964 - WARNING - 方案 #5: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:34,964 - INFO - 开始生成方案 #6
2025-07-28 22:16:34,964 - WARNING - 方案 #6: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:34,964 - INFO - ========== 字幕 #6 的 1 套有效场景方案生成完成 ==========
2025-07-28 22:16:34,964 - INFO - 
----- 处理字幕 #6 的方案 #1 -----
2025-07-28 22:16:34,964 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\6_1.mp4
2025-07-28 22:16:34,964 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpy9_q_yue
2025-07-28 22:16:34,965 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\136.mp4 (确认存在: True)
2025-07-28 22:16:34,965 - INFO - 添加场景ID=136，时长=2.32秒，累计时长=2.32秒
2025-07-28 22:16:34,965 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\139.mp4 (确认存在: True)
2025-07-28 22:16:34,965 - INFO - 添加场景ID=139，时长=1.68秒，累计时长=4.00秒
2025-07-28 22:16:34,965 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\135.mp4 (确认存在: True)
2025-07-28 22:16:34,965 - INFO - 添加场景ID=135，时长=1.12秒，累计时长=5.12秒
2025-07-28 22:16:34,965 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\138.mp4 (确认存在: True)
2025-07-28 22:16:34,965 - INFO - 添加场景ID=138，时长=0.56秒，累计时长=5.68秒
2025-07-28 22:16:34,965 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\137.mp4 (确认存在: True)
2025-07-28 22:16:34,965 - INFO - 添加场景ID=137，时长=0.80秒，累计时长=6.48秒
2025-07-28 22:16:34,965 - INFO - 准备合并 5 个场景文件，总时长约 6.48秒
2025-07-28 22:16:34,965 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/136.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/139.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/135.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/138.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/137.mp4'

2025-07-28 22:16:34,965 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpy9_q_yue\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpy9_q_yue\temp_combined.mp4
2025-07-28 22:16:35,153 - INFO - 合并后的视频时长: 6.60秒，目标音频时长: 5.78秒
2025-07-28 22:16:35,153 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpy9_q_yue\temp_combined.mp4 -ss 0 -to 5.782 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\6_1.mp4
2025-07-28 22:16:35,493 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 22:16:35,493 - INFO - 目标音频时长: 5.78秒
2025-07-28 22:16:35,493 - INFO - 实际视频时长: 5.82秒
2025-07-28 22:16:35,493 - INFO - 时长差异: 0.04秒 (0.71%)
2025-07-28 22:16:35,493 - INFO - ==========================================
2025-07-28 22:16:35,493 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 22:16:35,493 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\6_1.mp4
2025-07-28 22:16:35,494 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpy9_q_yue
2025-07-28 22:16:35,548 - INFO - 方案 #1 处理完成:
2025-07-28 22:16:35,548 - INFO -   - 音频时长: 5.78秒
2025-07-28 22:16:35,548 - INFO -   - 视频时长: 5.82秒
2025-07-28 22:16:35,548 - INFO -   - 时长差异: 0.04秒 (0.71%)
2025-07-28 22:16:35,548 - INFO - 
字幕 #6 处理完成，成功生成 1/1 套方案
2025-07-28 22:16:35,549 - INFO - 生成的视频文件:
2025-07-28 22:16:35,549 - INFO -   1. F:/github/aicut_auto/newcut_ai\6_1.mp4
2025-07-28 22:16:35,549 - INFO - ========== 字幕 #6 处理结束 ==========

