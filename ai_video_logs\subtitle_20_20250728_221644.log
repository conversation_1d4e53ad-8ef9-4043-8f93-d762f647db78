2025-07-28 22:16:44,901 - INFO - ========== 字幕 #20 处理开始 ==========
2025-07-28 22:16:44,901 - INFO - 字幕内容: 但他依旧被蒙在鼓里，坚信妹妹只是中了邪，一切都是真千金这个“扫把星”害的。
2025-07-28 22:16:44,901 - INFO - 字幕序号: [521, 525]
2025-07-28 22:16:44,901 - INFO - 音频文件详情:
2025-07-28 22:16:44,901 - INFO -   - 路径: output\20.wav
2025-07-28 22:16:44,901 - INFO -   - 时长: 4.75秒
2025-07-28 22:16:44,901 - INFO -   - 验证音频时长: 4.75秒
2025-07-28 22:16:44,901 - INFO - 字幕时间戳信息:
2025-07-28 22:16:44,910 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-28 22:16:44,911 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-28 22:16:44,911 - INFO -   - 根据生成的音频时长(4.75秒)已调整字幕时间戳
2025-07-28 22:16:44,911 - INFO - ========== 开始为字幕 #20 生成 6 套场景方案 ==========
2025-07-28 22:16:44,911 - INFO - 开始查找字幕序号 [521, 525] 对应的场景，共有 2710 个场景可选
2025-07-28 22:16:44,911 - INFO - 找到related_overlap场景: scene_id=545, 字幕#521
2025-07-28 22:16:44,911 - INFO - 找到related_overlap场景: scene_id=549, 字幕#525
2025-07-28 22:16:44,912 - INFO - 字幕 #521 找到 1 个overlap场景, 0 个between场景
2025-07-28 22:16:44,912 - INFO - 字幕 #525 找到 1 个overlap场景, 0 个between场景
2025-07-28 22:16:44,912 - INFO - 共收集 2 个未使用的overlap场景和 0 个未使用的between场景
2025-07-28 22:16:44,912 - INFO - 开始生成方案 #1
2025-07-28 22:16:44,912 - INFO - 方案 #1: 为字幕#521选择初始化overlap场景id=545
2025-07-28 22:16:44,912 - INFO - 方案 #1: 为字幕#525选择初始化overlap场景id=549
2025-07-28 22:16:44,912 - INFO - 方案 #1: 初始选择后，当前总时长=5.56秒
2025-07-28 22:16:44,912 - INFO - 方案 #1: 额外between选择后，当前总时长=5.56秒
2025-07-28 22:16:44,912 - INFO - 方案 #1: 场景总时长(5.56秒)大于音频时长(4.75秒)，需要裁剪
2025-07-28 22:16:44,912 - INFO - 调整前总时长: 5.56秒, 目标时长: 4.75秒
2025-07-28 22:16:44,912 - INFO - 需要裁剪 0.81秒
2025-07-28 22:16:44,912 - INFO - 裁剪最长场景ID=549：从2.96秒裁剪至2.15秒
2025-07-28 22:16:44,912 - INFO - 调整后总时长: 4.75秒，与目标时长差异: 0.00秒
2025-07-28 22:16:44,912 - INFO - 方案 #1 调整/填充后最终总时长: 4.75秒
2025-07-28 22:16:44,912 - INFO - 方案 #1 添加到方案列表
2025-07-28 22:16:44,912 - INFO - 开始生成方案 #2
2025-07-28 22:16:44,912 - WARNING - 方案 #2: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:44,912 - INFO - 开始生成方案 #3
2025-07-28 22:16:44,912 - WARNING - 方案 #3: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:44,912 - INFO - 开始生成方案 #4
2025-07-28 22:16:44,912 - WARNING - 方案 #4: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:44,912 - INFO - 开始生成方案 #5
2025-07-28 22:16:44,912 - WARNING - 方案 #5: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:44,912 - INFO - 开始生成方案 #6
2025-07-28 22:16:44,912 - WARNING - 方案 #6: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 22:16:44,912 - INFO - ========== 字幕 #20 的 1 套有效场景方案生成完成 ==========
2025-07-28 22:16:44,913 - INFO - 
----- 处理字幕 #20 的方案 #1 -----
2025-07-28 22:16:44,913 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\20_1.mp4
2025-07-28 22:16:44,913 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpxnmi3lnn
2025-07-28 22:16:44,913 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\545.mp4 (确认存在: True)
2025-07-28 22:16:44,913 - INFO - 添加场景ID=545，时长=2.60秒，累计时长=2.60秒
2025-07-28 22:16:44,914 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\549.mp4 (确认存在: True)
2025-07-28 22:16:44,914 - INFO - 添加场景ID=549，时长=2.96秒，累计时长=5.56秒
2025-07-28 22:16:44,914 - INFO - 准备合并 2 个场景文件，总时长约 5.56秒
2025-07-28 22:16:44,914 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/545.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/549.mp4'

2025-07-28 22:16:44,914 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpxnmi3lnn\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpxnmi3lnn\temp_combined.mp4
2025-07-28 22:16:45,057 - INFO - 合并后的视频时长: 5.61秒，目标音频时长: 4.75秒
2025-07-28 22:16:45,057 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpxnmi3lnn\temp_combined.mp4 -ss 0 -to 4.745 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\20_1.mp4
2025-07-28 22:16:45,386 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 22:16:45,386 - INFO - 目标音频时长: 4.75秒
2025-07-28 22:16:45,386 - INFO - 实际视频时长: 4.78秒
2025-07-28 22:16:45,386 - INFO - 时长差异: 0.04秒 (0.80%)
2025-07-28 22:16:45,386 - INFO - ==========================================
2025-07-28 22:16:45,386 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 22:16:45,386 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\20_1.mp4
2025-07-28 22:16:45,387 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpxnmi3lnn
2025-07-28 22:16:45,435 - INFO - 方案 #1 处理完成:
2025-07-28 22:16:45,435 - INFO -   - 音频时长: 4.75秒
2025-07-28 22:16:45,435 - INFO -   - 视频时长: 4.78秒
2025-07-28 22:16:45,435 - INFO -   - 时长差异: 0.04秒 (0.80%)
2025-07-28 22:16:45,435 - INFO - 
字幕 #20 处理完成，成功生成 1/1 套方案
2025-07-28 22:16:45,435 - INFO - 生成的视频文件:
2025-07-28 22:16:45,435 - INFO -   1. F:/github/aicut_auto/newcut_ai\20_1.mp4
2025-07-28 22:16:45,435 - INFO - ========== 字幕 #20 处理结束 ==========

